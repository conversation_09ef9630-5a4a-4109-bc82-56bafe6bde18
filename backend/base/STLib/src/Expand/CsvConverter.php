<?php

namespace STLib\Expand;

trait CsvConverter {

    /**
     * @param array|\Iterator $data
     * @param array|null $columns
     * @param callable|null $mapper
     * @return string|null
     * @todo Implement \Iterator
     *
     */
    protected function convertArrayToCsv(array|\Iterator $data, ?array $columns = null, ?callable $mapper = null): ?string {
        if (count($data) === 0) {
            return '';
        }
        $columns ??= array_keys(current($data));
        if (is_callable($mapper)) {
            array_walk($columns, $mapper);
        }
        array_unshift($data, $columns);
        
        $tmpCsvFile = tmpfile();
        $tmpCsvFilePath = stream_get_meta_data($tmpCsvFile)['uri'];
        
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->fromArray($data, null, 'A1', true);
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Csv($spreadsheet);
        $writer->save($tmpCsvFilePath);
        return file_get_contents($tmpCsvFilePath);
    }
    
}
