<?php

namespace STLib\Expand;

trait TsvConverter {
    
    /**
     * 
     * @param string $tsv
     * @param array $mapper
     * @param bool $ignoreFirstLine
     * @param bool $ignoreLastLine
     * @return array|null
     */
    protected function convertTsvToArray(string $tsv, array $mapper = [], bool $ignoreFirstLine = false, bool $ignoreLastLine = false): ?array {
        if (empty($tsv)) {
            return null;
        }
        $result = [];
        $rows = explode("\n", trim($tsv));
        if ($ignoreFirstLine) {
            array_shift($rows);
        }
        if ($ignoreLastLine) {
            array_pop($rows);
        }
        $keys = explode("\t", array_shift($rows));
        array_walk($keys, function(&$key) use ($mapper) {
            if (isset($mapper[$key])) {
                $key = $mapper[$key];
            }
        });
        foreach ($rows as $row) {
            if (!empty($row)) {
                $result[] = array_combine($keys, explode("\t", $row));
            }
        }
        return $result;
    }
    
}
