<?php

namespace STLib\Expand;

trait AttributeFill {

    /**
     *
     * @return array
     */
    abstract public function getAttributeNames(): array;

    /**
     * Sets the model attributes.
     * @param array $data
     * @return $this
     * @throws \RuntimeException
     */
    public function setAttributes(array $data) {
        foreach ($this->getAttributeNames() as $attributeName) {
            if (array_key_exists($attributeName, $data)) {
                $setter = 'set' . ucfirst($attributeName);
                if (method_exists($this, $setter)) {
                    $propertyComment = (new \ReflectionClass($this))->getMethod($setter)->getDocComment();
                    $dataType = preg_match('/@param\s+([^\s]+)/', $propertyComment, $matches) ? $matches[1] : null;
                    if (!empty($dataType)) {
                        settype($data[$attributeName], $dataType);
                    }
                    $this->$setter($data[$attributeName]);
                } else {
                    throw new \RuntimeException('Unknown method "' . $setter . '" in class ' . __CLASS__ . '.');
                }
            }
        }

        return $this;
    }

}
