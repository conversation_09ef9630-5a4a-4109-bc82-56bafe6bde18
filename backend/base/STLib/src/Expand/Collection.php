<?php

namespace STLib\Expand;

class Collection implements \Iterator, \Countable, \ArrayAccess {

    /**
     *
     * @var array
     */
    protected array $list = [];
    
    /**
     * 
     * @param array $list
     */
    public function __construct(array $list = []) {
        $this->list = $list;
    }

    /**
     * 
     * @param mixed $object
     * @param int|string|null $key
     * @return Collection
     */
    public function add(mixed $object, int|string|null $key = null): Collection {
        if (!is_null($key)) {
            $this->list[$key] = $object;
        } else {
            $this->list[] = $object;
        }
        return $this;
    }

    /**
     * 
     * @param mixed $needle
     * @return bool
     */
    public function remove(mixed $needle): bool {
        foreach ($this->list as $key => $object) {
            if ($needle === $object) {
                unset($this->list[$key]);
                return true;
            }
        }

        return false;
    }

    /**
     * 
     * @param int|string $index
     * @return bool
     */
    public function removeByKey(int|string $index): bool {
        if (array_key_exists($index, $this->list)) {
            unset($this->list[$index]);
            return true;
        }

        return false;
    }

    /**
     * 
     * @param mixed $object
     * @return bool
     */
    public function isFirst(mixed $object): bool {
        return current($this->list) === $object;
    }

    /**
     * 
     * @param mixed $object
     * @return bool
     */
    public function isLast(mixed $object): bool {
        return end($this->list) === $object;
    }
    
    /**
     * 
     * @return mixed
     */
    public function first(): mixed {
        $this->rewind();
        return current($this->list);
    }
    
    /**
     * 
     * @return mixed
     */
    public function last(): mixed {
        return end($this->list);
    }

    /**
     * 
     * @return bool
     */
    public function clear(): bool {
        $this->list = [];
        return true;
    }

    /**
     * 
     * @param mixed $object
     * @return bool
     */
    public function exists(mixed $object): bool {
        return in_array($object, $this->list, true);
    }

    /**
     * 
     * @param string|int $index
     * @return bool
     */
    public function keyExists(string|int $index): bool {
        return array_key_exists($index, $this->list);
    }

    /**
     *
     * @return array
     */
    public function toArray(): array {
        return $this->list;
    }

    /**
     * 
     * @param callable $callback
     * @return array
     */
    public function apply(callable $callback): array {
        array_walk($this->list, $callback);
        return $this->list;
    }
    
    /**
     * 
     * @param callable $callback
     * @return array
     */
    public function usort(callable $callback): array {
        usort($this->list, $callback);
        return $this->list;
    }
    
    /**
     * 
     * @return array
     */
    public function ksort(): array {
        ksort($this->list);
        return $this->list;
    }
    
    /**
     * 
     * @param callable $callback
     * @return array
     */
    public function filter(callable $callback): array {
        $this->list = array_filter($this->list, $callback);
        return $this->list;
    }
    
    /**
     * 
     * @param callable $callback
     * @return mixed
     */
    public function reduce(callable $callback): mixed {
        return array_reduce($this->list, $callback);
    }
    
    /**
     * 
     * @param callable $callback
     * @return Collection
     */
    public function slice(callable $callback): Collection {
        $sliced = clone $this;
        $sliced->filter($callback);
        return $sliced;
    }

    /**
     * (non-PHPdoc)
     *
     * @see Iterator::current()
     */
    public function current(): mixed {
        return current($this->list);
    }

    /**
     * (non-PHPdoc)
     *
     * @see \Iterator::key()
     */
    public function key(): mixed {
        return key($this->list);
    }

    /**
     * (non-PHPdoc)
     *
     * @see \Iterator::next()
     */
    public function next(): void {
        next($this->list);
    }

    /**
     * (non-PHPdoc)
     *
     * @see \Iterator::rewind()
     */
    public function rewind(): void {
        reset($this->list);
    }

    /**
     * (non-PHPdoc)
     *
     * @see \Iterator::valid()
     */
    public function valid(): bool {
        return !is_null(key($this->list)) && array_key_exists(key($this->list), $this->list);
    }

    /**
     * (non-PHPdoc)
     *
     * @see \Countable::count()
     */
    public function count(): int {
        return count($this->list);
    }
    
    /**
     * 
     * @return bool
     */
    public function isEmpty(): bool {
        return count($this->list) === 0;
    }
    
    /**
     * (non-PHPdoc)
     *
     * @see \ArrayAccess::offsetExists()
     */
    public function offsetExists(mixed $index): bool {
        return $this->keyExists($index);
    }

    /**
     * (non-PHPdoc)
     *
     * @see \ArrayAccess::offsetGet()
     */
    public function offsetGet(mixed $index): mixed {
        if ($this->offsetExists($index)) {
            return $this->list[$index];
        }
        return null;
    }

    /**
     * (non-PHPdoc)
     *
     * @see \ArrayAccess::offsetSet()
     */
    public function offsetSet(mixed $index, mixed $object): void {
        $this->list[$index] = $object;
    }

    /**
     * (non-PHPdoc)
     *
     * @see \ArrayAccess::offsetUnset()
     */
    public function offsetUnset(mixed $index): void {
        if ($this->offsetExists($index)) {
            unset($this->list[$index]);
        }
    }
    
    /**
     * 
     * @return array
     */
    public function keys(): array {
        return array_keys($this->list);
    }
    
    /**
     * 
     * @return mixed
     */
    public function rand(): mixed {
        return $this->list[array_rand($this->list)];
    }
    
    /**
     * 
     * @param int $size
     * @return array
     */
    public function chunk(int $size): array {
        return array_chunk($this->list, $size);
    }
    
    /**
     * 
     * @param mixed $object
     * @return int|string|false
     */
    public function search(mixed $object): int|string|false {
        return array_search($object, $this->list);
    }

}
