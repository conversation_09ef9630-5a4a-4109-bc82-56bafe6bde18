<?php

namespace STLib\Mvc\Command;

use Interop\Container\ContainerInterface;
use Laminas\ServiceManager\Factory\FactoryInterface;

class CommandFactory implements FactoryInterface {

    /**
     * 
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return AbstractCommand
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): AbstractCommand {
        $config = $container->get('config');
        $command = (null === $options) ? new $requestedName : new $requestedName($options);
        $command->getPluginManager()->configure($config['controller_plugins']);
        return $command->setServiceManager($container);
    }

}
