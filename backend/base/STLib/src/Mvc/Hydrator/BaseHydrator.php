<?php

namespace STLib\Mvc\Hydrator;

class BaseHydrator extends \Laminas\Hydrator\ClassMethodsHydrator
{

    /**
     * Holds the names of the methods used for hydration, indexed by class::property name,
     * false if the hydration method is not callable/usable for hydration purposes
     *
     * @var string[]|bool[]
     */
    private array $hydrationMethodsCache = [];
    
    /**
     * Hydrate an object by populating getter/setter methods
     *
     * Hydrates an object by getter/setter methods of the object.
     *
     * {@inheritDoc}
     */
    public function hydrate(array $data, object $object): object {
        $objectClass = get_class($object);
        
        $objectClassShortName = (new \ReflectionClass($object))->getShortName();
        $camelCaseToUnderscoreFilter = new \Laminas\Filter\Word\CamelCaseToUnderscore();
        $objectUnderscoreClassName = strtolower($camelCaseToUnderscoreFilter->filter($objectClassShortName));

        // TODO: change this poor algorithm
        foreach ($data as $property => $value) {
            $this->tryToHydrate($property, $value, $data, $objectClass, $object);
            $alternativeProperty = preg_replace('/^' . $objectUnderscoreClassName . '_/', '', $property);
            $this->tryToHydrate($alternativeProperty, $value, $data, $objectClass, $object);
        }

        return $object;
    }
    
    /**
     * 
     * @param string $property
     * @param mixed $value
     * @param array $data
     * @param string $objectClass
     * @param object $object
     */
    protected function tryToHydrate(string $property, $value, array $data, string $objectClass, object $object) {
        $propertyFqn = $objectClass . '::$' . $property;

        if (!isset($this->hydrationMethodsCache[$propertyFqn])) {
            $setterName = 'set' . ucfirst($this->hydrateName($property, $data));
            if (is_callable([$object, $setterName])
                && (! $this->methodExistsCheck || method_exists($object, $setterName))) {
                $this->hydrationMethodsCache[$propertyFqn] = $setterName;
            }

            $setterName = ucfirst($this->hydrateName($property, $data));
            if (is_callable([$object, $setterName])
                && (! $this->methodExistsCheck || method_exists($object, $setterName))) {
                $this->hydrationMethodsCache[$propertyFqn] = $setterName;
            }
        }

        if (isset($this->hydrationMethodsCache) && isset($this->hydrationMethodsCache[$propertyFqn])) {
            $object->{$this->hydrationMethodsCache[$propertyFqn]}($this->hydrateValue($property, $value, $data));
        }
    }
    
}
