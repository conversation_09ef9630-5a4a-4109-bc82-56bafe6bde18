<?php

namespace STLib\Mvc\Hydrator;

use ReflectionException;

trait BaseHydratorTrait {
    
    /**
     *
     * @var \STLib\Mvc\Hydrator\BaseHydrator
     */
    protected ?\STLib\Mvc\Hydrator\BaseHydrator $baseHydrator = null;

    /**
     *
     * @param array $data
     * @param string $class
     * @param bool $withConstructor
     * @return object
     * @throws ReflectionException
     */
    protected function hydrate(array $data, string $class, bool $withConstructor = false): object {
        $reflectionClass = (new \ReflectionClass($class));
        $object = $withConstructor ? $reflectionClass->newInstance() : $reflectionClass->newInstanceWithoutConstructor();
        return $this->getBaseHydrator()->hydrate(
            $data,
            $object
        );
    }
    
    /**
     * 
     * @param object $object
     * @return array
     */
    protected function extract(object $object): array {
        return $this->getBaseHydrator()->extract($object);
    }
    
    /**
     * 
     * @return \STLib\Mvc\Hydrator\BaseHydrator
     */
    private function getBaseHydrator(): BaseHydrator {
        if (is_null($this->baseHydrator)) {
            $this->baseHydrator = new BaseHydrator();
        }
        return $this->baseHydrator;
    }
    
}
