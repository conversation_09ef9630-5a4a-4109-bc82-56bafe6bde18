<?php

namespace STLib\Mvc\Hydrator;

class MappingHydrator extends BaseHydrator
{
    
    /**
     * 
     * @param array $data
     * @param object $object
     * @param array $mapping
     * @return object
     */
    public function hydrate(array $data, object $object, array $mapping = []): object {
        return parent::hydrate($this->mapper($data, $mapping), $object);
    }
    
    /**
     * 
     * @param object $object
     * @param array $mapping
     * @return array
     */
    public function extract(object $object, array $mapping = []): array {
        $data = parent::extract($data, $object);
        return $this->mapper($data, $mapping);
    }
    
    /**
     * 
     * @param array $data
     * @param array $mapping
     * @return array
     */
    protected function mapper(array $data, array $mapping): array {
        $result = [];
        foreach ($data as $key => $value) {
            $result[isset($mapping[$key]) !== false ? $mapping[$key] : $key] = $value;
        }
        return $result;
    }

}
