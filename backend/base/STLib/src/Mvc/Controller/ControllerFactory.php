<?php

namespace STLib\Mvc\Controller;

use Interop\Container\ContainerInterface;
use Laminas\ServiceManager\Factory\FactoryInterface;

class ControllerFactory implements FactoryInterface {

    /**
     * 
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return AbstractController
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): AbstractController {
        $controller = (null === $options) ? new $requestedName : new $requestedName($options);
        return $controller->setServiceManager($container);
    }

}
