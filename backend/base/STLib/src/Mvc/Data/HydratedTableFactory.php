<?php

namespace STLib\Mvc\Data;

use Interop\Container\ContainerInterface;
use InvalidArgumentException;
use <PERSON><PERSON>\Db\Adapter\Adapter;
use <PERSON>inas\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STLib\Db\AbstractTable;
use STLib\Db\HydratedAbstractTable;
use STLib\Db\TableGateway\TableGateway;

class HydratedTableFactory implements FactoryInterface
{
    /**
     *
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return AbstractTable
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): AbstractTable
    {
        if (!is_a($requestedName, HydratedAbstractTable::class, true)) {
            throw new InvalidArgumentException('Table class must be an instance of ' . HydratedAbstractTable::class);
        }

        $tableName = $requestedName::getTableName();
        $entityName = $requestedName::getEntityName();
        $collectionName = $requestedName::getCollectionName();
        $entity = new $entityName();

        $dbAdapter = $container->get(Adapter::class);

        $resultSetPrototype = new CollectionResultSet();
        $resultSetPrototype->setArrayObjectPrototype($entity);
        $resultSetPrototype->setCollectionClassName($collectionName);

        $gateway = new TableGateway($tableName, $dbAdapter, resultSetPrototype: $resultSetPrototype);

        return new $requestedName($gateway);
    }
}
