<?php

declare(strict_types=1);

namespace STLib\Db;

use STLib\Mvc\Hydrator\BaseHydrator;

abstract class Entity
{
    public function __construct(
        readonly protected BaseHydrator $hydrator = new BaseHydrator(),
    ) {
    }

    abstract public function getId(): ?int;

    public function exchangeArray($data): void
    {
        $this->init();
        $this->hydrator->hydrate($data, $this);
    }

    public function init(): void
    {
    }

    protected function exchangeJson(array $data, string $key): array
    {
        $exchangeData = [];
        if (!is_null($data[$key])) {
            $exchangeData = json_decode($data[$key], true);
        }

        return $exchangeData;
    }
}
