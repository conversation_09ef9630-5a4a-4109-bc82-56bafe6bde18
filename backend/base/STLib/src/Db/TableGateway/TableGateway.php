<?php

namespace STLib\Db\TableGateway;

/**
 * @property string $lastInsertValue
 */
class TableGateway extends \Laminas\Db\TableGateway\TableGateway {
    
    /**
     * 
     * @param string|\Laminas\Db\Sql\TableIdentifier|array $table
     * @return \Laminas\Db\TableGateway\TableGateway
     */
    public function setTable(string|\Laminas\Db\Sql\TableIdentifier|array $table): \Laminas\Db\TableGateway\TableGateway {
        $this->table = $table;
        return $this;
    }
    
}
