<?php

namespace STLib;

class Module
{
    
    /**
     * 
     * @return array
     */
    public function getAutoloaderConfig(): array {
        return [
            \Laminas\Loader\StandardAutoloader::class => [
                'namespaces' => [
                    __NAMESPACE__ => __DIR__ . '/src/',
                ],
            ],
        ];
    }
    
    /**
     *
     * @param \Laminas\ModuleManager\ModuleManager $moduleManager
     * @return void
     */
    public function init(\Laminas\ModuleManager\ModuleManager $moduleManager): void
    {
        $events = $moduleManager->getEventManager();
        $events->attach(\Laminas\ModuleManager\ModuleEvent::EVENT_LOAD_MODULES_POST, function (\Laminas\ModuleManager\ModuleEvent $moduleEvent) {
            $serviceManager = $moduleEvent->getParam('ServiceManager');
            $adapter = $serviceManager->get(\Laminas\Db\Adapter\Adapter::class);
            $connection = $adapter->driver->getConnection();
            Db\Connection::setConnection($connection);
        });
    }
    
}
