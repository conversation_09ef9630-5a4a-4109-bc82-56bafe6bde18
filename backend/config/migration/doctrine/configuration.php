<?php

return [
    'table_storage' => [
        'table_name' => 'migrations',
        'version_column_name' => 'version',
        'version_column_length' => 511,
        'executed_at_column_name' => 'executed_at',
        'execution_time_column_name' => 'execution_time',
    ],
    'migrations_paths' => [
        'Migrations' => getcwd() . '/data/migrations/mysql',
    ],
    'all_or_nothing' => true,
    'check_database_platform' => true,
    'organize_migrations' => 'none',
    'custom_template' => getcwd() . '/config/migration/doctrine/template.php',
];
