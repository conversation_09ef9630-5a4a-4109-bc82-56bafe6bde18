<?php

declare(strict_types=1);

return [
    'db' => [
        'driver' => 'pdo',
        'dsn' => 'mysql:dbname=' . getenv('MYSQL_DATABASE') .';host=' . getenv('MYSQL_HOST') .';port=' . getenv('MYSQL_PORT') ?: '3306' . ';charset=utf8mb4;',
        'driver_options' => [
            //PDO::MYSQL_ATTR_INIT_COMMAND => "",
        ],
        'username' => getenv('MYSQL_USER'),
        'password' => getenv('MYSQL_PASS'),
    ],
];
