<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         displayDetailsOnTestsThatTriggerDeprecations="true"
         displayDetailsOnTestsThatTriggerErrors="true"
         displayDetailsOnTestsThatTriggerNotices="true"
         displayDetailsOnTestsThatTriggerWarnings="true"
         displayDetailsOnPhpunitDeprecations="true"
>
    <php>
        <env name="APP_ENV" value="test" force="true" />
        <env name="APP_DEBUG" value="no" force="true" />
        <env name="MYSQL_PASS" value="" force="true" />
        <env name="REDIS_PASS" value="" force="true" />
        <env name="CLICKHOUSE_PASS" value="" force="true" />
        <env name="ALGO_API_KEY" value="some-test-key" force="true" />
    </php>
</phpunit>
