<?php

declare(strict_types=1);

namespace tests\Unit\base\STLib\Validator\Check;

use PHPUnit\Framework\TestCase;
use STLib\Validator\Check\AiInput;

final class AiInputTest extends TestCase
{
    private AiInput $validator;

    protected function setUp(): void
    {
        $this->validator = new AiInput();
    }

    public function testValidInputPasses(): void
    {
        $validInputs = [
            'This is a valid input with enough characters and letters.',
            'Hello world! This text contains 12 chars and 6 letters.',
            'Testing with numbers 123 and punctuation: - _ . , ! ? ; : " \' —',
            'Multi-line text
with line breaks
should work fine.',
            'Text with em dash — and various punctuation marks.',
        ];

        foreach ($validInputs as $input) {
            $this->assertTrue(
                $this->validator->isValid($input),
                "Input should be valid: '$input'. Errors: " . implode(', ', $this->validator->getMessages())
            );
        }
    }

    public function testTooShortInputFails(): void
    {
        $shortInputs = [
            'Short',
            'Too short',
            '12345678901', // exactly 11 characters
        ];

        foreach ($shortInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (too short): '$input'"
            );
        }

        dd($this->validator->getMessages());
        $this->assertArrayHasKey(AiInput::TOO_SHORT, $this->validator->getMessages());
    }

    public function testDisallowedCharactersFail(): void
    {
        $invalidInputs = [
            'Text with emoji 😀 should fail',
            'Text with @ symbol should fail',
            'Text with # hashtag should fail',
            'Text with $ dollar sign should fail',
            'Text with % percent should fail',
            'Text with & ampersand should fail',
            'Text with * asterisk should fail',
            'Text with + plus should fail',
            'Text with = equals should fail',
            'Text with [] brackets should fail',
            'Text with {} braces should fail',
            'Text with () parentheses should fail',
            'Text with | pipe should fail',
            'Text with \\ backslash should fail',
            'Text with / forward slash should fail',
            'Text with < less than should fail',
            'Text with > greater than should fail',
            'Text with ^ caret should fail',
            'Text with ~ tilde should fail',
            'Text with ` backtick should fail',
        ];

        foreach ($invalidInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (disallowed characters): '$input'"
            );
            $this->assertArrayHasKey(AiInput::DISALLOWED_CHARACTERS, $this->validator->getMessages());
        }
    }

    public function testConsecutiveWhitespaceFails(): void
    {
        $invalidInputs = [
            'Text with  double spaces should fail',
            'Text with   triple spaces should fail',
            'Text with    multiple spaces should fail',
            "Text with\n\ndouble line breaks should fail",
            "Text with\n\n\ntriple line breaks should fail",
            'Text with multiple  spaces  in  different  places should fail',
        ];

        foreach ($invalidInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (consecutive whitespace): '$input'"
            );
            $this->assertArrayHasKey(AiInput::CONSECUTIVE_WHITESPACE, $this->validator->getMessages());
        }
    }

    public function testMeaninglessContentFails(): void
    {
        $meaninglessInputs = [
            '.,!?;:"\'— .,!?;:"\'— .,!?;:"\'—', // only punctuation
            '                    ', // only spaces (20 spaces)
            '.,!?;:"\'— .,!?;:"\'—', // punctuation with spaces but no letters/numbers
            '12345 .,!?;:"\'—', // numbers and punctuation but less than 6 letters
            'a b c d e', // exactly 5 letters, should fail
            '.,!?;:"\'— a b c d e .,!?;:"\'—', // 5 letters with punctuation
        ];

        foreach ($meaninglessInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (meaningless content): '$input'"
            );
            $this->assertArrayHasKey(AiInput::MEANINGLESS_CONTENT, $this->validator->getMessages());
        }
    }

    public function testMinimumLetterRequirement(): void
    {
        // Test inputs with exactly 6 letters (should pass)
        $validInputs = [
            'a b c d e f .,!?;:"\'— .,!?;:"\'—', // exactly 6 letters
            '123456 abcdef .,!?;:"\'—', // 6 letters with numbers and punctuation
        ];

        foreach ($validInputs as $input) {
            $this->assertTrue(
                $this->validator->isValid($input),
                "Input should be valid (6 or more letters): '$input'. Errors: " . implode(', ', $this->validator->getMessages())
            );
        }

        // Test inputs with less than 6 letters (should fail)
        $invalidInputs = [
            '12345 abcde .,!?;:"\'—', // exactly 5 letters
            '123456789 abc .,!?;:"\'—', // 3 letters
        ];

        foreach ($invalidInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Input should be invalid (less than 6 letters): '$input'"
            );
            $this->assertArrayHasKey(AiInput::MEANINGLESS_CONTENT, $this->validator->getMessages());
        }
    }

    public function testNonStringInputsAreConverted(): void
    {
        // Test that non-string inputs are converted to strings
        $this->assertFalse($this->validator->isValid(123)); // too short after conversion
        $this->assertFalse($this->validator->isValid(null)); // empty after conversion
        $this->assertFalse($this->validator->isValid(false)); // empty after conversion
        
        // Test a number that would be valid if it were a string
        $longNumber = 123456789012345; // 15 digits, but no letters
        $this->assertFalse($this->validator->isValid($longNumber)); // should fail for meaningless content
    }

    public function testEmptyAndWhitespaceOnlyInputs(): void
    {
        $emptyInputs = [
            '',
            '   ',
            "\n",
            "\n\n",
            "   \n   \n   ",
        ];

        foreach ($emptyInputs as $input) {
            $this->assertFalse(
                $this->validator->isValid($input),
                "Empty/whitespace input should be invalid: '$input'"
            );
            // Could be either TOO_SHORT or MEANINGLESS_CONTENT depending on the input
            $messages = $this->validator->getMessages();
            $this->assertTrue(
                isset($messages[AiInput::TOO_SHORT]) || isset($messages[AiInput::MEANINGLESS_CONTENT]),
                "Should have either TOO_SHORT or MEANINGLESS_CONTENT error"
            );
        }
    }
}
