<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Entity\Company;
use STCompany\Entity\Team;
use STCompany\Entity\User;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreation\UserBuilder;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreation\UserInviter;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreator;
use STCompany\Service\TeamService;
use STCompany\Service\UserService;
use tests\TestCase;
use tests\WithConsecutive;

final class UsersCreatorTest extends TestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateUsers(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $inviteLink = $this->faker->url();

        $name1 = $this->faker->firstName() . ' ' . $this->faker->lastName();
        $name2 = $this->faker->lastName() . ' ' . $this->faker->firstName();
        $name3 = $this->faker->lastName() . ' ' . $this->faker->firstName();

        $email1 = $this->faker->email();
        $email2 = $this->faker->email();
        $email3 = $this->faker->email();

        $users = [
            [
                'name' => $name1,
                'email' => $email1,
            ],
            [
                'name' => $name2,
                'email' => $email2,
            ],
            [
                'name' => $name3,
                'email' => $email3,
            ],
        ];

        $team = $this->createMock(Team::class);

        $userBuilder = $this->createMock(UserBuilder::class);
        $userBuilder
            ->method('buildTeam')
            ->with($company)
            ->willReturn($team);

        $teamService = $this->createMock(TeamService::class);
        $teamService
            ->expects($this->once())
            ->method('saveTeam')
            ->with($team);

        $user1 = $this->createMock(User::class);
        $user1
            ->method('isFirstLogin')
            ->willReturn(true);
        $user2 = $this->createMock(User::class);
        $user2
            ->method('isFirstLogin')
            ->willReturn(false);
        $user3 = $this->createMock(User::class);
        $user3
            ->method('isFirstLogin')
            ->willReturn(true);

        $buildUserMap = [
            [$name1, $email1, $company, $team, $user1],
            [$name2, $email2, $company, $team, $user2],
            [$name3, $email3, $company, $team, $user3],
        ];
        $userBuilder
            ->method('buildUser')
            ->willReturnMap($buildUserMap);

        $userService = $this->createMock(UserService::class);
        $userService
            ->expects($this->exactly(3))
            ->method('saveUser')
            ->with(
                ...
                WithConsecutive::create(
                    [$user1, $companyId],
                    [$user2, $companyId],
                    [$user3, $companyId],
                )
            );

        $userInviter = $this->createMock(UserInviter::class);
        $userInviter
            ->expects($this->exactly(2))
            ->method('inviteNewUser')
            ->with(
                ...
                WithConsecutive::create(
                    [$user1, $company, $inviteLink, $name1],
                    [$user3, $company, $inviteLink, $name1],
                )
            );;
        $userInviter
            ->expects($this->once())
            ->method('inviteExistedUser')
            ->with($user2, $company, $name1);

        $creator = new UsersCreator($userBuilder, $teamService, $userService, $userInviter);
        $creator->createUsers($company, $users, $inviteLink);
    }
}
