<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding\EventsCreation;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Entity\LlmEvent\LlmEvent;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\LlmEventsCreator;
use STCompany\Service\Interfaces\LlmEventSaverInterface;
use STCompany\Service\Interfaces\LlmEventSelectorInterface;
use tests\TestCase;
use tests\WithConsecutive;

final class LlmEventsCreatorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCreateLlmEvents(): void
    {
        $existedLlmEventId = $this->faker->numberBetween(101, 200);
        $deletedLlmEventId = $this->faker->numberBetween(201, 300);

        $existedLlmEvent = $this->createMock(LlmEvent::class);
        $newLlmEvent = $this->createMock(LlmEvent::class);
        $newDeletedLlmEvent = $this->createMock(LlmEvent::class);

        $existedLlmEventName = $this->faker->word();
        $newLlmEventName = $this->faker->sentence(2);
        $newDeletedLlmEventName = $this->faker->text(10);

        $existedLlmEventData = [
            'id' => $existedLlmEventId,
            'name' => $existedLlmEventName . " \n",
            'description' => $this->faker->text(),
        ];
        $newLlmEventData = [
            'name' => " \n" . $newLlmEventName,
            'description' => $this->faker->sentence(),
        ];
        $newDeletedLlmEventData = [
            'id' => $deletedLlmEventId,
            'name' => $newDeletedLlmEventName,
            'description' => $this->faker->text() . " \n",
        ];

        $events = [
            $existedLlmEventData,
            $newLlmEventData,
            $newDeletedLlmEventData,
        ];

        $llmEventsSelector = $this->createMock(LlmEventSelectorInterface::class);
        $llmEventsSelector
            ->method('getLlmEvent')
            ->with(
                ...
                WithConsecutive::create(
                    [$existedLlmEventId],
                    [$deletedLlmEventId]
                )
            )
            ->willReturnOnConsecutiveCalls(
                $existedLlmEvent,
                $this->throwException(new NotFoundApiException())
            );

        $llmEventSaverMap = [
            [$newLlmEventName, $newLlmEventData['description'], null, $newLlmEvent],
            [$newDeletedLlmEventName, $newDeletedLlmEventData['description'], null, $newDeletedLlmEvent],
        ];
        $llmEventSaver = $this->createMock(LlmEventSaverInterface::class);
        $llmEventSaver
            ->expects($this->exactly(2))
            ->method('save')
            ->willReturnMap($llmEventSaverMap);

        $creator = new LlmEventsCreator($llmEventsSelector, $llmEventSaver);
        $llmEventCollection = $creator->createLlmEvents($events);

        $this->assertSame(3, $llmEventCollection->count());
        $this->assertSame($existedLlmEvent, $llmEventCollection->offsetGet(0));
        $this->assertSame($newLlmEvent, $llmEventCollection->offsetGet(1));
        $this->assertSame($newDeletedLlmEvent, $llmEventCollection->offsetGet(2));
    }
}
