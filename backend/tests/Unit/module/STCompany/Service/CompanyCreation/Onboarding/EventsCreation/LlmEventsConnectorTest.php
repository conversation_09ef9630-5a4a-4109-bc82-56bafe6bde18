<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding\EventsCreation;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Entity\Company;
use STCompany\Entity\LlmEvent\LlmEventCollection as CompanyLlmEventCollection;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\LlmEventsConnector;
use STLlmEvent\Entity\LlmEvent;
use STLlmEvent\Entity\LlmEventCollection;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use tests\TestCase;

final class LlmEventsConnectorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testConnectEvents(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $llmEventId1 = $this->faker->numberBetween(1, 100);
        $llmEventId2 = $this->faker->numberBetween(101, 200);

        $llmEvent1 = $this->createMock(LlmEvent::class);
        $llmEvent2 = $this->createMock(LlmEvent::class);

        $llmEvent1->method('getId')->willReturn($llmEventId1);
        $llmEvent2->method('getId')->willReturn($llmEventId2);

        $llmEventCollection = new LlmEventCollection();
        $llmEventCollection->add($llmEvent1);
        $llmEventCollection->add($llmEvent2);

        $companyLlmEvent1 = new CompanyLlmEvent();
        $companyLlmEvent1->setId($llmEventId1);
        $companyLlmEvent1->setCompanyId($companyId);

        $companyLlmEvent2 = new CompanyLlmEvent();
        $companyLlmEvent2->setId($llmEventId2);
        $companyLlmEvent2->setCompanyId($companyId);

        $companyLlmEventCollection = new CompanyLlmEventCollection();
        $companyLlmEventCollection->add($companyLlmEvent1);
        $companyLlmEventCollection->add($companyLlmEvent2);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->expects($this->once())
            ->method('saveEvents')
            ->with($companyLlmEventCollection);

        $connector = new LlmEventsConnector($companyLlmEventsTable);
        $connector->connectEvents($company, $llmEventCollection);
    }
}
