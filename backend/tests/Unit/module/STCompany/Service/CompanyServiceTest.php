<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service;

use PHPUnit\Framework\MockObject\Exception;
use STCall\Data\CallsTable;
use STCompany\Data\ClientsTable;
use STCompany\Data\CompaniesCallTemplatesTable;
use STCompany\Data\CompaniesLanguagesTable;
use STCompany\Data\CompaniesRatesTable;
use STCompany\Data\CompaniesTable;
use STCompany\Data\CompanyDetailsTable;
use STCompany\Data\TeamsTable;
use STCompany\Data\UsersCompaniesLanguagesTable;
use STCompany\Service\CompanyService;
use STUser\Data\UsersTable;
use tests\TestCase;

final class CompanyServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testUpdateSettings(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $settings = [$this->faker->word() => $this->faker->word()];

        $companiesTable = $this->createMock(CompaniesTable::class);
        $companiesTable
            ->expects($this->once())
            ->method('partialUpdateCompany')
            ->with($companyId, $settings);

        $service = new CompanyService(
            $companiesTable,
            $this->createMock(CompaniesCallTemplatesTable::class),
            $this->createMock(CompaniesRatesTable::class),
            $this->createMock(UsersTable::class),
            $this->createMock(CompaniesLanguagesTable::class),
            $this->createMock(UsersCompaniesLanguagesTable::class),
            $this->createMock(TeamsTable::class),
            $this->createMock(CallsTable::class),
            $this->createMock(ClientsTable::class),
            $this->createMock(CompanyDetailsTable::class),
            ['api' => [], 'env' => '']
        );

        $service->updateSettings($companyId, $settings);
    }
}
