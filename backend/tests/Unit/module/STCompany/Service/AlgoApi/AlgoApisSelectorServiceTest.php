<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\AlgoApi;

use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Data\CompaniesAlgoApisTable;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\AlgoApi\AlgoApi;
use STCompany\Service\AlgoApi\AlgoApisSelectorService;
use STLib\Mvc\Hydrator\Hydrator;
use tests\TestCase;

final class AlgoApisSelectorServiceTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function testGetAlgoApi(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $algoApiId = $this->faker->numberBetween(101, 200);

        $algoApi = $this->createMock(AlgoApi::class);

        $algoApiData = ['key' => 'value'];

        $resultSet = new ResultSet();
        $resultSet->initialize([$algoApiData]);

        $companiesAlgoApisTable = $this->createMock(CompaniesAlgoApisTable::class);
        $companiesAlgoApisTable
            ->method('getAlgoApi')
            ->with($algoApiId, $companyId)
            ->willReturn($resultSet);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->with($algoApiData, AlgoApi::class, true)
            ->willReturn($algoApi);

        $selector = new AlgoApisSelectorService($companiesAlgoApisTable, $hydrator);

        $this->assertSame($algoApi, $selector->getAlgoApi($algoApiId, $companyId));
    }

    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     */
    public function testGetDirectNerAlgoApis(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $algoApi1 = $this->createMock(AlgoApi::class);
        $algoApi2 = $this->createMock(AlgoApi::class);

        $algoApiData1 = ['key1' => 'value1'];
        $algoApiData2 = ['key2' => 'value2'];

        $resultSet = new ResultSet();
        $resultSet->initialize([$algoApiData1, $algoApiData2]);

        $companiesAlgoApisTable = $this->createMock(CompaniesAlgoApisTable::class);
        $companiesAlgoApisTable
            ->method('getDirectNerAlgoApis')
            ->with($companyId)
            ->willReturn($resultSet);

        $hydratorMap = [
            [$algoApiData1, AlgoApi::class, true, $algoApi1],
            [$algoApiData2, AlgoApi::class, true, $algoApi2]
        ];
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->willReturnMap($hydratorMap);

        $selector = new AlgoApisSelectorService($companiesAlgoApisTable, $hydrator);
        $algoApiCollection = $selector->getDirectNerAlgoApis($companyId);

        $this->assertFalse($algoApiCollection->isEmpty());
        $this->assertSame(2, $algoApiCollection->count());

        $this->assertSame($algoApi1, $algoApiCollection[0]);
        $this->assertSame($algoApi2, $algoApiCollection[1]);
    }
}
