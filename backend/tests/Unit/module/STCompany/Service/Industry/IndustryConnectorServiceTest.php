<?php

namespace tests\Unit\module\STCompany\Service\Industry;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyIndustriesTable;
use STCompany\Entity\Industry\Industry as CompanyIndustry;
use STCompany\Service\Industry\IndustryConnectorService;
use tests\TestCase;

class IndustryConnectorServiceTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testConnect(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $industryId = $this->faker->numberBetween(101, 200);

        $companyIndustry = $this->createMock(CompanyIndustry::class);
        $industriesTable = $this->createMock(CompanyIndustriesTable::class);
        $industriesTable
            ->expects($this->once())
            ->method('saveIndustry')
            ->with(
                self::callback(
                    function (CompanyIndustry $companyIndustry) use ($companyId, $industryId) {
                        return $companyIndustry->getCompanyId() === $companyId
                            && $companyIndustry->getId() === $industryId;
                    }
                ),
            );
        $industriesTable
            ->method('getIndustry')
            ->with($industryId, $companyId)
            ->willReturn($companyIndustry);

        $connector = new IndustryConnectorService($industriesTable);

        $this->assertSame($companyIndustry, $connector->connect($industryId, $companyId));
    }

    /**
     * @throws Exception
     */
    public function testDisconnect(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $industryId = $this->faker->numberBetween(101, 200);

        $companyIndustryTable = $this->createMock(CompanyIndustriesTable::class);
        $companyIndustryTable
            ->expects($this->once())
            ->method('deleteIndustry')
            ->with($industryId, $companyId);

        $connector = new IndustryConnectorService($companyIndustryTable);
        $connector->disconnect($industryId, $companyId);
    }
}
