<?php

namespace tests\Unit\module\STCall\Helper;

use tests\TestCase;
use STCall\Helper\ChecklistCallsStatisticsHelper;

class ChecklistCallsStatisticsHelperTest extends TestCase
{
    /**
     * @test
     */
    public function calculateScoreWithRegularValues(): void
    {
        $data = [
            ['score' => 3],
            ['score' => 5],
            ['score' => 4],
        ];

        $result = ChecklistCallsStatisticsHelper::calculateScore($data, 'score');

        $this->assertEquals(4, $result);
    }

    /**
     * @test
     */
    public function calculateScoreWithPercentageFlag(): void
    {
        // Arrange
        $data = [
            ['rating' => 0.7],
            ['rating' => 0.8],
            ['rating' => 0.9],
        ];

        $result = ChecklistCallsStatisticsHelper::calculateScore($data, 'rating', true);

        $this->assertEquals(80, $result);
    }

    /**
     * @test
     */
    public function calculateScoreWithSingleItem(): void
    {
        $data = [
            ['points' => 7],
        ];

        $result = ChecklistCallsStatisticsHelper::calculateScore($data, 'points');

        $this->assertEquals(7, $result);
    }
}