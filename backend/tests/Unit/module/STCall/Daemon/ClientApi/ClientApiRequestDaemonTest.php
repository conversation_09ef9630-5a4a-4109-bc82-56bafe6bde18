<?php

namespace tests\Unit\module\STCall\Daemon\ClientApi;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Daemon\ClientApi\ClientApiRequestDaemon;
use STCall\Service\CallAnalysisService;
use STCall\Service\Import\UploadService;
use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use stdClass;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

class ClientApiRequestDaemonTest extends TestCase
{
    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testHandle(): void
    {
        $roboTruckEventName = 'call_upload_api_request';

        $requestMessage = new stdClass();
        $requestMessage->company_bearer_token = $this->faker->text(15);
        $requestMessage->request_id = $this->faker->text(10);
        $requestMessage->request_body = json_encode(['some_key' => 'some_value']);

        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company
            ->method('getId')
            ->willReturn($companyId);

        $companyService = $this->createMock(CompanyService::class);
        $companyService
            ->method('getCompanyByApiToken')
            ->with($requestMessage->company_bearer_token)
            ->willReturn($company);

        $roboTruckEventExtra = [
            'id' => $requestMessage->request_id,
            'company_id' => $companyId,
            'call_id' => null,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $requestMessage->request_body, $roboTruckEventExtra, $oldLogName);

        $daemon = new ClientApiRequestDaemon($dataCollector);

        $uploadService = $this->createMock(UploadService::class);
        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $params = $daemon->params();
        $params->add($companyService, CompanyService::class);
        $params->add($uploadService, UploadService::class);
        $params->add($callAnalysisService, CallAnalysisService::class);

        $daemon->handle(json_encode($requestMessage));
    }
}
