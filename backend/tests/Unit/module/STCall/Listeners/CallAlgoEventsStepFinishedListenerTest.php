<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Listeners;

use <PERSON><PERSON>\EventManager\EventInterface;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Entity\Call;
use STCall\Entity\CallRole;
use STCall\Entity\CallRoleCollection;
use STCall\Entity\EventHappeningCollection;
use STCall\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCall\Listeners\CallAlgoEventsStepFinishedListener;
use STCall\Service\CallAnalysis\AlgoEventsStep;
use STCall\Service\CallService;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCall\Service\Webhooks\WebhookServiceFactory;
use STCompany\Data\EventsColorsTable;
use STCompany\Entity\Company;
use STCompany\Entity\Event\Color;
use STCompany\Entity\Event\EventCollection;
use STCompany\Service\CompanyService;
use STCompany\Service\EventService;
use STCompany\Service\RoleService;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

final class CallAlgoEventsStepFinishedListenerTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function testListen(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $company = $this->createMock(Company::class);
        $queueName = 'webhooks';

        $getParamMap = [
            ['company_id', null, $companyId],
            ['call_id', null, $callId],
        ];
        $event = $this->createMock(EventInterface::class);
        $event->method('getParam')->willReturnMap($getParamMap);

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('getType')->willReturn(WebhookServiceInterface::WEBHOOK_TYPE_EVENTS);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory
            ->method('create')
            ->with(AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE)
            ->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('isWebhooksEnabled')
            ->with(WebhookServiceInterface::WEBHOOK_TYPE_EVENTS, $companyId)
            ->willReturn(true);

        $companySelector = $this->createMock(CompanyService::class);
        $companySelector->method('getCompany')->with($companyId)->willReturn($company);

        $roleIds = [$this->faker->numberBetween(101, 200), $this->faker->numberBetween(201, 300)];
        $roleSelector = $this->createMock(RoleService::class);
        $roleSelector->method('getRoleIds')->with($companyId)->willReturn($roleIds);

        $eventCollection = new EventCollection();
        $color = $this->createMock(Color::class);

        $eventSelector = $this->createMock(EventService::class);
        $eventSelector->method('getEvents')->with($companyId)->willReturn($eventCollection);
        $eventSelector
            ->method('getColor')
            ->with(EventsColorsTable::GREY_COLOR_ID)
            ->willReturn($color);

        $call1 = $this->createMock(Call::class);
        $call2 = $this->createMock(Call::class);

        $paragraphData1 = ['paragraph data 1'];
        $paragraphData2 = ['paragraph data 2'];

        $eventHappeningCollection1 = $this->createMock(EventHappeningCollection::class);
        $eventHappeningCollection2 = $this->createMock(EventHappeningCollection::class);
        $eventHappeningCollection3 = $this->createMock(EventHappeningCollection::class);

        $eventHappeningCollection1->method('isEmpty')->willReturn(false);
        $eventHappeningCollection2->method('isEmpty')->willReturn(true);
        $eventHappeningCollection3->method('isEmpty')->willReturn(false);

        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph3 = $this->createMock(Paragraph::class);

        $paragraph1->method('getEventHappenings')->willReturn($eventHappeningCollection1);
        $paragraph2->method('getEventHappenings')->willReturn($eventHappeningCollection2);
        $paragraph3->method('getEventHappenings')->willReturn($eventHappeningCollection3);

        $paragraph1->method('toArray')->willReturn($paragraphData1);
        $paragraph3->method('toArray')->willReturn($paragraphData2);

        $paragraphCollection1 = new ParagraphCollection([$paragraph1, $paragraph2]);
        $paragraphCollection2 = new ParagraphCollection([$paragraph3]);

        $call1->method('getParagraphs')->willReturn($paragraphCollection1);
        $call2->method('getParagraphs')->willReturn($paragraphCollection2);

        $callRole1 = $this->createMock(CallRole::class);
        $callRole2 = $this->createMock(CallRole::class);

        $callRole1->method('getCall')->willReturn($call1);
        $callRole2->method('getCall')->willReturn($call2);

        $callRoleCollection = new CallRoleCollection([$callRole1, $callRole2]);
        $callService = $this->createMock(CallService::class);
        $callService
            ->method('getCallsWithRoleIds')
            ->with($company, $roleIds, [$callId], $eventCollection, $color)
            ->willReturn($callRoleCollection);

        $data = [$paragraphData1, $paragraphData2];
        $expectedData = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE,
            'data' => $data,
        ];

        $channel = $this->createMock(AMQPChannel::class);
        $channel
            ->expects($this->once())
            ->method('basic_publish')
            ->with(
                self::callback(
                    function (AMQPMessage $message) use ($expectedData) {
                        $messageBody = json_decode($message->getBody(), true);

                        return $expectedData === $messageBody;
                    }
                ),
                '',
                $queueName
            );
        $channel->expects($this->once())->method('close');

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit->method('getChannel')->willReturn($channel);

        $dataCollector = $this->createMock(DataCollector::class);

        $listener = new CallAlgoEventsStepFinishedListener(
            $webhookServiceFactory,
            $webhookSettingsSelector,
            $companySelector,
            $roleSelector,
            $eventSelector,
            $callService,
            $rabbit,
            $dataCollector
        );

        $listener->listen($event);
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testListenWhenWebhooksIsOff(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $getParamMap = [
            ['company_id', null, $companyId],
            ['call_id', null, $callId],
        ];
        $event = $this->createMock(EventInterface::class);
        $event->method('getParam')->willReturnMap($getParamMap);

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('getType')->willReturn(WebhookServiceInterface::WEBHOOK_TYPE_EVENTS);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory
            ->method('create')
            ->with(AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE)
            ->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('isWebhooksEnabled')
            ->with(WebhookServiceInterface::WEBHOOK_TYPE_EVENTS, $companyId)
            ->willReturn(false);

        $companySelector = $this->createMock(CompanyService::class);
        $roleSelector = $this->createMock(RoleService::class);
        $eventSelector = $this->createMock(EventService::class);
        $callService = $this->createMock(CallService::class);

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit->expects($this->never())->method('getChannel');

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector->expects($this->never())->method('collect');

        $listener = new CallAlgoEventsStepFinishedListener(
            $webhookServiceFactory,
            $webhookSettingsSelector,
            $companySelector,
            $roleSelector,
            $eventSelector,
            $callService,
            $rabbit,
            $dataCollector
        );

        $listener->listen($event);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param int|null $companyId
     * @param string|null $callId
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testListenWhenWrongEventData(?int $companyId, ?string $callId): void
    {
        $roboTruckEventName = 'add_company_events_to_webhooks_queue_fail';

        $getParamMap = [
            ['company_id', null, $companyId],
            ['call_id', null, $callId],
        ];
        $event = $this->createMock(EventInterface::class);
        $event->method('getParam')->willReturnMap($getParamMap);

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('getType')->willReturn(WebhookServiceInterface::WEBHOOK_TYPE_EVENTS);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory
            ->method('create')
            ->with(AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE)
            ->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('isWebhooksEnabled')
            ->with(WebhookServiceInterface::WEBHOOK_TYPE_EVENTS, $companyId)
            ->willReturn(false);

        $companySelector = $this->createMock(CompanyService::class);
        $roleSelector = $this->createMock(RoleService::class);
        $eventSelector = $this->createMock(EventService::class);
        $callService = $this->createMock(CallService::class);

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit->expects($this->never())->method('getChannel');

        $roboTruckMessage = 'Fail to extract call data.';
        $roboTruckExtra = ['event' => json_encode($event)];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckMessage, $roboTruckExtra, null);

        $listener = new CallAlgoEventsStepFinishedListener(
            $webhookServiceFactory,
            $webhookSettingsSelector,
            $companySelector,
            $roleSelector,
            $eventSelector,
            $callService,
            $rabbit,
            $dataCollector
        );

        $listener->listen($event);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [null, 'some call id'],
            [123, null],
        ];
    }
}
