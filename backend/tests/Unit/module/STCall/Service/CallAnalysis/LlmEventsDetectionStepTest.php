<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Service\AiSolutionsCommutatorService;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequest;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequestCreator;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsAlgoEventsTable;
use STCall\Data\CallsTable;
use STCall\Entity\AlgoEvent;
use STCall\Entity\AlgoEventCollection;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\CallSelection\CallSelector;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\LlmEventsDetectionStep;
use STCall\Service\EventTriggerService;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCall\Service\Interfaces\TranslatorInterface;
use STCompany\Entity\Company;
use tests\TestCase;

final class LlmEventsDetectionStepTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetQueueNames(): void
    {
        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callSelector = $this->createMock(CallSelector::class);
        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $llmRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $aiSolutionsCommutatorService = $this->createMock(AiSolutionsCommutatorService::class);
        $translator = $this->createMock(TranslatorInterface::class);
        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);
        $eventTriggerService = $this->createMock(EventTriggerService::class);

        $step = new LlmEventsDetectionStep(
            $callsTable,
            $callFactory,
            $callSelector,
            $companySelector,
            $requestParamsBuilder,
            $llmRequestCreator,
            $aiSolutionsCommutatorService,
            $translator,
            $callsAlgoEventsTable,
            $eventTriggerService
        );

        $this->assertSame('call-llm-events-detection-step', $step->getQueueName());
        $this->assertSame('call-llm-events-detection-step-error', $step->getErrorQueueName());
        $this->assertSame('call-algo-events-step', $step->getNextStepQueue());
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunch(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $encryptionKey = $this->faker->text();

        $company = $this->createMock(Company::class);
        $company->method('getEncryptionKey')->willReturn($encryptionKey);

        $callId = $this->faker->uuid();

        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getIsLlmEventsDetected')->willReturn(false);
        $call->expects($this->once())->method('setIsLlmEventsDetected')->with(true);

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable
            ->expects($this->once())
            ->method('saveCall')
            ->with($call);

        $callFactory = $this->createMock(CallFactory::class);

        $callSelector = $this->createMock(CallSelector::class);
        $callSelector->method('getCall')->with($callId, $companyId)->willReturn($call);

        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $companySelector->method('getCompany')->with($companyId)->willReturn($company);

        $requestParams = $this->createMock(RequestParams::class);

        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder
            ->method('build')
            ->with($company, $call)
            ->willReturn($requestParams);

        $llmEventsAlgoApiRequest = $this->createMock(LlmEventsAlgoApiRequest::class);

        $llmRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $llmRequestCreator
            ->method('create')
            ->with($company, $requestParams)
            ->willReturn($llmEventsAlgoApiRequest);

        $event1 = $this->createMock(AlgoEvent::class);
        $event2 = $this->createMock(AlgoEvent::class);

        $eventData1 = [$this->faker->word() => $this->faker->word(), $this->faker->word() => $this->faker->word()];
        $eventData2 = [
            $this->faker->word() => $this->faker->sentence(),
            $this->faker->word() => $this->faker->word()
        ];

        $event1->method('toArray')->willReturn($eventData1);
        $event2->method('toArray')->willReturn($eventData2);

        $algoEventCollection = new AlgoEventCollection([$event1, $event2]);

        $aiSolutionsCommutatorService = $this->createMock(AiSolutionsCommutatorService::class);
        $aiSolutionsCommutatorService
            ->method('getAlgoEventsFromCall')
            ->with($company, $call, [$llmEventsAlgoApiRequest])
            ->willReturn($algoEventCollection);

        $translator = $this->createMock(TranslatorInterface::class);

        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);
        $callsAlgoEventsTable
            ->expects($this->once())
            ->method('saveEvents')
            ->with($algoEventCollection, $encryptionKey);

        $step = $this->getMockBuilder(LlmEventsDetectionStep::class)
            ->setConstructorArgs([
                'callsTable' => $callsTable,
                'callFactory' => $callFactory,
                'callSelector' => $callSelector,
                'companySelector' => $companySelector,
                'requestParamsBuilder' => $requestParamsBuilder,
                'llmRequestCreator' => $llmRequestCreator,
                'aiSolutionsCommutatorService' => $aiSolutionsCommutatorService,
                'translator' => $translator,
                'callsAlgoEventsTable' => $callsAlgoEventsTable
            ])
            ->onlyMethods(['translateAlgoEvents'])
            ->getMock();
        $step
            ->expects($this->once())
            ->method('translateAlgoEvents')
            ->with($algoEventCollection);

        $this->assertTrue($step->launch($callId, $companyId));
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunchWhenNoLlmRequest(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $encryptionKey = $this->faker->text();

        $company = $this->createMock(Company::class);
        $company->method('getEncryptionKey')->willReturn($encryptionKey);

        $callId = $this->faker->uuid();

        $call = $this->createMock(Call::class);
        $call->method('getIsLlmEventsDetected')->willReturn(false);
        $call->expects($this->once())->method('setIsLlmEventsDetected')->with(true);

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable
            ->expects($this->once())
            ->method('saveCall')
            ->with($call);

        $callFactory = $this->createMock(CallFactory::class);

        $callSelector = $this->createMock(CallSelector::class);
        $callSelector->method('getCall')->with($callId, $companyId)->willReturn($call);

        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $companySelector->method('getCompany')->with($companyId)->willReturn($company);

        $requestParams = $this->createMock(RequestParams::class);

        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder
            ->method('build')
            ->with($company, $call)
            ->willReturn($requestParams);

        $llmRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $llmRequestCreator
            ->method('create')
            ->with($company, $requestParams)
            ->willReturn(null);

        $aiSolutionsCommutatorService = $this->createMock(AiSolutionsCommutatorService::class);
        $aiSolutionsCommutatorService
            ->expects($this->never())
            ->method('getAlgoEventsFromCall');

        $translator = $this->createMock(TranslatorInterface::class);

        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);

        $step = $this->getMockBuilder(LlmEventsDetectionStep::class)
            ->setConstructorArgs([
                'callsTable' => $callsTable,
                'callFactory' => $callFactory,
                'callSelector' => $callSelector,
                'companySelector' => $companySelector,
                'requestParamsBuilder' => $requestParamsBuilder,
                'llmRequestCreator' => $llmRequestCreator,
                'aiSolutionsCommutatorService' => $aiSolutionsCommutatorService,
                'translator' => $translator,
                'callsAlgoEventsTable' => $callsAlgoEventsTable
            ])
            ->onlyMethods(['translateAlgoEvents'])
            ->getMock();

        $this->assertTrue($step->launch($callId, $companyId));
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunchWhenStepAlreadyCompleted(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $call = $this->createMock(Call::class);
        $call->method('getIsLlmEventsDetected')->willReturn(true);

        $callSelector = $this->createMock(CallSelector::class);
        $callSelector->method('getCall')->with($callId, $companyId)->willReturn($call);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $llmRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $aiSolutionsCommutatorService = $this->createMock(AiSolutionsCommutatorService::class);
        $translator = $this->createMock(TranslatorInterface::class);
        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);
        $eventTriggerService = $this->createMock(EventTriggerService::class);

        $this->expectException(StepIsAlreadyFinishedException::class);
        $this->expectExceptionMessage('Llm events detection step is already finished');

        $step = new LlmEventsDetectionStep(
            $callsTable,
            $callFactory,
            $callSelector,
            $companySelector,
            $requestParamsBuilder,
            $llmRequestCreator,
            $aiSolutionsCommutatorService,
            $translator,
            $callsAlgoEventsTable,
            $eventTriggerService
        );
        $step->launch($callId, $companyId);
    }
}
