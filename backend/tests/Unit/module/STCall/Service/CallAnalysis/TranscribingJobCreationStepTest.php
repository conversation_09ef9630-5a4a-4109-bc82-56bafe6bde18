<?php

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use Laminas\Db\ResultSet\ResultSetInterface;
use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Data\CallsParagraphsTable;
use ST<PERSON>all\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\CallAnalysis\TranscribingDriver\TwoStepsDriverInterface;
use STCall\Service\CallAnalysis\TranscribingJobCreationStep;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

class TranscribingJobCreationStepTest extends TestCase
{
    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testRun(): void
    {
        $roboTruckEventName = 'call_analyze_transcribing_job_creation_step_success';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();

        $call = $this->createMock(Call::class);
        $call
            ->method('getId')
            ->willReturn($callId);
        $call
            ->method('getCompanyId')
            ->willReturn($companyId);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory
            ->method('createCall')
            ->willReturn($call);

        $company = $this->createMock(Company::class);
        $company
            ->method('getId')
            ->willReturn($companyId);

        $companyData = ['some company data'];
        $resultSet = $this->createMock(ResultSetInterface::class);
        $resultSet
            ->method('current')
            ->willReturn($companyData);

        $companiesTable = $this->createMock(CompaniesTable::class);
        $companiesTable
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($resultSet);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->with($companyData, Company::class)
            ->willReturn($company);

        $jobName = $this->faker->text(15);
        $driver = $this->createMock(TwoStepsDriverInterface::class);
        $driver
            ->method('createJob')
            ->willReturn($jobName);

        $driverProvider = $this->createMock(DriverProvider::class);
        $driverProvider
            ->method('provide')
            ->willReturn($driver);

        $callsParagraphsTable = $this->createMock(CallsParagraphsTable::class);
        $companyVocabularyService = $this->createMock(CompanyVocabularyService::class);

        $roboTruckEventMessage = 'Job name is ' . $jobName;
        $roboTruckEventExtra = [
            'id' => null,
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, $oldLogName);

        $step = new TranscribingJobCreationStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $dataCollector,
            [],
            [],
            [],
            [],
            [],
            []
        );
        $step->applyOptions([
            'driver' => 'aws'
        ]);

        $step->launch($callId, $companyId);
    }
}
