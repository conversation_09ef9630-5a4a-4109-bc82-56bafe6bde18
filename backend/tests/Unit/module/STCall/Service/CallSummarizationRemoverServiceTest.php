<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service;

use PHPUnit\Framework\MockObject\Exception;
use STCall\Data\CallsSummarizationsTable;
use STCall\Entity\Call;
use STCall\Service\CallSummarizationRemoverService;
use tests\TestCase;

final class CallSummarizationRemoverServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testRemoveCallSummarization(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);

        $callSummarizationsTable = $this->createMock(CallsSummarizationsTable::class);
        $callSummarizationsTable
            ->expects($this->once())
            ->method('removeCallSummarization')
            ->with($callId, $companyId);

        $remover = new CallSummarizationRemoverService($callSummarizationsTable);
        $remover->removeCallSummarization($call);
    }
}
