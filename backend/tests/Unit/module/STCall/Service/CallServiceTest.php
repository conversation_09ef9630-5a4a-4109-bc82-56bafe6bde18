<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service;

use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use ST<PERSON>all\Data\CallsAlgoEventsTable;
use ST<PERSON>all\Data\CallsCommentsNotificationsTable;
use ST<PERSON>all\Data\CallsCommentsTable;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsReviewsTable;
use STCall\Data\CallsTable;
use STCall\Data\EventHappeningsChangesTable;
use STCall\Data\EventsRepository;
use STCall\Entity\Call;
use STCall\Entity\Paragraph;
use ST<PERSON>all\Entity\ParagraphCollection;
use STCall\Service\CallService;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\Hydrator;
use STUser\Data\UsersTable;
use tests\TestCase;

final class CallServiceTest extends TestCase
{
    /**
     * @throws Exception
     * @throws ReflectionException
     */
    public function testGetParagraphs(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $encryptionKey = $this->faker->text();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getEncryptionKey')->willReturn($encryptionKey);

        $callId = $this->faker->text();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);

        $paragraph1Data = [
            'key1' => 'value1',
        ];
        $paragraph2Data = [
            'key2' => 'value2',
        ];
        $paragraphsData = [$paragraph1Data, $paragraph2Data];

        $callsParagraphsTable = $this->createMock(CallsParagraphsTable::class);
        $callsParagraphsTable
            ->method('getParagraphs')
            ->with($companyId, $callId, $encryptionKey)
            ->willReturn($paragraphsData);

        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph1->method('getParagraphNumber')->willReturn($this->faker->numberBetween(101, 200));
        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph2->method('getParagraphNumber')->willReturn($this->faker->numberBetween(201, 300));

        $hydratorMap = [
            [$paragraph1Data, Paragraph::class, true, $paragraph1],
            [$paragraph2Data, Paragraph::class, true, $paragraph2],
        ];
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->willReturnMap($hydratorMap);

        $expectedCollection = new ParagraphCollection([
            $paragraph1->getParagraphNumber() => $paragraph1,
            $paragraph2->getParagraphNumber() => $paragraph2,
        ]);

        $callsTable = $this->createMock(CallsTable::class);
        $callsCommentsTable = $this->createMock(CallsCommentsTable::class);
        $callsCommentsNotificationsTable = $this->createMock(CallsCommentsNotificationsTable::class);
        $callsReviewsTable = $this->createMock(CallsReviewsTable::class);
        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);
        $eventsRepository = $this->createMock(EventsRepository::class);
        $eventHappeningsChangesTable = $this->createMock(EventHappeningsChangesTable::class);
        $usersTable = $this->createMock(UsersTable::class);

        $callService = new CallService(
            $callsTable,
            $callsParagraphsTable,
            $callsCommentsTable,
            $callsCommentsNotificationsTable,
            $callsReviewsTable,
            $callsAlgoEventsTable,
            $eventsRepository,
            $eventHappeningsChangesTable,
            $usersTable,
            $hydrator,
            ['api' => [], 'env' => 'local']
        );
        $this->assertEquals($expectedCollection, $callService->getParagraphs($company, $call->getId()));
    }
}
