<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service;

use Laminas\EventManager\Event;
use Laminas\EventManager\EventManager;
use PHPUnit\Framework\MockObject\Exception;
use STCall\Service\EventTriggerService;
use tests\TestCase;

final class EventTriggerServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testTriggerEvent(): void
    {
        $eventName = $this->faker->word();
        $eventParams = [$this->faker->word() => $this->faker->word(), $this->faker->word() => $this->faker->word()];

        $event = new Event($eventName, null, $eventParams);

        $eventManager = $this->createMock(EventManager::class);
        $eventManager
            ->expects($this->once())
            ->method('triggerEvent')
            ->with($event);

        $trigger = new EventTriggerService($eventManager);
        $trigger->trigger($eventName, $eventParams);
    }
}
