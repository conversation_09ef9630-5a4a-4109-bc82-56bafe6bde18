<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\Webhooks;

use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STCall\Service\CallAnalysis\AlgoEventsStep;
use STCall\Service\CallAnalysis\ChecklistStep;
use STCall\Service\CallAnalysis\LlmEventsDetectionStep;
use STCall\Service\CallAnalysis\SummarizationStep;
use STCall\Service\CallAnalysis\TranscribingJobCollectionStep;
use STCall\Service\CallAnalysis\TranscribingStep;
use STCall\Service\CallAnalysis\TranscribingWhisperStep;
use STCall\Service\CallAnalysis\TranslationStep;
use STCall\Service\Webhooks\Services\ChecklistsWebhookService;
use STCall\Service\Webhooks\Services\EventsWebhookService;
use STCall\Service\Webhooks\Services\ParagraphsWebhookService;
use STCall\Service\Webhooks\Services\SummarizationWebhookService;
use STCall\Service\Webhooks\Services\TranslationWebhookService;
use STCall\Service\Webhooks\WebhookServiceFactory;
use tests\TestCase;

final class WebhookServiceFactoryTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCreate(): void
    {
        $paragraphsWebhookPreparer = $this->createMock(ParagraphsWebhookService::class);
        $translationWebhookPreparer = $this->createMock(TranslationWebhookService::class);
        $eventsWebhookPreparer = $this->createMock(EventsWebhookService::class);
        $checklistsWebhookPreparer = $this->createMock(ChecklistsWebhookService::class);
        $summarizationWebhookPreparer = $this->createMock(SummarizationWebhookService::class);

        $factory = new WebhookServiceFactory(
            $paragraphsWebhookPreparer,
            $translationWebhookPreparer,
            $eventsWebhookPreparer,
            $checklistsWebhookPreparer,
            $summarizationWebhookPreparer
        );

        $this->assertSame($paragraphsWebhookPreparer, $factory->create(TranscribingStep::CALL_TRANSCRIBING_QUEUE));
        $this->assertSame(
            $paragraphsWebhookPreparer,
            $factory->create(TranscribingJobCollectionStep::CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE)
        );
        $this->assertSame(
            $paragraphsWebhookPreparer,
            $factory->create(TranscribingWhisperStep::CALL_TRANSCRIBING_QUEUE)
        );

        $this->assertSame($translationWebhookPreparer, $factory->create(TranslationStep::CALL_TRANSLATION_QUEUE));

        $this->assertSame(
            $eventsWebhookPreparer,
            $factory->create(AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE)
        );

        $this->assertSame($checklistsWebhookPreparer, $factory->create(ChecklistStep::CALL_CHECKLIST_QUEUE));

        $this->assertSame($summarizationWebhookPreparer, $factory->create(SummarizationStep::CALL_SUMMARIZATION_QUEUE));

        $unknownSource = 'unknown source';
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Unknown source: ' . $unknownSource);

        $factory->create($unknownSource);
    }
}
