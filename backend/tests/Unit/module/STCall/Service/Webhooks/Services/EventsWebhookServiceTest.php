<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\Webhooks\Services;

use STCall\Service\Webhooks\Services\EventsWebhookService;
use tests\TestCase;

class EventsWebhookServiceTest extends TestCase
{
    public function testGetType(): void
    {
        $service = new EventsWebhookService();
        $this->assertSame('events', $service->getType());
    }

    /**
     * @return void
     */
    public function testFilterData(): void
    {
        $data = [
            [
                'some_field' => 'some_value',
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 0,
                'speaker_number' => 0,
                'start_time' => 0.79999995,
                'event_happenings' => [
                    [
                        'event' => [
                            'some_field' => 'some_value',
                            'name' => 'location',
                            'role' => [
                                'id' => 406,
                                'name' => 'Admin',
                                'some_field' => 'some_value',
                            ],
                        ],
                        'main_point_phrase' => 'Where do you live?',
                        'en_main_point_phrase' => 'Where do you live?',
                        'text' => 'Where do you live?',
                        'en_text' => 'Where do you live?',
                    ],
                    [
                        'event' => [
                            'some_field' => 'some_value',
                            'name' => 'some event',
                            'role' => [
                                'id' => 406,
                                'name' => 'Admin',
                                'some_field' => 'some_value',
                            ],
                        ],
                        'main_point_phrase' => 'What happened?',
                        'en_main_point_phrase' => 'What happened?',
                        'text' => 'What happened?',
                        'en_text' => 'What happened?',
                    ]
                ],
                'speaker_role' => 'client'
            ],
            [
                'some_field' => 'some_value',
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 1,
                'start_time' => 3.9199998,
                'event_happenings' => [
                    [
                        'event' => [
                            'some_field' => 'some_value',
                            'name' => 'location',
                            'role' => [
                                'id' => 111,
                                'name' => 'Manager',
                                'some_field' => 'some_value',
                            ],
                        ],
                        'main_point_phrase' => 'I live in Pasadena.',
                        'en_main_point_phrase' => 'I live in Pasadena.',
                        'text' => 'I live in Pasadena.',
                        'en_text' => 'I live in Pasadena.',
                    ]
                ],
                'speaker_role' => 'unclear'
            ],
        ];

        $filteredData = [
            [
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 0,
                'paragraph_start_time' => 0.79999995,
                'speaker_role' => 'client',
                'events' => [
                    [
                        'role_id' => 406,
                        'role_name' => 'Admin',
                        'event_name' => 'location',
                        'main_point_phrase' => 'Where do you live?',
                        'en_main_point_phrase' => 'Where do you live?',
                        'text' => 'Where do you live?',
                        'en_text' => 'Where do you live?',
                    ],
                    [
                        'role_id' => 406,
                        'role_name' => 'Admin',
                        'event_name' => 'some event',
                        'main_point_phrase' => 'What happened?',
                        'en_main_point_phrase' => 'What happened?',
                        'text' => 'What happened?',
                        'en_text' => 'What happened?',
                    ],
                ]
            ],
            [
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 1,
                'paragraph_start_time' => 3.9199998,
                'speaker_role' => 'unclear',
                'events' => [
                    [
                        'role_id' => 111,
                        'role_name' => 'Manager',
                        'event_name' => 'location',
                        'main_point_phrase' => 'I live in Pasadena.',
                        'en_main_point_phrase' => 'I live in Pasadena.',
                        'text' => 'I live in Pasadena.',
                        'en_text' => 'I live in Pasadena.',
                    ]
                ]
            ],
        ];

        $webhookService = new EventsWebhookService();

        $this->assertEquals($filteredData, $webhookService->filterData($data));
    }
}
