<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\CallSummarization;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Service\CallSummarization\CallSummarizationGetter;
use STAlgo\Service\Client;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Entity\Call;
use STCompany\Entity\Company;
use tests\TestCase;

final class CallSummarizationGetterTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     * @throws GuzzleException
     * @throws JsonException
     */
    public function testGetSummarization(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $callId = $this->faker->uuid();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);

        $requestParams = $this->createMock(RequestParams::class);
        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder->method('build')->with($company, $call)->willReturn($requestParams);

        $summarizationData = [
            'results' => [
                'Key Points' => $this->faker->sentence(20),
                'Customer Sentiment' => $this->faker->sentence(10),
                'Next Steps' => $this->faker->sentence(15),
                'Primary Purpose' => $this->faker->sentence(10),
                'Main Topics' => $this->faker->sentence(10),
                'Customer\'s Problems' => $this->faker->sentence(20),
                'Key Action Items' => $this->faker->sentence(15),
                'Business Opportunities' => $this->faker->sentence(20),
                'Risks' => $this->faker->sentence(15),
                'Conversation Type' => $this->faker->sentence(10)
            ]
        ];

        $client = $this->createMock(Client::class);
        $client->method('getCallSummarization')->with($requestParams)->willReturn($summarizationData);

        $getter = new CallSummarizationGetter($requestParamsBuilder, $client);

        $callSummarization = $getter->getSummarization($company, $call);
        $this->assertSame($callId, $callSummarization->getCallId());
        $this->assertSame($companyId, $callSummarization->getCompanyId());
        $this->assertSame($summarizationData['results']['Key Points'], $callSummarization->getKeyPoints());
        $this->assertSame($summarizationData['results']['Customer Sentiment'], $callSummarization->getCustomerSentiment());
        $this->assertSame($summarizationData['results']['Next Steps'], $callSummarization->getNextSteps());
        $this->assertSame($summarizationData['results']['Primary Purpose'], $callSummarization->getPrimaryPurpose());
        $this->assertSame($summarizationData['results']['Main Topics'], $callSummarization->getMainTopics());
        $this->assertSame($summarizationData['results']['Customer\'s Problems'], $callSummarization->getCustomerProblems());
        $this->assertSame($summarizationData['results']['Key Action Items'], $callSummarization->getKeyActionItems());
        $this->assertSame($summarizationData['results']['Business Opportunities'], $callSummarization->getBusinessOpportunities());
    }

    /**
     * @dataProvider wrongResponseDataProvider
     * @param array $summarizationData
     * @return void
     * @throws Exception
     * @throws GuzzleException
     * @throws JsonException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     */
    public function testGetSummarizationWhenWrongResponseData(array $summarizationData): void
    {
        $company = $this->createMock(Company::class);
        $call = $this->createMock(Call::class);

        $requestParams = $this->createMock(RequestParams::class);
        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder->method('build')->with($company, $call)->willReturn($requestParams);

        $client = $this->createMock(Client::class);
        $client->method('getCallSummarization')->with($requestParams)->willReturn($summarizationData);

        $getter = new CallSummarizationGetter($requestParamsBuilder, $client);

        $this->expectException(ThirdPartyApiException::class);
        $this->expectExceptionMessage('Unexpected response from algo: ' . json_encode($summarizationData));
        $getter->getSummarization($company, $call);
    }

    public static function wrongResponseDataProvider(): array
    {
        return [
            'empty_array' => [[]],
            'empty_results' => [
                ['results' => []]
            ],
            'missing_status' => [
                [
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps'
                    ]
                ]
            ],
            'wrong_status' => [
                [
                    'status' => 'error',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points'
                    ]
                ]
            ],
            'missing_company_id' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment'
                    ]
                ]
            ],
            'missing_call_id' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment'
                    ]
                ]
            ],
            'missing_key_points' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps'
                    ]
                ]
            ],
            'missing_customer_sentiment' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Next Steps' => 'Sample next steps'
                    ]
                ]
            ],
            'missing_next_steps' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment'
                    ]
                ]
            ],
            'missing_primary_purpose' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps',
                        'Main Topics' => 'Sample topics',
                        'Customer\'s Problems' => 'Sample problems'
                    ]
                ]
            ],
            'missing_main_topics' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps',
                        'Primary Purpose' => 'Sample purpose',
                        'Customer\'s Problems' => 'Sample problems'
                    ]
                ]
            ],
            'missing_customer_problems' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps',
                        'Primary Purpose' => 'Sample purpose',
                        'Main Topics' => 'Sample topics',
                        'Key Action Items' => 'Sample action items'
                    ]
                ]
            ],
            'missing_key_action_items' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps',
                        'Primary Purpose' => 'Sample purpose',
                        'Main Topics' => 'Sample topics',
                        'Customer\'s Problems' => 'Sample problems',
                        'Business Opportunities' => 'Sample opportunities'
                    ]
                ]
            ],
            'missing_business_opportunities' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => 'Sample key points',
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps',
                        'Primary Purpose' => 'Sample purpose',
                        'Main Topics' => 'Sample topics',
                        'Customer\'s Problems' => 'Sample problems',
                        'Key Action Items' => 'Sample action items',
                        'Risks' => 'Sample risks'
                    ]
                ]
            ],
            'null_values' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => null,
                        'Customer Sentiment' => 'Sample sentiment',
                        'Next Steps' => 'Sample next steps'
                    ]
                ]
            ],
            'wrong_type_values' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 'not-a-number',
                        'call_id' => 12345,
                        'Key Points' => ['array', 'instead', 'of', 'string'],
                        'Customer Sentiment' => 'Sample sentiment'
                    ]
                ]
            ],
            'empty_string_values' => [
                [
                    'status' => 'ok',
                    'results' => [
                        'company_id' => 18,
                        'call_id' => '5d6c1ca0-fe0f-4d04-bfb6-6e1f71219beb',
                        'Key Points' => '',
                        'Customer Sentiment' => '',
                        'Next Steps' => ''
                    ]
                ]
            ]
        ];
    }
}
