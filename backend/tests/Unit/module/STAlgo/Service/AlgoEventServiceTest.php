<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service;

use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Data\AlgoApisTable;
use STA<PERSON>go\Data\AlgoEventsHintsTable;
use STAlgo\Data\AlgoEventsTable;
use STAlgo\Entity\AlgoEvent;
use STAlgo\Service\AlgoEventService;
use STAlgo\Service\Client;
use STLib\Mvc\Hydrator\Hydrator;
use tests\TestCase;

final class AlgoEventServiceTest extends TestCase
{
    /**
     * @throws Exception
     * @throws ReflectionException
     */
    public function testGetAvailableNerAlgoEvents(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $algoApiId1 = $this->faker->numberBetween(101, 200);
        $algoApiId2 = $this->faker->numberBetween(201, 300);

        $algoApisData = [
            ['algo_api_id' => $algoApiId1],
            ['algo_api_id' => $algoApiId2]
        ];
        $algoApisResultSet = new ResultSet();
        $algoApisResultSet->initialize($algoApisData);

        $algoApisTable = $this->createMock(AlgoApisTable::class);
        $algoApisTable
            ->method('getCompanyNerAlgoApis')
            ->with($companyId)
            ->willReturn($algoApisResultSet);

        $algoEventData1 = ['data1'];
        $algoEventData2 = ['data2'];
        $algoEventsData = [$algoEventData1, $algoEventData2];

        $algoEventsResultSet = new ResultSet();
        $algoEventsResultSet->initialize($algoEventsData);

        $algoEventsTable = $this->createMock(AlgoEventsTable::class);
        $algoEventsTable
            ->method('getNerAlgoEvents')
            ->with([$algoApiId1, $algoApiId2])
            ->willReturn($algoEventsResultSet);

        $algoEventsHintsTable = $this->createMock(AlgoEventsHintsTable::class);

        $algoEventName1 = $this->faker->word();
        $algoEventName2 = $this->faker->word();

        $event1 = new AlgoEvent();
        $event1->setAlgoEvent($algoEventName1);
        $event1->setAlgoApiId($algoApiId1);

        $event2 = new AlgoEvent();
        $event2->setAlgoEvent($algoEventName2);
        $event2->setAlgoApiId($algoApiId2);

        $hydratorMap = [
            [$algoEventData1, AlgoEvent::class, $event1],
            [$algoEventData2, AlgoEvent::class, $event2],
        ];
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->willReturnMap($hydratorMap);

        $client = $this->createMock(Client::class);

        $service = new AlgoEventService($algoEventsTable, $algoApisTable, $algoEventsHintsTable, $client, $hydrator);
        $algoEventsCollection = $service->getAvailableNerAlgoEvents($companyId);

        $this->assertSame($event1, $algoEventsCollection[$algoEventName1 . '-' . $algoApiId1]);
        $this->assertSame($event2, $algoEventsCollection[$algoEventName2 . '-' . $algoApiId2]);
    }
}
