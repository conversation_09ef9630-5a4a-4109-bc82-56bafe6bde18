<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\ParamsBuilding;

use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCall\Service\CallService;
use STCompany\Entity\Company;
use tests\TestCase;

class RequestParamsBuilderTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     */
    public function testBuild(): void
    {
        $callId = $this->faker->text();
        $companyId = $this->faker->numberBetween(1, 100);

        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getLanguage')->willReturn('en');

        $encryptionKey = $this->faker->text();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getEncryptionKey')->willReturn($encryptionKey);

        $paragraphId1 = $this->faker->numberBetween(101, 200);
        $paragraphText1 = $this->faker->text();
        $paragraphEnText1 = $this->faker->text();
        $paragraphSpeakerNumber1 = $this->faker->numberBetween(1, 2);

        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph1
            ->method('getParagraphNumber')
            ->willReturn($paragraphId1);
        $paragraph1
            ->method('getText')
            ->willReturn($paragraphText1);
        $paragraph1
            ->method('getEnText')
            ->willReturn($paragraphEnText1);
        $paragraph1
            ->method('getSpeakerNumber')
            ->willReturn($paragraphSpeakerNumber1);

        $paragraphId2 = $this->faker->numberBetween(201, 300);
        $paragraphText2 = $this->faker->text();
        $paragraphEnText2 = $this->faker->text();
        $paragraphSpeakerNumber2 = $this->faker->numberBetween(1, 2);

        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph2
            ->method('getParagraphNumber')
            ->willReturn($paragraphId2);
        $paragraph2
            ->method('getText')
            ->willReturn($paragraphText2);
        $paragraph2
            ->method('getEnText')
            ->willReturn($paragraphEnText2);
        $paragraph2
            ->method('getSpeakerNumber')
            ->willReturn($paragraphSpeakerNumber2);

        $paragraphCollection = new ParagraphCollection([$paragraph1, $paragraph2]);

        $callService = $this->createMock(CallService::class);
        $callService
            ->method('getParagraphs')
            ->with($company, $callId)
            ->willReturn($paragraphCollection);

        $builder = new RequestParamsBuilder($callService);
        $params = $builder->build($company, $call);

        $expectedParams = [
            'call_id' => $callId,
            'company_id' => $companyId,
            'language' => 'en',
            'paragraphs' => [
                [
                    'id' => $paragraphId1,
                    'text' => $paragraphText1,
                    'en_text' => $paragraphEnText1,
                    'speaker_number' => $paragraphSpeakerNumber1,
                ],
                [
                    'id' => $paragraphId2,
                    'text' => $paragraphText2,
                    'en_text' => $paragraphEnText2,
                    'speaker_number' => $paragraphSpeakerNumber2,
                ]
            ]
        ];

        $this->assertInstanceOf(RequestParams::class, $params);
        $this->assertSame($expectedParams, $params->toArray());
    }
}
