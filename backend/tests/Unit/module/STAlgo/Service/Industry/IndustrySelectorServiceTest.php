<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\Industry;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Data\AlgoApiIndustriesTable;
use STAlgo\Entity\Industry\IndustryCollection;
use STAlgo\Service\Industry\IndustrySelectorService;
use tests\TestCase;

final class IndustrySelectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetIndustries(): void
    {
        $algoApiId = $this->faker->numberBetween(1, 100);

        $industriesCollection = $this->createMock(IndustryCollection::class);

        $industriesTable = $this->createMock(AlgoApiIndustriesTable::class);
        $industriesTable
            ->method('getIndustries')
            ->with($algoApiId)
            ->willReturn($industriesCollection);

        $selector = new IndustrySelectorService($industriesTable);

        $this->assertSame($industriesCollection, $selector->getIndustries($algoApiId));
    }
}
