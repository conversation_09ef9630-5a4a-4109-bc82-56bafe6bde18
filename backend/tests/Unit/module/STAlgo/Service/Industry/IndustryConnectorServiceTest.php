<?php

namespace tests\Unit\module\STAlgo\Service\Industry;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Data\AlgoApiIndustriesTable;
use STAlgo\Entity\Industry\Industry as AlgoApiIndustry;
use STAlgo\Service\Industry\IndustryConnectorService;
use STApi\Entity\Exception\NotFoundApiException;
use tests\TestCase;

class IndustryConnectorServiceTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testConnect(): void
    {
        $algoApiId = $this->faker->numberBetween(1, 100);
        $industryId = $this->faker->numberBetween(101, 200);

        $algoApiIndustry = $this->createMock(AlgoApiIndustry::class);
        $industriesTable = $this->createMock(AlgoApiIndustriesTable::class);
        $industriesTable
            ->expects($this->once())
            ->method('saveIndustry')
            ->with(
                self::callback(
                    function (AlgoApiIndustry $algoApiIndustry) use ($algoApiId, $industryId) {
                        return $algoApiIndustry->getAlgoApiId() === $algoApiId
                            && $algoApiIndustry->getId() === $industryId;
                    }
                ),
            );
        $industriesTable
            ->method('getIndustry')
            ->with($industryId, $algoApiId)
            ->willReturn($algoApiIndustry);

        $connector = new IndustryConnectorService($industriesTable);

        $this->assertSame($algoApiIndustry, $connector->connect($industryId, $algoApiId));
    }

    /**
     * @throws Exception
     */
    public function testDisconnect(): void
    {
        $algoApiId = $this->faker->numberBetween(1, 100);
        $industryId = $this->faker->numberBetween(101, 200);

        $industriesTable = $this->createMock(AlgoApiIndustriesTable::class);
        $industriesTable
            ->expects($this->once())
            ->method('deleteIndustry')
            ->with($industryId, $algoApiId);

        $connector = new IndustryConnectorService($industriesTable);
        $connector->disconnect($industryId, $algoApiId);
    }
}
