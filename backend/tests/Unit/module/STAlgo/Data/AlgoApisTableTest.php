<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Data;

use Exception;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use tests\TestCase;

final class AlgoApisTableTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     */
    public function testSaveAlgoApis(): void
    {
        $newPath = $this->faker->text(10);
        $newIndustryId = $this->faker->numberBetween(1, 30);
        $newIsDefaultApi = $this->faker->randomElement([true, false]);
        $newAnalyzeMethod = $this->faker->word();
        $newName = $this->faker->word();
        $newDescription = $this->faker->text(1000);
        $newIsDeleted = $this->faker->randomElement([true, false]);

        $newAlgoApi = new AlgoApi();
        $newAlgoApi->setPath($newPath);
        $newAlgoApi->setIndustryId($newIndustryId);
        $newAlgoApi->isDefaultApi($newIsDefaultApi);
        $newAlgoApi->setAnalyzeMethod($newAnalyzeMethod);
        $newAlgoApi->setName($newName);
        $newAlgoApi->setDescription($newDescription);
        $newAlgoApi->setIsDeleted($newIsDeleted);

        $updatedId = $this->faker->numberBetween(100, 200);
        $updatedPath = $this->faker->text(10);
        $updatedIndustryId = $this->faker->numberBetween(1, 30);
        $updatedIsDefaultApi = $this->faker->randomElement([true, false]);
        $updatedAnalyzeMethod = $this->faker->word();
        $updatedName = $this->faker->word();
        $updatedDescription = $this->faker->text(1000);
        $updatedIsDeleted = $this->faker->randomElement([true, false]);

        $updatedAlgoApi = new AlgoApi();
        $updatedAlgoApi->setId($updatedId);
        $updatedAlgoApi->setPath($updatedPath);
        $updatedAlgoApi->setIndustryId($updatedIndustryId);
        $updatedAlgoApi->isDefaultApi($updatedIsDefaultApi);
        $updatedAlgoApi->setAnalyzeMethod($updatedAnalyzeMethod);
        $updatedAlgoApi->setName($updatedName);
        $updatedAlgoApi->setDescription($updatedDescription);
        $updatedAlgoApi->setIsDeleted($updatedIsDeleted);

        $algoApiCollection = new AlgoApiCollection([$newAlgoApi, $updatedAlgoApi]);

        $table = $this->getMockBuilder(AlgoApisTable::class)
            ->onlyMethods(['multiInsertOrUpdate'])
            ->disableOriginalConstructor()
            ->getMock();

        $dataToSave = [
            [
                'algo_api_id' => null,
                'path' => $newPath,
                'industry_id' => $newIndustryId,
                'is_default_api' => $newIsDefaultApi,
                'analyze_method' => $newAnalyzeMethod,
                'name' => $newName,
                'description' => $newDescription,
                'is_deleted' => $newIsDeleted,
            ],
            [
                'algo_api_id' => $updatedId,
                'path' => $updatedPath,
                'industry_id' => $updatedIndustryId,
                'is_default_api' => $updatedIsDefaultApi,
                'analyze_method' => $updatedAnalyzeMethod,
                'name' => $updatedName,
                'description' => $updatedDescription,
                'is_deleted' => $updatedIsDeleted,
            ]
        ];

        $updatedColumns = [
            'industry_id',
            'is_default_api',
            'name',
            'description',
            'is_deleted',
        ];
        $table
            ->expects($this->once())
            ->method('multiInsertOrUpdate')
            ->with($dataToSave, $updatedColumns);

        $table->saveAlgoApis($algoApiCollection);
    }
}
