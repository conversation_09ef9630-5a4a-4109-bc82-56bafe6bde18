<?php

namespace tests\Unit\module\Console\Command\RoboTruck;

use Console\Command\RoboTruck\PushCommand;
use <PERSON><PERSON>\Cli\Input\ParamAwareInputInterface;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PHPUnit\Framework\MockObject\Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use RuntimeException;
use STRabbit\Service\RabbitService;
use STRedis\Service\MutexService;
use STRoboTruck\Service\PusherService;
use Symfony\Component\Console\Exception\ExceptionInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Lock\LockInterface;
use tests\TestCase;
use tests\WithConsecutive;

class PushCommandTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ExceptionInterface
     */
    public function testPushWhenEventsCountIsAMultipleOfLimit(): void
    {
        $limit = '2';

        $lock = $this->createMock(LockInterface::class);
        $lock->expects($this->once())->method('acquire')->willReturn(true);
        $lock->expects($this->once())->method('release');

        $mutex = $this->createMock(MutexService::class);
        $mutex
            ->method('getLock')
            ->with(PushCommand::class)
            ->willReturn($lock);
        $this->serviceManager->setService(MutexService::class, $mutex);

        $messageText1 = ['key1' => 'value1'];
        $messageText2 = ['key2' => 'value2'];
        $messageText3 = ['key3' => 'value3'];
        $messageText4 = ['key4' => 'value4'];

        $messageBody1 = ['events' => [$messageText1]];
        $messageBody2 = ['events' => [$messageText2, $messageText3]];
        $messageBody3 = ['events' => [$messageText4]];

        $message1 = $this->createMock(AMQPMessage::class);
        $message1
            ->method('getBody')
            ->willReturn(json_encode($messageBody1));
        $message2 = $this->createMock(AMQPMessage::class);
        $message2
            ->method('getBody')
            ->willReturn(json_encode($messageBody2));
        $message3 = $this->createMock(AMQPMessage::class);
        $message3
            ->method('getBody')
            ->willReturn(json_encode($messageBody3));

        $noAck = true;

        $channel = $this->createMock(AMQPChannel::class);
        $channel
            ->expects($this->exactly(2))
            ->method('queue_declare')
            ->with(
                ...
                WithConsecutive::create(
                    [PusherService::ROBO_TRUCK_QUEUE_NAME, false, true, false, false],
                    [PusherService::ROBO_TRUCK_QUEUE_ERROR_NAME, false, true, false, false],
                )
            );

        $channel
            ->method('basic_get')
            ->with(PusherService::ROBO_TRUCK_QUEUE_NAME, $noAck)
            ->willReturnOnConsecutiveCalls($message1, $message2, $message3, null);

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit
            ->method('getChannel')
            ->willReturn($channel);

        $this->serviceManager->setService(RabbitService::class, $rabbit);

        $pusher = $this->createMock(PusherService::class);
        $pusher
            ->expects(self::exactly(2))
            ->method('push')
            ->with(
                ...
                WithConsecutive::create(
                    [[$messageText1, $messageText2]],
                    [[$messageText3, $messageText4]]
                )
            );
        $this->serviceManager->setService(PusherService::class, $pusher);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $input
            ->method('getOption')
            ->with('limit')
            ->willReturn($limit);

        $output = $this->createMock(OutputInterface::class);

        /**
         * @var PushCommand $pushCommand
         */
        $pushCommand = $this->serviceManager->get(PushCommand::class);

        $result = $pushCommand->run($input, $output);
        $this->assertSame(0, $result);
    }

    /**
     * @return void
     * @throws Exception
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function testPushWhenEventsCountIsNotAMultipleOfLimit(): void
    {
        $limit = '2';

        $lock = $this->createMock(LockInterface::class);
        $lock->expects($this->once())->method('acquire')->willReturn(true);
        $lock->expects($this->once())->method('release');

        $mutex = $this->createMock(MutexService::class);
        $mutex
            ->method('getLock')
            ->with(PushCommand::class)
            ->willReturn($lock);
        $this->serviceManager->setService(MutexService::class, $mutex);

        $messageText1 = ['key1' => 'value1'];
        $messageText2 = ['key2' => 'value2'];
        $messageText3 = ['key3' => 'value3'];

        $messageBody1 = ['events' => [$messageText1]];
        $messageBody2 = ['events' => [$messageText2, $messageText3]];

        $message1 = $this->createMock(AMQPMessage::class);
        $message1
            ->method('getBody')
            ->willReturn(json_encode($messageBody1));
        $message2 = $this->createMock(AMQPMessage::class);
        $message2
            ->method('getBody')
            ->willReturn(json_encode($messageBody2));

        $noAck = true;

        $channel = $this->createMock(AMQPChannel::class);
        $channel
            ->expects($this->exactly(2))
            ->method('queue_declare')
            ->with(
                ...
                WithConsecutive::create(
                    [PusherService::ROBO_TRUCK_QUEUE_NAME, false, true, false, false],
                    [PusherService::ROBO_TRUCK_QUEUE_ERROR_NAME, false, true, false, false],
                )
            );

        $channel
            ->method('basic_get')
            ->with(PusherService::ROBO_TRUCK_QUEUE_NAME, $noAck)
            ->willReturnOnConsecutiveCalls($message1, $message2, null);

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit
            ->method('getChannel')
            ->willReturn($channel);

        $this->serviceManager->setService(RabbitService::class, $rabbit);

        $pusher = $this->createMock(PusherService::class);
        $pusher
            ->expects(self::exactly(2))
            ->method('push')
            ->with(
                ...
                WithConsecutive::create(
                    [[$messageText1, $messageText2]],
                    [[$messageText3]]
                )
            );
        $this->serviceManager->setService(PusherService::class, $pusher);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $input
            ->method('getOption')
            ->with('limit')
            ->willReturn($limit);

        $output = $this->createMock(OutputInterface::class);

        $pushCommand = $this->serviceManager->get(PushCommand::class);

        $result = $pushCommand->run($input, $output);
        $this->assertSame(0, $result);
    }

    /**
     * @return void
     * @throws ContainerExceptionInterface
     * @throws Exception
     * @throws NotFoundExceptionInterface
     */
    public function testPushWhenNoMessages(): void
    {
        $lock = $this->createMock(LockInterface::class);
        $lock->expects($this->once())->method('acquire')->willReturn(true);
        $lock->expects($this->once())->method('release');

        $mutex = $this->createMock(MutexService::class);
        $mutex->method('getLock')->with(PushCommand::class)->willReturn($lock);
        $this->serviceManager->setService(MutexService::class, $mutex);

        $limit = 3;
        $noAck = true;

        $channel = $this->createMock(AMQPChannel::class);
        $channel
            ->method('basic_get')
            ->with(PusherService::ROBO_TRUCK_QUEUE_NAME, $noAck)
            ->willReturn(null);

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit
            ->method('getChannel')
            ->willReturn($channel);

        $this->serviceManager->setService(RabbitService::class, $rabbit);

        $pusher = $this->createMock(PusherService::class);
        $pusher
            ->expects(self::never())
            ->method('push');

        $this->serviceManager->setService(PusherService::class, $pusher);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $input
            ->method('getOption')
            ->with('limit')
            ->willReturn($limit);

        $output = $this->createMock(OutputInterface::class);

        $pushCommand = $this->serviceManager->get(PushCommand::class);

        $result = $pushCommand->run($input, $output);
        $this->assertSame(0, $result);
    }

    /**
     * @return void
     * @throws ContainerExceptionInterface
     * @throws Exception
     * @throws NotFoundExceptionInterface
     */
    public function testPushWhenCannotTakeLock(): void
    {
        $lock = $this->createMock(LockInterface::class);
        $lock->expects($this->once())->method('acquire')->willReturn(false);

        $mutex = $this->createMock(MutexService::class);
        $mutex->method('getLock')->with(PushCommand::class)->willReturn($lock);
        $this->serviceManager->setService(MutexService::class, $mutex);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $output = $this->createMock(OutputInterface::class);
        $output
            ->expects($this->once())
            ->method('writeln')
            ->with('RoboTruck push command is already running');

        $pushCommand = $this->serviceManager->get(PushCommand::class);

        $result = $pushCommand->run($input, $output);
        $this->assertSame(1, $result);
    }

    /**
     * @dataProvider wrongLimitDataProvider
     *
     * @param mixed $limit
     * @param string $error
     * @return void
     * @throws ContainerExceptionInterface
     * @throws Exception
     * @throws NotFoundExceptionInterface
     */
    public function testPushWhenWrongLimit(mixed $limit, string $error): void
    {
        $lock = $this->createMock(LockInterface::class);
        $lock->expects($this->once())->method('acquire')->willReturn(true);
        $lock->expects($this->once())->method('release');

        $mutex = $this->createMock(MutexService::class);
        $mutex->method('getLock')->with(PushCommand::class)->willReturn($lock);
        $this->serviceManager->setService(MutexService::class, $mutex);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $input
            ->method('getOption')
            ->with('limit')
            ->willReturn($limit);

        $output = $this->createMock(OutputInterface::class);

        $pushCommand = $this->serviceManager->get(PushCommand::class);
        $pushCommand->setServiceManager($this->serviceManager);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage($error);
        $pushCommand->run($input, $output);
    }

    public static function wrongLimitDataProvider(): array
    {
        return [
            [
                'limit' => 0,
                'error' => 'The limit must be greater than 1. Limit entered: 0',
            ],
            [
                'limit' => 'some string',
                'error' => 'The limit parameter must be an positive integer.',
            ],
            [
                'limit' => ['some array'],
                'error' => 'The limit parameter must be an positive integer.',
            ],
        ];
    }
}
