<?php

namespace tests\Unit\module\STRoboTruck\Service;

use STRoboTruck\Service\DataMaskService;
use tests\TestCase;

class DataMaskServiceTest extends TestCase
{
    public function testMaskData(): void
    {
        $data = [
            'some_key' => 'some_value',
            'password-confirm' => 'some_password',
            'parameters' => [
                'password' => 'some_password',
                'password-confirm' => 'some_password',
                'email' => '<EMAIL>',
            ],

        ];
        $dataMasked = [
            'some_key' => 'some_value',
            'password-confirm' => '*************',
            'parameters' => [
                'password' => '*************',
                'password-confirm' => '*************',
                'email' => '**********@gmail.com',
            ],
        ];

        $this->assertEquals((new DataMaskService())->maskSensitiveData($data), $dataMasked);
    }
}
