<?php

namespace tests\Unit\module\STRoboTruck\Service\HttpClient;

use PHPUnit\Framework\MockObject\Exception;
use STConfiguration\Service\ConfigurationService;
use STRoboTruck\Service\HttpClient\ClientFactory;
use tests\TestCase;

class ClientFactoryTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCreate(): void
    {
        $url = $this->faker->url();
        $apiKey = $this->faker->sha256();

        $roboTruckConfig = [
            'url' => $url,
            'api-key' => $apiKey,
        ];
        $configuration = $this->createMock(ConfigurationService::class);
        $configuration
            ->method('get')
            ->with('robo-truck')
            ->willReturn($roboTruckConfig);

        $factory = new ClientFactory($configuration);
        $client = $factory->create();

        $request = $client->getRequest();
        $this->assertSame($url, $request->getUri()->toString());

        $this->assertSame('application/json', $request->getHeader('Content-Type')->getFieldValue());
        $this->assertSame($apiKey, $request->getHeader('x-api-key')->getFieldValue());

        $this->assertSame('POST', $request->getMethod());
    }
}
