<?php

namespace tests\Unit\module\STRoboTruck\Service;

use Laminas\Http\PhpEnvironment\RemoteAddress;
use Laminas\Http\PhpEnvironment\Request;
use Laminas\Mvc\MvcEvent;
use Laminas\Router\RouteMatch;
use PHPUnit\Framework\MockObject\Exception;
use STCompany\Entity\Company;
use STLib\Expand\Collection;
use STRoboTruck\Service\DataCollection\DataCollector;
use STRoboTruck\Service\RequestCollectorService;
use STUser\Entity\User;
use tests\TestCase;

class RequestCollectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCollect(): void
    {
        $roboTruckEventName = 'controller_api_request';

        $userId = $this->faker->numberBetween(1, 100);
        $user = $this->createMock(User::class);
        $user
            ->method('getId')
            ->willReturn($userId);

        $companyId = $this->faker->numberBetween(101, 200);
        $company = $this->createMock(Company::class);
        $company
            ->method('getId')
            ->willReturn($companyId);

        $action = $this->faker->text();
        $routeMatch = $this->createMock(RouteMatch::class);
        $routeMatch
            ->method('getParam')
            ->with('action', null)
            ->willReturn($action);

        $mvcEvent = $this->createMock(MvcEvent::class);
        $mvcEvent
            ->method('getRouteMatch')
            ->willReturn($routeMatch);

        $parameters = [
            'key 1' => 'value 1',
            'key 2' => 'value 2',
        ];
        $parametersCollection = new Collection($parameters);

        $method = $this->faker->text(6);

        $request = $this->createMock(Request::class);
        $request
            ->method('getMethod')
            ->willReturn($method);

        $mvcEvent
            ->method('getRequest')
            ->willReturn($request);

        $ip = $this->faker->ipv4();
        $remoteAddress = $this->createMock(RemoteAddress::class);
        $remoteAddress
            ->method('getIpAddress')
            ->willReturn($ip);

        $controllerName = $this->faker->text(10);
        $roboTruckEventMessage = $method . ' request';
        $roboTruckEventExtra = [
            'controller_name' => $controllerName,
            'action' => $action,
            'user_id' => $userId,
            'company_id' => $companyId,
            'parameters' => $parameters,
            'client_ip' => $ip,
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra);


        $requestCollector = new RequestCollectorService($dataCollector, $remoteAddress);
        $requestCollector->collect($controllerName, $mvcEvent, $parametersCollection, $user, $company);
    }

    /**
     * @throws Exception
     */
    public function testCollectWhenNoUserAndCompanyAndClientIp(): void
    {
        $roboTruckEventName = 'controller_api_request';

        $action = $this->faker->text();
        $routeMatch = $this->createMock(RouteMatch::class);
        $routeMatch
            ->method('getParam')
            ->with('action', null)
            ->willReturn($action);

        $mvcEvent = $this->createMock(MvcEvent::class);
        $mvcEvent
            ->method('getRouteMatch')
            ->willReturn($routeMatch);

        $parametersCollection = new Collection();

        $method = $this->faker->text(6);

        $request = $this->createMock(Request::class);
        $request
            ->method('getMethod')
            ->willReturn($method);

        $mvcEvent
            ->method('getRequest')
            ->willReturn($request);

        $remoteAddress = $this->createMock(RemoteAddress::class);
        $remoteAddress
            ->method('getIpAddress')
            ->willReturn('');

        $controllerName = $this->faker->text(10);
        $roboTruckEventMessage = $method . ' request';
        $roboTruckEventExtra = [
            'controller_name' => $controllerName,
            'action' => $action,
            'user_id' => null,
            'company_id' => null,
            'parameters' => [],
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra);

        $requestCollector = new RequestCollectorService($dataCollector, $remoteAddress);
        $requestCollector->collect($controllerName, $mvcEvent, $parametersCollection, null, null);
    }
}
