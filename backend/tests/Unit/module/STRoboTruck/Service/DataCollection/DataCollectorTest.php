<?php

namespace tests\Unit\module\STRoboTruck\Service\DataCollection;

use Laminas\Log\Logger;
use <PERSON>inas\Stdlib\SplPriorityQueue;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PHPUnit\Framework\MockObject\Exception;
use STLog\Service\LogManager;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\DataCollection\DataCollector;
use STRoboTruck\Service\DataMaskService;
use STRoboTruck\Service\PusherService;
use tests\TestCase;

class DataCollectorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCollect(): void
    {
        $roboTruckLoggerName = 'robo-truck';
        $source = $this->faker->randomElement(['robonote', 'front']);
        $eventName = $this->faker->text(10);
        $message = $this->faker->text(10);
        $extra = [
            'some_key' => 'some_value',
        ];

        $splPriorityQueue = $this->createMock(SplPriorityQueue::class);
        $splPriorityQueue
            ->method('toArray')
            ->willReturn([]);

        $expectedExtra = array_merge($extra, ['event_name' => $eventName, 'source' => $source]);

        $logger = $this->createPartialMock(Logger::class, ['info']);
        $logger
            ->expects($this->once())
            ->method('info')
            ->with($message, $expectedExtra);
        $logger->setWriters($splPriorityQueue);

        $logManager = $this->createMock(LogManager::class);
        $logManager
            ->method('create')
            ->with($roboTruckLoggerName)
            ->willReturn($logger);

        $rabbit = $this->createMock(RabbitService::class);

        $dataMaskService = $this->createMock(DataMaskService::class);
        $dataMaskService
            ->expects($this->once())
            ->method('maskSensitiveData')
            ->with($extra)
            ->willReturn($extra);

        $dataCollector = new DataCollector($logManager, $rabbit, $dataMaskService);
        $dataCollector->collect($eventName, $message, $extra, source: $source);
    }

    /**
     * @throws Exception
     */
    public function testCollectWhenOldLoggerName(): void
    {
        $oldLoggerName = $this->faker->text(10);
        $roboTruckLoggerName = 'robo-truck';

        $eventName = $this->faker->text(10);
        $message = $this->faker->text(10);
        $oldExtra = ['some_key' => 'some_value'];

        $splPriorityQueue = $this->createMock(SplPriorityQueue::class);
        $splPriorityQueue
            ->method('toArray')
            ->willReturn([]);

        $oldLogger = $this->createPartialMock(Logger::class, ['info']);
        $oldLogger
            ->expects($this->once())
            ->method('info')
            ->with($message, $oldExtra);
        $oldLogger->setWriters($splPriorityQueue);

        $roboTruckExtra = $oldExtra;
        $roboTruckExtra['source'] = 'robonote';
        $roboTruckExtra['event_name'] = $eventName;
        $roboTruckLogger = $this->createPartialMock(Logger::class, ['info']);
        $roboTruckLogger
            ->expects($this->once())
            ->method('info')
            ->with($message, $roboTruckExtra);
        $roboTruckLogger->setWriters($splPriorityQueue);

        $logManagerReturnMap = [
            [$oldLoggerName, $oldLogger],
            [$roboTruckLoggerName, $roboTruckLogger],
        ];
        $logManager = $this->createMock(LogManager::class);
        $logManager
            ->method('create')
            ->willReturnMap($logManagerReturnMap);

        $rabbit = $this->createMock(RabbitService::class);

        $dataMaskService = $this->createMock(DataMaskService::class);
        $dataMaskService
            ->expects($this->once())
            ->method('maskSensitiveData')
            ->with($oldExtra)
            ->willReturn($oldExtra);

        $dataCollector = new DataCollector($logManager, $rabbit, $dataMaskService);
        $dataCollector->collect($eventName, $message, $oldExtra, $oldLoggerName);
    }

    /**
     * @throws Exception
     */
    public function testCollectErrors(): void
    {
        $events = [
            [
                'name' => 'search client',
                'source' => 'robonote',
            ],
            [
                'name' => 'search call',
                'source' => 'frontend',
            ]
        ];

        $code = $this->faker->numberBetween(0, 1000);
        $error = 'some error';

        $expectedAMQPMessageBody = [
            'events' => $events,
            'error' => 'Status code: ' . $code . ', error message: ' . $error,
        ];

        $channel = $this->createMock(AMQPChannel::class);
        $channel
            ->expects($this->once())
            ->method('basic_publish')
            ->with(
                self::callback(
                    function (AMQPMessage $message) use ($expectedAMQPMessageBody) {
                        $messageBody = json_decode($message->getBody(), true);

                        return $expectedAMQPMessageBody === $messageBody;
                    }
                ),
                '',
                PusherService::ROBO_TRUCK_QUEUE_ERROR_NAME
            );
        $channel
            ->expects($this->once())
            ->method('close');

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit
            ->method('getChannel')
            ->willReturn($channel);

        $logManager = $this->createMock(LogManager::class);
        $dataMaskService = $this->createMock(DataMaskService::class);

        $dataCollector = new DataCollector($logManager, $rabbit, $dataMaskService);
        $dataCollector->collectError($events, $code, $error);
    }
}
