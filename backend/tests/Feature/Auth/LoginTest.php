<?php

declare(strict_types=1);

namespace tests\Feature\Auth;

use ArrayObject;
use Carbon\Carbon;
use Exception;
use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use RobThree\Auth\TwoFactorAuth;
use STApi\Entity\Exception\NotFoundApiException;
use STRedis\Service\RedisService;
use STUser\Data\UsersTable;
use STUser\Entity\User;
use STUser\Service\TwoFactorAuthFactory;
use tests\Feature\AppTokenTestCase;

final class LoginTest extends AppTokenTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws Exception
     */
    public function testLogin(): void
    {
        $id = $this->faker->numberBetween(1, 100);
        $salt = $this->serviceManager->get('config')['auth']['salt'];
        $email = $this->faker->email();
        $registrationDate = new Carbon();
        $password = $this->faker->password();
        $passwordHash = hash('whirlpool', $salt . $password . $registrationDate->format('h:i:sU'));
        $twoFactorAuthSecret = $this->faker->sha1();
        $qrCodeImageAsDataUri = $this->faker->url();

        $twoFactorAuth = $this->createMock(TwoFactorAuth::class);
        $twoFactorAuth->method('createSecret')->willReturn($twoFactorAuthSecret);
        $twoFactorAuth->method('getQRCodeImageAsDataUri')->willReturn($qrCodeImageAsDataUri);

        $twoFactorAuthFactory = $this->createMock(TwoFactorAuthFactory::class);
        $twoFactorAuthFactory
            ->method('create')
            ->willReturn($twoFactorAuth);
        $this->serviceManager->setService(TwoFactorAuthFactory::class, $twoFactorAuthFactory);

        $userResultSet = $this->createMock(ResultSet::class);
        $userData = new ArrayObject([
            'id' => $id,
            'user_email' => $email,
            'user_password' => $passwordHash,
        ]);
        $userResultSet
            ->method('current')
            ->willReturn($userData);

        $usersTable = $this->createMock(UsersTable::class);
        $usersTable->method('getUserByEmail')->with($email)->willReturn($userResultSet);
        $usersTable->method('getUserByCredentials')->with($email, $passwordHash)->willReturn($userResultSet);
        $usersTable
            ->expects($this->once())
            ->method('saveUser')
            ->with(
                self::callback(
                    function (User $actualUser) use ($twoFactorAuthSecret) {
                        return $twoFactorAuthSecret === $actualUser->getTwoFactorSecret();
                    }
                )
            );
        $this->serviceManager->setService(UsersTable::class, $usersTable);

        $loginAttemptsKey = sprintf('user:%s:login_attempts', $id);

        $redisService = $this->createMock(RedisService::class);
        $redisService
            ->method('get')
            ->with($loginAttemptsKey)
            ->willReturn((string) $this->faker->numberBetween(1, 5));
        $redisService
            ->expects($this->once())
            ->method('delete')
            ->with($loginAttemptsKey);
        $this->serviceManager->setService(RedisService::class, $redisService);

        $this->getRequest()->setContent(json_encode([
            'email' => $email,
            'password' => $password
        ]));

        $this->dispatchApi('auth', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertTrue($response['result']['is-qr-code-generated']);
        $this->assertSame($qrCodeImageAsDataUri, $response['result']['two-auth-qr-code']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws ContainerExceptionInterface
     * @throws Exception
     */
    public function testLoginWhenEmailDoesntExists(): void
    {
        $id = $this->faker->numberBetween(1, 100);
        $salt = $this->serviceManager->get('config')['auth']['salt'];
        $email = $this->faker->email();
        $registrationDate = new Carbon();
        $password = $this->faker->password();
        $passwordHash = hash('whirlpool', $salt . $password . $registrationDate->format('h:i:sU'));
        $twoFactorAuthSecret = $this->faker->sha1();
        $qrCodeImageAsDataUri = $this->faker->url();

        $twoFactorAuth = $this->createMock(TwoFactorAuth::class);
        $twoFactorAuth->method('createSecret')->willReturn($twoFactorAuthSecret);
        $twoFactorAuth->method('getQRCodeImageAsDataUri')->willReturn($qrCodeImageAsDataUri);

        $twoFactorAuthFactory = $this->createMock(TwoFactorAuthFactory::class);
        $twoFactorAuthFactory
            ->method('create')
            ->willReturn($twoFactorAuth);
        $this->serviceManager->setService(TwoFactorAuthFactory::class, $twoFactorAuthFactory);

        $userResultSet = $this->createMock(ResultSet::class);
        $userData = new ArrayObject([
            'id' => $id,
            'user_email' => $email,
            'user_password' => $passwordHash,
        ]);
        $userResultSet
            ->method('current')
            ->willReturn($userData);

        $usersTable = $this->createMock(UsersTable::class);
        $usersTable->method('getUserByEmail')->with($email)->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(UsersTable::class, $usersTable);

        $this->getRequest()->setContent(json_encode([
            'email' => $email,
            'password' => $password
        ]));

        $this->dispatchApi('auth', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(400, $response['error']['code']);
        $this->assertSame('Invalid email or password', $response['error']['messages']);

        $this->assertResponseStatusCode(400);
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws ContainerExceptionInterface
     * @throws Exception
     */
    public function testLoginWhenWrongCredentials(): void
    {
        $id = $this->faker->numberBetween(1, 100);
        $salt = $this->serviceManager->get('config')['auth']['salt'];
        $email = $this->faker->email();
        $registrationDate = new Carbon();
        $password = $this->faker->password();
        $passwordHash = hash('whirlpool', $salt . $password . $registrationDate->format('h:i:sU'));
        $twoFactorAuthSecret = $this->faker->sha1();
        $qrCodeImageAsDataUri = $this->faker->url();

        $twoFactorAuth = $this->createMock(TwoFactorAuth::class);
        $twoFactorAuth->method('createSecret')->willReturn($twoFactorAuthSecret);
        $twoFactorAuth->method('getQRCodeImageAsDataUri')->willReturn($qrCodeImageAsDataUri);

        $twoFactorAuthFactory = $this->createMock(TwoFactorAuthFactory::class);
        $twoFactorAuthFactory
            ->method('create')
            ->willReturn($twoFactorAuth);
        $this->serviceManager->setService(TwoFactorAuthFactory::class, $twoFactorAuthFactory);

        $userResultSet = $this->createMock(ResultSet::class);
        $userData = new ArrayObject([
            'id' => $id,
            'user_email' => $email,
            'user_password' => $passwordHash,
        ]);
        $userResultSet
            ->method('current')
            ->willReturn($userData);

        $usersTable = $this->createMock(UsersTable::class);
        $usersTable->method('getUserByEmail')->with($email)->willReturn($userResultSet);
        $usersTable->method('getUserByCredentials')->with($email, $passwordHash)->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(UsersTable::class, $usersTable);

        $loginAttemptsKey = sprintf('user:%s:login_attempts', $id);

        $redisService = $this->createMock(RedisService::class);
        $redisService
            ->method('get')
            ->with($loginAttemptsKey)
            ->willReturn((string) $this->faker->numberBetween(1, 5));
        $redisService
            ->expects($this->once())
            ->method('incr')
            ->with($loginAttemptsKey);
        $redisService
            ->expects($this->once())
            ->method('expire')
            ->with($loginAttemptsKey, 10 * 60);
        $this->serviceManager->setService(RedisService::class, $redisService);

        $this->getRequest()->setContent(json_encode([
            'email' => $email,
            'password' => $password
        ]));

        $this->dispatchApi('auth', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(400, $response['error']['code']);
        $this->assertSame('Invalid email or password', $response['error']['messages']);

        $this->assertResponseStatusCode(400);
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws ContainerExceptionInterface
     * @throws Exception
     */
    public function testLoginWhenCodeSent(): void
    {
        $id = $this->faker->numberBetween(1, 100);
        $salt = $this->serviceManager->get('config')['auth']['salt'];
        $email = $this->faker->email();
        $registrationDate = new Carbon();
        $password = $this->faker->password();
        $passwordHash = hash('whirlpool', $salt . $password . $registrationDate->format('h:i:sU'));
        $twoFactorAuthCode = $this->faker->sentence();
        $twoFactorAuthSecret = $this->faker->sha1();

        $twoFactorAuth = $this->createMock(TwoFactorAuth::class);
        $twoFactorAuth
            ->method('verifyCode')
            ->with($twoFactorAuthSecret, $twoFactorAuthCode)
            ->willReturn(true);

        $twoFactorAuthFactory = $this->createMock(TwoFactorAuthFactory::class);
        $twoFactorAuthFactory
            ->method('create')
            ->willReturn($twoFactorAuth);
        $this->serviceManager->setService(TwoFactorAuthFactory::class, $twoFactorAuthFactory);

        $userResultSet = $this->createMock(ResultSet::class);
        $userData = new ArrayObject([
            'id' => $id,
            'user_email' => $email,
            'user_password' => $passwordHash,
            'user_two_factor_secret' => $twoFactorAuthSecret,
        ]);
        $userResultSet
            ->method('current')
            ->willReturn($userData);

        $usersTable = $this->createMock(UsersTable::class);
        $usersTable->method('getUserByEmail')->with($email)->willReturn($userResultSet);
        $usersTable->method('getUserByCredentials')->with($email, $passwordHash)->willReturn($userResultSet);
        $this->serviceManager->setService(UsersTable::class, $usersTable);

        $loginAttemptsKey = sprintf('user:%s:login_attempts', $id);

        $redisService = $this->createMock(RedisService::class);
        $redisService
            ->method('get')
            ->with($loginAttemptsKey)
            ->willReturn((string) $this->faker->numberBetween(1, 5));
        $redisService
            ->expects($this->once())
            ->method('delete')
            ->with($loginAttemptsKey);
        $redisService
            ->expects($this->once())
            ->method('set')
            ->with(
                self::callback(
                    function (string $key) use ($id) {
                        return str_starts_with($key, 'auth:token:' . $id);
                    }
                ),
                $id
            );
        $redisService
            ->expects($this->once())
            ->method('expire')
            ->with(
                self::callback(
                    function (string $key) use ($id) {
                        return str_starts_with($key, 'auth:token:' . $id);
                    }
                ),
                60 * 60 * 24 * 30
            );
        $this->serviceManager->setService(RedisService::class, $redisService);

        $this->getRequest()->setContent(json_encode([
            'code' => $twoFactorAuthCode,
            'email' => $email,
            'password' => $password
        ]));

        $this->dispatchApi('auth', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($id, $response['result']['user']['id']);
        $this->assertSame($email, $response['result']['user']['email']);
        $this->assertNotNull($response['result']['user']['personal_avatar_file']);
        $this->assertNotNull($response['result']['user']['token']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws ContainerExceptionInterface
     * @throws Exception
     */
    public function testLoginWhenInvalidCodeSent(): void
    {
        $id = $this->faker->numberBetween(1, 100);
        $salt = $this->serviceManager->get('config')['auth']['salt'];
        $email = $this->faker->email();
        $registrationDate = new Carbon();
        $password = $this->faker->password();
        $passwordHash = hash('whirlpool', $salt . $password . $registrationDate->format('h:i:sU'));
        $twoFactorAuthCode = $this->faker->sentence();
        $twoFactorAuthSecret = $this->faker->sha1();

        $twoFactorAuth = $this->createMock(TwoFactorAuth::class);
        $twoFactorAuth
            ->method('verifyCode')
            ->with($twoFactorAuthSecret, $twoFactorAuthCode)
            ->willReturn(false);

        $twoFactorAuthFactory = $this->createMock(TwoFactorAuthFactory::class);
        $twoFactorAuthFactory
            ->method('create')
            ->willReturn($twoFactorAuth);
        $this->serviceManager->setService(TwoFactorAuthFactory::class, $twoFactorAuthFactory);

        $userResultSet = $this->createMock(ResultSet::class);
        $userData = new ArrayObject([
            'id' => $id,
            'user_email' => $email,
            'user_password' => $passwordHash,
            'user_two_factor_secret' => $twoFactorAuthSecret,
        ]);
        $userResultSet
            ->method('current')
            ->willReturn($userData);

        $usersTable = $this->createMock(UsersTable::class);
        $usersTable->method('getUserByEmail')->with($email)->willReturn($userResultSet);
        $usersTable->method('getUserByCredentials')->with($email, $passwordHash)->willReturn($userResultSet);
        $this->serviceManager->setService(UsersTable::class, $usersTable);

        $loginAttemptsKey = sprintf('user:%s:login_attempts', $id);

        $redisService = $this->createMock(RedisService::class);
        $redisService
            ->method('get')
            ->with($loginAttemptsKey)
            ->willReturn((string) $this->faker->numberBetween(1, 5));
        $redisService
            ->expects($this->once())
            ->method('delete')
            ->with($loginAttemptsKey);
        $this->serviceManager->setService(RedisService::class, $redisService);

        $this->getRequest()->setContent(json_encode([
            'code' => $twoFactorAuthCode,
            'email' => $email,
            'password' => $password
        ]));

        $this->dispatchApi('auth', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Invalid code of two factor authentification', $response['error']['messages']);

        $this->assertResponseStatusCode(400);
    }
}
