<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Webhooks;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCompany\Data\CompaniesWebhooksSettingsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use tests\Feature\AuthTestCase;

final class GetListWebhookSettingsTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     * @throws ContainerExceptionInterface
     */
    public function testGetList(): void
    {
        $webhookSettingsData1 = [
            'company_webhook_setting_id' => $this->faker->numberBetween(1, 100),
            'type' => $this->faker->randomElement(WebhookServiceInterface::TYPES),
            'is_enabled' => $this->faker->boolean(),
            'url' => $this->faker->url(),
            'headers' => json_encode([$this->faker->word() => $this->faker->word()]),
            'company_id' => $this->companyId,
            'some_another_field' => 'some value',
        ];
        $webhookSettingsData2 = [
            'company_webhook_setting_id' => $this->faker->numberBetween(101, 200),
            'type' => $this->faker->randomElement(WebhookServiceInterface::TYPES),
            'is_enabled' => $this->faker->boolean(),
            'url' => $this->faker->url(),
            'headers' => null,
            'company_id' => $this->companyId,
            'some_another_field' => 'some value',
        ];
        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->method('getCompaniesWebhookSettingsDataList')
            ->with($this->companyId)
            ->willReturn([$webhookSettingsData1, $webhookSettingsData2]);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('company/webhooks', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            [
                'id' => $webhookSettingsData1['company_webhook_setting_id'],
                'type' => $webhookSettingsData1['type'],
                'is_enabled' => $webhookSettingsData1['is_enabled'],
                'url' => $webhookSettingsData1['url'],
                'headers' => json_decode($webhookSettingsData1['headers'], true),
                'company_id' => $this->companyId
            ],
            [
                'id' => $webhookSettingsData2['company_webhook_setting_id'],
                'type' => $webhookSettingsData2['type'],
                'is_enabled' => $webhookSettingsData2['is_enabled'],
                'url' => $webhookSettingsData2['url'],
                'headers' => [],
                'company_id' => $this->companyId
            ]
        ];

        $this->assertSame($expectedData, $response['result']['webhooks']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetListWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/webhooks', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
