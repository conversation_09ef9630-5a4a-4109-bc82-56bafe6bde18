<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\ChecklistsTable;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class DeleteChecklistTest extends AuthTestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDelete(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->expects($this->once())
            ->method('deleteChecklist')
            ->with($checklistId, $this->companyId);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('checklist/' . $checklistId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertTrue($response['result']['is_deleted']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteByNonGlobalAdmin(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->expects($this->never())
            ->method('deleteChecklist');
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('checklist/' . $checklistId, 'DELETE');

        $this->assertResponseStatusCode(401);
    }
}
