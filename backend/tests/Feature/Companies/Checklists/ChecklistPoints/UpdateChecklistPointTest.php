<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists\ChecklistPoints;

use Carbon\Carbon;
use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\ChecklistsPointsTable;
use ST<PERSON>ompany\Data\ChecklistsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class UpdateChecklistPointTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdate(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId = $this->faker->numberBetween(101, 200);
        $title = 'Valid checklist point title with enough characters and letters';
        $description = 'This is a valid description with enough characters and letters for validation';
        $expectedActions = 'These are valid expected actions with enough characters and letters';
        $goodPerformanceDescription = 'This is a valid good performance description with enough characters';
        $badPerformanceDescription = 'This is a valid bad performance description with enough characters';
        $goodPerformanceExample = 'This is a valid good performance example with enough characters';
        $badPerformanceExample = 'This is a valid bad performance example with enough characters';
        $isOptional = $this->faker->boolean();
        $triggerCondition = 'This is a valid trigger condition with enough characters and letters';

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistPoint = new ChecklistPoint();
        $checklistPoint->setId($checklistPointId);
        $checklistPoint->setChecklistId($checklistId);
        $checklistPoint->setTitle($title);
        $checklistPoint->setDescription($description);
        $checklistPoint->setExpectedActions($expectedActions);
        $checklistPoint->setGoodPerformanceDescription($goodPerformanceDescription);
        $checklistPoint->setBadPerformanceDescription($badPerformanceDescription);
        $checklistPoint->setGoodPerformanceExample($goodPerformanceExample);
        $checklistPoint->setBadPerformanceExample($badPerformanceExample);
        $checklistPoint->setIsOptional($isOptional);
        $checklistPoint->setTriggerCondition($triggerCondition);
        $checklistPoint->setUpdatedAt(Carbon::now());

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->expects($this->once())
            ->method('saveChecklistPoint')
            ->with(
                self::callback(
                    function (ChecklistPoint $actualChecklistPoint) use ($checklistPoint) {
                        $expectedData = $checklistPoint->toArray();
                        $actualData = $actualChecklistPoint->toArray();

                        $actualUpdatedAt = $actualChecklistPoint->getUpdatedAt();
                        $expectedUpdatedAt = $checklistPoint->getUpdatedAt();
                        unset($actualData['updated_at'], $expectedData['updated_at']);

                        return $actualData === $expectedData &&
                            $actualUpdatedAt->format('Y-m-d H:i:s') === $expectedUpdatedAt->format('Y-m-d H:i:s');
                    }
                ),
            );
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->getRequest()->setContent(json_encode([
            'checklist_id' => $checklistId,
            'title' => $title,
            'description' => $description,
            'expected_actions' => $expectedActions,
            'good_performance_description' => $goodPerformanceDescription,
            'bad_performance_description' => $badPerformanceDescription,
            'good_performance_example' => $goodPerformanceExample,
            'bad_performance_example' => $badPerformanceExample,
            'is_optional' => $isOptional,
            'trigger_condition' => $triggerCondition,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($checklistPointId, $response['result']['checklistPoint']['id']);
        $this->assertSame($checklistId, $response['result']['checklistPoint']['checklist_id']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @dataProvider adminRoleTypesDataProvider
     * @param int $adminRoleType
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateByAdmin(int $adminRoleType): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId = $this->faker->numberBetween(101, 200);
        $title = $this->faker->word();
        $expectedActions = $this->faker->text();
        $goodPerformanceDescription = $this->faker->text();
        $badPerformanceDescription = $this->faker->text();

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->getRequest()->setContent(json_encode([
            'checklist_id' => $checklistId,
            'title' => $title,
            'expected_actions' => $expectedActions,
            'good_performance_description' => $goodPerformanceDescription,
            'bad_performance_description' => $badPerformanceDescription,
        ]));

        $this->loginAs($adminRoleType);
        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($checklistPointId, $response['result']['checklistPoint']['id']);
        $this->assertSame($checklistId, $response['result']['checklistPoint']['checklist_id']);
        $this->assertResponseStatusCode(200);
    }

    public static function adminRoleTypesDataProvider(): array
    {
        return [
            [1], // Role::ADMIN_ROLE_TYPE
            [4], // Role::COMPANY_ADMIN_ROLE_TYPE
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenReadPermission(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId = $this->faker->numberBetween(101, 200);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::READ_PERMISSION
        );

        $this->getRequest()->setContent(json_encode(['checklist_id' => $checklistId]));
        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenNoPermissions(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId = $this->faker->numberBetween(101, 200);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);

        $this->getRequest()->setContent(json_encode(['checklist_id' => $checklistId]));
        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
