<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists\ChecklistPoints;

use Exception;
use JsonException;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STAlgo\Service\Client;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class ImproveChecklistPointTest extends AuthTestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImprove(): void
    {
        $originalTitle = $this->faker->text();
        $originalExpectedActions = $this->faker->text();
        $originalGoodPerformanceDescription = $this->faker->text();
        $originalBadPerformanceDescription = $this->faker->text();

        $improvedExpectedActions = $this->faker->text();
        $improvedGoodPerformanceDescription = $this->faker->text();
        $improvedBadPerformanceDescription = $this->faker->text();

        $params = [
            'endpoint' => 'checklistV5',
            'event' => [
                'title' => $originalTitle,
                'expected_criteria' => $originalExpectedActions,
                'green_assessment_description' => $originalGoodPerformanceDescription,
                'red_assessment_description' => $originalBadPerformanceDescription,
            ]
        ];

        $resultData = [
            'status' => 'ok',
            'results' => [
                'title' => $originalTitle,
                'expected_criteria' => $improvedExpectedActions,
                'green_assessment_description' => $improvedGoodPerformanceDescription,
                'red_assessment_description' => $improvedBadPerformanceDescription,
            ],
        ];
        $client = $this->createMock(Client::class);
        $client
            ->method('improveChecklistPoint')
            ->with($params)
            ->willReturn($resultData);
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'title' => $originalTitle,
            'expected_actions' => $originalExpectedActions,
            'good_performance_description' => $originalGoodPerformanceDescription,
            'bad_performance_description' => $originalBadPerformanceDescription,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist/checklist-points/improve', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($improvedExpectedActions, $response['result']['checklist_point']['expected_actions']);
        $this->assertSame(
            $improvedGoodPerformanceDescription,
            $response['result']['checklist_point']['good_performance_description']
        );
        $this->assertSame(
            $improvedBadPerformanceDescription,
            $response['result']['checklist_point']['bad_performance_description']
        );

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImproveWhenUnsuccessfulAlgoResponse(): void
    {
        $originalTitle = $this->faker->text();
        $originalExpectedActions = $this->faker->text();
        $originalGoodPerformanceDescription = $this->faker->text();
        $originalBadPerformanceDescription = $this->faker->text();

        $params = [
            'endpoint' => 'checklistV5',
            'event' => [
                'title' => $originalTitle,
                'expected_criteria' => $originalExpectedActions,
                'green_assessment_description' => $originalGoodPerformanceDescription,
                'red_assessment_description' => $originalBadPerformanceDescription,
            ]
        ];
        $resultData = [
            'status' => 'fail',
            'results' => [],
        ];
        $client = $this->createMock(Client::class);
        $client
            ->method('improveChecklistPoint')
            ->with($params)
            ->willReturn($resultData);
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'title' => $originalTitle,
            'expected_actions' => $originalExpectedActions,
            'good_performance_description' => $originalGoodPerformanceDescription,
            'bad_performance_description' => $originalBadPerformanceDescription,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist/checklist-points/improve', 'POST');

        $error = 'Failed to improve checklist point.';
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages']);

        $this->assertResponseStatusCode(502);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param array $resultData
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImproveWhenWrongAlgoResponse(array $resultData): void
    {
        $title = $this->faker->text();
        $originalExpectedActions = $this->faker->text();
        $originalGoodPerformanceDescription = $this->faker->text();
        $originalBadPerformanceDescription = $this->faker->text();

        $params = [
            'endpoint' => 'checklistV5',
            'event' => [
                'title' => $title,
                'expected_criteria' => $originalExpectedActions,
                'green_assessment_description' => $originalGoodPerformanceDescription,
                'red_assessment_description' => $originalBadPerformanceDescription,
            ]
        ];

        $client = $this->createMock(Client::class);
        $client
            ->method('improveChecklistPoint')
            ->with($params)
            ->willReturn($resultData);
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'title' => $title,
            'expected_actions' => $originalExpectedActions,
            'good_performance_description' => $originalGoodPerformanceDescription,
            'bad_performance_description' => $originalBadPerformanceDescription,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist/checklist-points/improve', 'POST');

        $error = 'Failed to improve checklist point.';
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages']);

        $this->assertResponseStatusCode(502);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                [
                    'results' => [
                        'title' => 'title',
                        'expected_criteria' => 'expected_criteria',
                        'green_assessment_description' => 'green_assessment_description',
                        'red_assessment_description' => 'red_assessment_description',
                    ]
                ]
            ],
            [
                [
                    'status' => 'fail',
                    'results' => [
                        'title' => 'title',
                        'expected_criteria' => 'expected_criteria',
                        'green_assessment_description' => 'green_assessment_description',
                        'red_assessment_description' => 'red_assessment_description',
                    ]
                ]
            ],
            [
                [
                    'status' => 'ok',
                ]
            ],
            [
                [
                    'status' => 'ok',
                    'results' => [
                        'title' => 'title',
                        'green_assessment_description' => 'green_assessment_description',
                        'red_assessment_description' => 'red_assessment_description',
                    ]
                ]
            ],
            [
                [
                    'status' => 'ok',
                    'results' => [
                        'title' => 'title',
                        'expected_criteria' => 'expected_criteria',
                        'red_assessment_description' => 'red_assessment_description',
                    ]
                ]
            ],
            [
                [
                    'status' => 'ok',
                    'results' => [
                        'title' => 'title',
                        'expected_criteria' => 'expected_criteria',
                        'green_assessment_description' => 'green_assessment_description'
                    ]
                ]
            ]
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testImproveWhenUnexpectedAlgoResponse(): void
    {
        $originalTitle = $this->faker->text();
        $originalExpectedActions = $this->faker->text();
        $originalGoodPerformanceDescription = $this->faker->text();
        $originalBadPerformanceDescription = $this->faker->text();

        $params = [
            'endpoint' => 'checklistV5',
            'event' => [
                'title' => $originalTitle,
                'expected_criteria' => $originalExpectedActions,
                'green_assessment_description' => $originalGoodPerformanceDescription,
                'red_assessment_description' => $originalBadPerformanceDescription,
            ]
        ];

        $client = $this->createMock(Client::class);
        $client
            ->method('improveChecklistPoint')
            ->with($params)
            ->willThrowException(new JsonException());
        $this->serviceManager->setService(Client::class, $client);

        $this->getRequest()->setContent(json_encode([
            'title' => $originalTitle,
            'expected_actions' => $originalExpectedActions,
            'good_performance_description' => $originalGoodPerformanceDescription,
            'bad_performance_description' => $originalBadPerformanceDescription,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist/checklist-points/improve', 'POST');

        $error = 'Failed to improve checklist point.';
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages']);

        $this->assertResponseStatusCode(502);
    }
}
