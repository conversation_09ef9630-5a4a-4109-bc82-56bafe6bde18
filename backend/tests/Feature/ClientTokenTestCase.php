<?php

declare(strict_types=1);

namespace tests\Feature;

use ArrayObject;
use Laminas\Http\Headers;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Data\ApiApplicationsTable;
use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use tests\TestCase as BaseTestCase;

abstract class ClientTokenTestCase extends BaseTestCase
{
    private string $bearerToken;

    protected int $companyId;
    protected Company $company;

    /**
     * @return void
     * @throws PHPUnitException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->companyId = $this->faker->numberBetween(1, 100);

        $this->setHeaders();
        $this->setApplication();
        $this->setCompany();
    }

    /**
     * @throws PHPUnitException
     */
    private function setApplication(): void
    {
        $applicationArrayObject = $this->createMock(ArrayObject::class);
        $applicationArrayObject
            ->method('getArrayCopy')
            ->willReturn([
                'application_type' => ApiApplicationsTable::THIRD_PARTY_COMPANY_APPLICATION,
                'company_id' => $this->companyId
            ]);

        $applicationTable = $this->createMock(ApiApplicationsTable::class);
        $applicationTable
            ->method('getApplicationByToken')
            ->with($this->bearerToken)
            ->willReturn($applicationArrayObject);

        $this->serviceManager->setService(ApiApplicationsTable::class, $applicationTable);
    }

    /**
     * @return void
     * @throws PHPUnitException
     */
    private function setCompany(): void
    {
        $this->company = $this->createMock(Company::class);
        $this->company
            ->method('getId')
            ->willReturn($this->companyId);

        $companyService = $this->createMock(CompanyService::class);
        $companyService
            ->method('getCompany')
            ->with($this->companyId)
            ->willReturn($this->company);

        $this->serviceManager->setService(CompanyService::class, $companyService);
    }

    private function setHeaders(): void
    {
        $this->bearerToken = $this->faker->text(30);
        $authorizationToken = 'Bearer ' . $this->bearerToken;

        $headers = new Headers();
        $headers->addHeaders([
            'Authorization' => $authorizationToken,
        ]);
        $this->getRequest()->setHeaders($headers);
    }
}
