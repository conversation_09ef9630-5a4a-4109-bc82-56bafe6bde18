<?php

declare(strict_types=1);

namespace tests\Feature\Onboarding\CallsSettings;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use tests\Feature\AppTokenTestCase;

final class UpdateCallsSettingsTest extends AppTokenTestCase
{
    private const string ERROR_MESSAGE = 'Wrong data format.';

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateCallsSettings(): void
    {
        $languageCodes = array_keys(CallsTable::LANGUAGES);
        $data = [
            'languages' => [
                $this->faker->randomElement($languageCodes),
                $this->faker->randomElement($languageCodes),
                $this->faker->randomElement($languageCodes),
            ],
            'min_call_duration_for_auto_analyze' => $this->faker->numberBetween(1, 300),
        ];

        $companyName = $this->faker->text(30);
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();

        $onboardingFormToSave = $this->createMock(OnboardingForm::class);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setCallsSettings')
            ->with($data);

        $savedOnboardingForm = new OnboardingForm();
        $savedOnboardingForm->setCallsSettings($data);
        $savedOnboardingForm->setExternalId($externalId);
        $savedOnboardingForm->setFrontFormLink($frontFormLink);
        $savedOnboardingForm->setInviteLink($inviteLink);
        $savedOnboardingForm->setCompanyName($companyName);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturnOnConsecutiveCalls(
                $onboardingFormToSave,
                $onboardingFormToSave,
                $onboardingFormToSave,
                $savedOnboardingForm
            );

        $onboardingFormsTable
            ->expects($this->once())
            ->method('save')
            ->with($onboardingFormToSave);

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode($data));

        $this->dispatchApi('onboarding/form/' . $externalId . '/calls-settings', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($data, $response['result']['form']['calls_settings']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateCallsSettingsWhenNoLanguages(): void
    {
        $data = [
            'languages' => [],
            'min_call_duration_for_auto_analyze' => $this->faker->numberBetween(1, 300),
        ];

        $companyName = $this->faker->text(30);
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();

        $onboardingFormToSave = $this->createMock(OnboardingForm::class);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setCallsSettings')
            ->with($data);

        $savedOnboardingForm = new OnboardingForm();
        $savedOnboardingForm->setCallsSettings($data);
        $savedOnboardingForm->setExternalId($externalId);
        $savedOnboardingForm->setFrontFormLink($frontFormLink);
        $savedOnboardingForm->setInviteLink($inviteLink);
        $savedOnboardingForm->setCompanyName($companyName);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturnOnConsecutiveCalls(
                $onboardingFormToSave,
                $onboardingFormToSave,
                $onboardingFormToSave,
                $savedOnboardingForm
            );

        $onboardingFormsTable
            ->expects($this->once())
            ->method('save')
            ->with($onboardingFormToSave);

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode($data));

        $this->dispatchApi('onboarding/form/' . $externalId . '/calls-settings', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($data, $response['result']['form']['calls_settings']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateCallsSettingsWhenWrongFormId(): void
    {
        $externalId = $this->faker->uuid();
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/calls-settings', 'POST');

        $expectedError = '"id":["The onboarding form does not exists."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateCallsSettingsWhenDisableForEditForm(): void
    {
        $externalId = $this->faker->uuid();
        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getIsSubmitted')->willReturn(true);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturn($onboardingForm);
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/calls-settings', 'POST');

        $expectedError = '"id":["The completed onboarding form is already submitted and disable for edit."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateCallsSettingsWhenNoData(): void
    {
        $externalId = $this->faker->uuid();

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable->expects($this->never())
            ->method('save');
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/calls-settings', 'POST');

        $expectedError = '"calls_settings":["' . self::ERROR_MESSAGE . '"]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @dataProvider wrongCallsSettingsProvider
     * @param mixed $callsSettings
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateCallsSettingsWhenWrongData(mixed $callsSettings): void
    {
        $externalId = $this->faker->uuid();

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable->expects($this->never())
            ->method('save');
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode($callsSettings));

        $this->dispatchApi('onboarding/form/' . $externalId . '/calls-settings', 'POST');

        $expectedError = '"calls_settings":["' . self::ERROR_MESSAGE . '"]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    public static function wrongCallsSettingsProvider(): array
    {
        return [
            [
                'callsSettings' => null
            ],
            [
                'callsSettings' => ''
            ],
            [
                'callsSettings' => []
            ],
            [
                'callsSettings' => [[]]
            ],
            [
                'callsSettings' => [
                    'languages' => [
                        'en'
                    ],
                    'min_call_duration_for_auto_analyze' => null
                ]
            ],
            [
                'callsSettings' => [
                    'languages' => [
                        'en'
                    ],
                    'min_call_duration_for_auto_analyze' => 'some wrong min_call_duration_for_auto_analyze'
                ]
            ],
            [
                'callsSettings' => [
                    'languages' => [
                        'some unknown language code'
                    ],
                    'min_call_duration_for_auto_analyze' => 123
                ]
            ],
            [
                'callsSettings' => [
                    'languages' => [
                        'en'
                    ],
                    'min_call_duration_for_auto_analyze' => -1
                ]
            ],
            [
                'callsSettings' => [
                    'languages' => [
                        'en'
                    ],
                    'min_call_duration_for_auto_analyze' => 301
                ]
            ],
        ];
    }
}
