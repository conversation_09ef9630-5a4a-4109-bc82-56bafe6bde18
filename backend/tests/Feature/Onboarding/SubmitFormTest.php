<?php

declare(strict_types=1);

namespace tests\Feature\Onboarding;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Service\CompanyCreation\Onboarding\CompanyCreator;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Notification\OnboardingFormChecker;
use STOnboarding\Service\NotificatorService;
use tests\Feature\AppTokenTestCase;

final class SubmitFormTest extends AppTokenTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testSubmitFormWhenItIsCompleted(): void
    {
        $companyName = $this->faker->text(30);
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();
        $companyId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $onboardingFormToSave = $this->createMock(OnboardingForm::class);
        $onboardingFormToSave
            ->method('getIsSubmitted')
            ->willReturn(false);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setIsSubmitted')
            ->with(true);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setCompanyId')
            ->with($companyId);

        $savedOnboardingForm = new OnboardingForm();
        $savedOnboardingForm->setExternalId($externalId);
        $savedOnboardingForm->setFrontFormLink($frontFormLink);
        $savedOnboardingForm->setInviteLink($inviteLink);
        $savedOnboardingForm->setIsSubmitted(true);
        $savedOnboardingForm->setCompanyName($companyName);
        $savedOnboardingForm->setCompanyId($companyId);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturnOnConsecutiveCalls(
                $onboardingFormToSave,
                $onboardingFormToSave,
                $onboardingFormToSave,
                $savedOnboardingForm
            );

        $onboardingFormsTable->expects($this->once())
            ->method('save')
            ->with($onboardingFormToSave);
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $notificator = $this->createMock(NotificatorService::class);
        $notificator
            ->expects($this->once())
            ->method('notifyAboutFormSubmission')
            ->with($onboardingFormToSave);
        $this->serviceManager->setService(NotificatorService::class, $notificator);

        $onboardingFormChecker = $this->createMock(OnboardingFormChecker::class);
        $onboardingFormChecker
            ->method('isFormCompleted')
            ->with($onboardingFormToSave)
            ->willReturn(true);
        $this->serviceManager->setService(OnboardingFormChecker::class, $onboardingFormChecker);

        $companyCreator = $this->createMock(CompanyCreator::class);
        $companyCreator
            ->expects($this->once())
            ->method('createFromOnboardingForm')
            ->with($onboardingFormToSave)
            ->willReturn($company);
        $this->serviceManager->setService(CompanyCreator::class, $companyCreator);

        $this->dispatchApi('onboarding/form/' . $externalId . '/submit', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertArrayHasKey('result', $response);
        $this->assertIsArray($response['result']);

        $this->assertArrayHasKey('is_submitted', $response['result']['form']);
        $this->assertTrue($response['result']['form']['is_submitted']);
        $this->assertSame($companyId, $response['result']['form']['company_id']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testSubmitFormWhenItIsNonCompleted(): void
    {
        $companyName = $this->faker->text(30);
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();

        $onboardingForm = new OnboardingForm();
        $onboardingForm->setExternalId($externalId);
        $onboardingForm->setFrontFormLink($frontFormLink);
        $onboardingForm->setInviteLink($inviteLink);
        $onboardingForm->setIsSubmitted(false);
        $onboardingForm->setCompanyName($companyName);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturn($onboardingForm);

        $onboardingFormsTable
            ->expects($this->never())
            ->method('save');
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $notificator = $this->createMock(NotificatorService::class);
        $notificator
            ->expects($this->once())
            ->method('notifyAboutFormSubmission')
            ->with($onboardingForm);
        $this->serviceManager->setService(NotificatorService::class, $notificator);

        $onboardingFormChecker = $this->createMock(OnboardingFormChecker::class);
        $onboardingFormChecker
            ->method('isFormCompleted')
            ->with($onboardingForm)
            ->willReturn(false);
        $this->serviceManager->setService(OnboardingFormChecker::class, $onboardingFormChecker);

        $companyCreator = $this->createMock(CompanyCreator::class);
        $companyCreator
            ->expects($this->never())
            ->method('createFromOnboardingForm');
        $this->serviceManager->setService(CompanyCreator::class, $companyCreator);

        $this->dispatchApi('onboarding/form/' . $externalId . '/submit', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertArrayHasKey('result', $response);
        $this->assertIsArray($response['result']);

        $this->assertArrayHasKey('is_submitted', $response['result']['form']);
        $this->assertFalse($response['result']['form']['is_submitted']);
        $this->assertNull($response['result']['form']['company_id']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testSubmitFormWhenWrongFormId(): void
    {
        $externalId = $this->faker->uuid();
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/submit', 'POST');

        $expectedError = '"id":["The onboarding form does not exists."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testSubmitFormWhenDisableForEditForm(): void
    {
        $externalId = $this->faker->uuid();
        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getIsSubmitted')->willReturn(true);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturn($onboardingForm);
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/submit', 'POST');

        $expectedError = '"id":["The completed onboarding form is already submitted and disable for edit."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }
}
