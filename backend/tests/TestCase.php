<?php

namespace tests;

use Faker\Factory;
use Faker\Generator;
use <PERSON>inas\ServiceManager\ServiceLocatorInterface;
use Laminas\Test\PHPUnit\Controller\AbstractControllerTestCase;

class TestCase extends AbstractControllerTestCase
{
    protected ServiceLocatorInterface $serviceManager;
    protected Generator $faker;

    protected function setUp(): void
    {
        $this->setApplicationConfig(
            include getcwd() . '/config/application.config.php',
        );
        parent::setUp();

        $this->faker = Factory::create();
        $this->getApplication();
        $this->serviceManager = $this->application->getServiceManager();
        $this->serviceManager->setAllowOverride(true);
    }
}
