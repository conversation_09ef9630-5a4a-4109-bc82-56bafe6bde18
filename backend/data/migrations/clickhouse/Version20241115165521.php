<?php

namespace Clickhouse\Migrations;

class Version20241115165521 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                 swap_speakers
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                swap_speakers Bool DEFAULT 0
            AFTER
                is_sent_to_transcribing
        ');
    }
}
