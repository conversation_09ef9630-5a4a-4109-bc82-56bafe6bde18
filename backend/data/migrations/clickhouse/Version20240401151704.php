<?php

namespace Clickhouse\Migrations;

class Version20240401151704 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                risk_value_inner_conflict Float32 DEFAULT 0
            AFTER
                risk_value
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                risk_value_inner_conflict
        ');
    }
}
