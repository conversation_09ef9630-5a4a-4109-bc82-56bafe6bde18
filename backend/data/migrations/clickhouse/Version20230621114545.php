<?php

namespace Clickhouse\Migrations;

class Version20230621114545 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            ADD COLUMN
                event_is_deleted Bool DEFAULT 0
            AFTER
                event_is_confirmed
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            DROP COLUMN
                event_is_deleted
        ');
    }
}
