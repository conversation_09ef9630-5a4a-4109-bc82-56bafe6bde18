<?php

namespace Clickhouse\Migrations;

class Version20240222163454 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE precalculated_calls_events SYNC
        ');
        $this->getClient()->write('
            RENAME TABLE precalculated_calls_events_tmp TO precalculated_calls_events
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        throw new \Exception('There is no down for migration');
    }
}
