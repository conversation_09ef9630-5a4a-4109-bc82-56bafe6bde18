<?php

namespace Clickhouse\Migrations;

use ST<PERSON>all\Data\CallsChecklistsPointsTable;
use STClickhouse\Entity\Migration\BaseMigration;

class Version20250509102116 extends BaseMigration
{
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_checklists_points
            ADD COLUMN
                status Enum(\'' .
                CallsChecklistsPointsTable::STATUS_PASSED . '\', \'' .
                CallsChecklistsPointsTable::STATUS_NOT_PASSED . '\', \'' .
                CallsChecklistsPointsTable::STATUS_NOT_ENCOUNTERED .
                '\') 
            AFTER
                is_passed
        ');

        $this->getClient()->write('
            ALTER TABLE
                calls_checklists_points
            UPDATE
                status = if(is_passed = 1, \'' . CallsChecklistsPointsTable::STATUS_PASSED . '\', \'' . CallsChecklistsPointsTable::STATUS_NOT_PASSED . '\')
            WHERE
                1=1
        ');
    }

    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_checklists_points
            DROP COLUMN
                status
        ');
    }
}
