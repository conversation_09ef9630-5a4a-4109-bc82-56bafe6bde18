<?php

namespace Clickhouse\Migrations;

class Version20240726153026 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE precalculated_clients
        ');
        $this->getClient()->write('
            CREATE TABLE precalculated_clients
            (
                date Date,
                company_id UInt32,
                role_id UInt32,
                client_id String,
                agent_ids Array(UInt32),
                event_ids Array(UInt32),
                event_category_ids Array(UInt32),
                total_calls_duration UInt32 DEFAULT 0,
                effective_calls_duration UInt32 DEFAULT 0,
                not_effective_calls_duration UInt32 DEFAULT 0,
                effective_calls_count UInt32 DEFAULT 0,
                not_effective_calls_count UInt32 DEFAULT 0,
                reviewed_calls_count UInt32 DEFAULT 0,
                reviewed_chats_count UInt32 DEFAULT 0,
                reviewed_forms_count UInt32 DEFAULT 0,
                approved_forms_count UInt32 DEFAULT 0,
                refused_forms_count UInt32 DEFAULT 0,
                score Int32 DEFAULT 0,
                risk_rank Float32 DEFAULT 0,
                last_call_time DateTime,
                client_name String,
                email Nullable(String) DEFAULT NULL,
                country Nullable(String) DEFAULT NULL,
                status Nullable(String) DEFAULT NULL,
                source Nullable(String) DEFAULT NULL,
                acquisition_date Nullable(DateTime) DEFAULT NULL,
                is_converted Bool DEFAULT false,
                converted_date Nullable(DateTime) DEFAULT NULL,
                last_transaction_date Nullable(DateTime) DEFAULT NULL,
                campaign_id Nullable(String) DEFAULT NULL,
                value Float32 DEFAULT 0,
                created DateTime64 DEFAULT now64()
            )
            ENGINE = ReplacingMergeTree(created)
            ORDER BY (
                date,
                company_id,
                role_id,
                client_id
            )
            SETTINGS index_granularity = 8192;
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE precalculated_clients
        ');
        $this->getClient()->write('
            CREATE TABLE precalculated_clients
            (
                date Date,
                company_id UInt32,
                role_id UInt32,
                client_id String,
                total_calls_duration UInt32 DEFAULT 0,
                effective_calls_duration UInt32 DEFAULT 0,
                not_effective_calls_duration UInt32 DEFAULT 0,
                effective_calls_count UInt32 DEFAULT 0,
                not_effective_calls_count UInt32 DEFAULT 0,
                reviewed_calls_count UInt32 DEFAULT 0,
                reviewed_chats_count UInt32 DEFAULT 0,
                reviewed_forms_count UInt32 DEFAULT 0,
                approved_forms_count UInt32 DEFAULT 0,
                refused_forms_count UInt32 DEFAULT 0,
                score Int32 DEFAULT 0,
                risk_rank Float32 DEFAULT 0,
                last_call_time DateTime,
                teams Nested(
                    id UInt32,
                    name String
                ),
                agents Nested(
                    id UInt32,
                    name String
                ),
                events Nested(
                    call_id String,
                    call_time DateTime,
                    paragraph_number UInt32,
                    paragraph_start_time UInt32,
                    paragraph_speaker_role Enum(
                        \'agent\' = 1,
                        \'client\' = 2,
                        \'unclear\' = 3
                    ),
                    category_id UInt32,
                    category_name String,
                    event_id UInt32,
                    event_name String,
                    event_text String,
                    event_en_text String,
                    event_highlight String,
                    event_en_highlight String,
                    event_icon String,
                    event_color_id UInt32,
                    event_fill_color_hex String,
                    event_outline_color_hex String
                ),
                client_name String,
                email Nullable(String) DEFAULT NULL,
                country Nullable(String) DEFAULT NULL,
                status Nullable(String) DEFAULT NULL,
                source Nullable(String) DEFAULT NULL,
                acquisition_date Nullable(DateTime) DEFAULT NULL,
                is_converted Bool DEFAULT false,
                converted_date Nullable(DateTime) DEFAULT NULL,
                last_transaction_date Nullable(DateTime) DEFAULT NULL,
                campaign_id Nullable(String) DEFAULT NULL,
                value Float32 DEFAULT 0,
                created DateTime64 DEFAULT now64()
            )
            ENGINE = ReplacingMergeTree(created)
            ORDER BY (
                date,
                company_id,
                role_id,
                client_id
            )
            SETTINGS index_granularity = 8192;
        ');
    }
}
