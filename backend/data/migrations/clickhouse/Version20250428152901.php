<?php

namespace Clickhouse\Migrations;

class Version20250428152901 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     *
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write(
            '
            ALTER TABLE
                calls
            MODIFY COLUMN
                sourced_original_file_names Array(String) DEFAULT []
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            MODIFY COLUMN
                topic_name String DEFAULT \'\'
        '
        );
    }

    /**
     *
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write(
            '
            ALTER TABLE
                calls
            MODIFY COLUMN
                sourced_original_file_names Array(String)
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            MODIFY COLUMN
                topic_name String
        '
        );
    }
}
