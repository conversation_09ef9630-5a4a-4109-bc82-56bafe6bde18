<?php

namespace Clickhouse\Migrations;

class Version20240311102515 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                paragraphs_count UInt32 DEFAULT 0
            AFTER
                call_duration
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                paragraphs_count
        ');
    }
}
