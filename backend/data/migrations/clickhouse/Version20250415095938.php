<?php

namespace Clickhouse\Migrations;

class Version20250415095938 extends \STClickhouse\Entity\Migration\BaseMigration
{
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE calls_summarizations 
            ADD COLUMN key_points String DEFAULT \'\',
            ADD COLUMN customer_sentiment String DEFAULT \'\',
            ADD COLUMN next_steps String DEFAULT \'\',
            ADD COLUMN primary_purpose String DEFAULT \'\',
            ADD COLUMN main_topics String DEFAULT \'\',
            ADD COLUMN customer_problems String DEFAULT \'\',
            ADD COLUMN key_action_items String DEFAULT \'\',
            ADD COLUMN business_opportunities String DEFAULT \'\',
            ADD COLUMN risks String DEFAULT \'\',
            ADD COLUMN conversation_type String DEFAULT \'\''
        );

        $this->getClient()->write(<<<SQL
            ALTER TABLE
                calls_summarizations
            UPDATE
                key_points = arrayStringConcat(extractAll(overview, '## Key Points\\n(.*?)\\n##'), ' '),
                customer_sentiment = arrayStringConcat(extractAll(overview, '## Customer Sentiment\\n(.*?)\\n##'), ' '),
                next_steps = arrayStringConcat(extractAll(overview, '## Next Steps\\n(.*?)($|\\n#)'), ' '),
                primary_purpose = arrayStringConcat(extractAll(details, '### Primary Purpose\\n(.*?)\\n##'), ' '),
                main_topics = arrayStringConcat(extractAll(details, '### Main Topics\n(.*?)\n###'), ' '),
                customer_problems = arrayStringConcat(extractAll(details, '### Customer\'s Problems\\n(.*?)\\n###'), ' '),
                key_action_items = arrayStringConcat(extractAll(details, '### Key Action Items\\n(.*?)\\n###'), ' '),
                business_opportunities = arrayStringConcat(extractAll(details, '### Business Opportunities\\n(.*?)($|\\n#)'), ' ')
            WHERE 1=1
        SQL);
    }

    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE calls_summarizations 
            DROP COLUMN key_points,
            DROP COLUMN customer_sentiment,
            DROP COLUMN next_steps,
            DROP COLUMN primary_purpose,
            DROP COLUMN main_topics,
            DROP COLUMN customer_problems,
            DROP COLUMN key_action_items,
            DROP COLUMN business_opportunities,
            DROP COLUMN risks,
            DROP COLUMN conversation_type
        ');
    }
}
