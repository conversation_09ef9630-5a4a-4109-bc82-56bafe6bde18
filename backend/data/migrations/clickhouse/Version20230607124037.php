<?php

namespace Clickhouse\Migrations;

class Version20230607124037 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_events_precalculated
            ADD COLUMN
                call_reviewers Array(Map(String, String))
            AFTER
                call_reviewers_names
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_events_precalculated
            DROP COLUMN
                call_reviewers
        ');
    }
}
