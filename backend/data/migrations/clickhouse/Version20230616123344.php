<?php

namespace Clickhouse\Migrations;

class Version20230616123344 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            RENAME TABLE calls_events_precalculated TO precalculated_calls_events
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            RENAME TABLE precalculated_calls_events TO calls_events_precalculated
        ');
    }
}
