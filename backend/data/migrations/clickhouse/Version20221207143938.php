<?php

namespace Clickhouse\Migrations;

class Version20221207143938 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_algo_events
            ADD COLUMN
                en_main_point_phrase String
            AFTER main_point_phrase
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_algo_events
            DROP COLUMN
                en_main_point_phrase
        ');
    }
}
