<?php

namespace Clickhouse\Migrations;

class Version20240402075226 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE
                dashboard_statistics_calls_1
        ');
        $this->getClient()->write('
            DROP TABLE
                dashboard_statistics_calls_2
        ');
        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_calls_1 (
                company_id UInt32,
                call_date Date,
                role_id Nullable(UInt32) DEFAULT NULL,
                team_ids Array(UInt32),
                calls_count UInt32,
                calls_duration UInt64,
                comments_count UInt32,
                reviewed_count UInt32,
                analyzed_count UInt32,
                effective_count UInt32,
                reviewed_duration UInt64,
                analyzed_duration UInt64,
                effective_duration UInt64,
                created_at DateTime
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date
            )
        ');
        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_calls_2 (
                company_id UInt32,
                call_date Date,
                role_id Nullable(UInt32) DEFAULT NULL,
                team_ids Array(UInt32),
                calls_count UInt32,
                calls_duration UInt64,
                comments_count UInt32,
                reviewed_count UInt32,
                analyzed_count UInt32,
                effective_count UInt32,
                reviewed_duration UInt64,
                analyzed_duration UInt64,
                effective_duration UInt64,
                created_at DateTime
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date
            )
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE
                dashboard_statistics_calls_1
        ');
        $this->getClient()->write('
            DROP TABLE
                dashboard_statistics_calls_2
        ');
        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_calls_1 (
                company_id UInt32,
                call_date Date,
                role_id UInt32,
                team_ids Array(UInt32),
                calls_count UInt32,
                calls_duration UInt64,
                comments_count UInt32,
                reviewed_count UInt32,
                analyzed_count UInt32,
                effective_count UInt32,
                reviewed_duration UInt64,
                analyzed_duration UInt64,
                effective_duration UInt64,
                created_at DateTime
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date,
                role_id
            )
        ');
        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_calls_2 (
                company_id UInt32,
                call_date Date,
                role_id UInt32,
                team_ids Array(UInt32),
                calls_count UInt32,
                calls_duration UInt64,
                comments_count UInt32,
                reviewed_count UInt32,
                analyzed_count UInt32,
                effective_count UInt32,
                reviewed_duration UInt64,
                analyzed_duration UInt64,
                effective_duration UInt64,
                created_at DateTime
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date,
                role_id
            )
        ');
    }
}
