<?php

namespace Clickhouse\Migrations;

class Version20230417114202 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                clients
            ADD COLUMN
                client_name String
            AFTER client_id
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                clients
            DROP COLUMN
                client_name
        ');
    }
}
