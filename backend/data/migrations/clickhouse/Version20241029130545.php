<?php

namespace Clickhouse\Migrations;

class Version20241029130545 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                 call_language Nullable(String) DEFAULT NULL
            AFTER
                call_origin
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                 call_type String DEFAULT \'call\'
            AFTER
                call_origin
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                 call_status Nullable(String) DEFAULT NULL
            AFTER
                call_language
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                 is_analyzed Bool DEFAULT 0
            AFTER
                call_status
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                 call_duration UInt32 DEFAULT 0
            AFTER
                call_status
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                 call_language
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                 call_type
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                 call_status
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                 is_analyzed
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                 call_duration
        ');
    }
}
