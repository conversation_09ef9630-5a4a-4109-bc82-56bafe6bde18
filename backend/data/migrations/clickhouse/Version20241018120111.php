<?php

namespace Clickhouse\Migrations;

class Version20241018120111 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     *
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                event_category_ids Array(UInt64)
            AFTER
                reviewer_user_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                event_ids Array(UInt64)
            AFTER
                reviewer_user_id
        ');
    }

    /**
     *
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                event_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                event_category_ids
        ');
    }
}
