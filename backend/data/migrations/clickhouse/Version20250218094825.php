<?php

namespace Clickhouse\Migrations;

use STClickhouse\Entity\Migration\BaseMigration;

class Version20250218094825 extends BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                is_summarization_completed Bool DEFAULT 0
            AFTER
                is_checklist_completed
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                 is_summarization_completed
        ');
    }
}
