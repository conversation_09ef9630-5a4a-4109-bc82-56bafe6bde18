<?php

namespace Clickhouse\Migrations;

class Version20241120110123 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            DROP COLUMN
                highlight
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            DROP COLUMN
                en_highlight
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            ADD COLUMN
                highlight Nullable(String) DEFAULT NULL
            AFTER
                en_text
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            ADD COLUMN
                en_highlight String DEFAULT \'\'
            AFTER
                en_text
        ');
    }
}
