<?php

namespace Clickhouse\Migrations;

class Version20231212113644 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                event_happenings_changes
            ADD COLUMN
                event_en_highlight Nullable(String) DEFAULT NULL
            AFTER
                user_id
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                event_happenings_changes
            DROP COLUMN
                event_en_highlight
        ');
    }
}
