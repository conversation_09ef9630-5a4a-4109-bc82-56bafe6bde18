<?php

namespace Clickhouse\Migrations;

class Version20231019155123 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE IF EXISTS migrations_disrtibuted
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            CREATE TABLE migrations_disrtibuted
            (
                `version` String,
                `apply_time` DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree
            ORDER BY version
            SETTINGS index_granularity = 8192;
        ');
    }
}
