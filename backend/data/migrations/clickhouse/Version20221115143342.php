<?php

namespace Clickhouse\Migrations;

class Version20221115143342 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                is_translated Bool DEFAULT 0
            AFTER is_transcribed
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                is_analyzed Bool DEFAULT 0
            AFTER is_translated
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                is_translated
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                is_analyzed
        ');
    }
}
