<?php

namespace Clickhouse\Migrations;

class Version20230613105012 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated
            ADD COLUMN
                comments Array(Map(String, String))
            AFTER
                risk_rank
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated
            ADD COLUMN
                reviewers Array(Map(String, String))
            AFTER
                risk_rank
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated
            DROP COLUMN
                comments
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated
            DROP COLUMN
                reviewers
        ');
    }
}
