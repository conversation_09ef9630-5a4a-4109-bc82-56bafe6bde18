<?php

namespace Clickhouse\Migrations;

class Version20240517122707 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE ems_data_sets (
                ems_data_set_id String,
                company_id UInt32,
                event_id Nullable(UInt64),
                name String,
                guideline String DEFAULT NULL,
                status String,
                created_at DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree()
            ORDER BY (
                ems_data_set_id
            )
        ');
        $this->getClient()->write('
            CREATE TABLE ems_data_set_examples (
                ems_data_set_example_id String,
                ems_data_set_id String,
                text String,
                highlight String,
                language String,
                call_id Nullable(String) DEFAULT NULL,
                paragraph Nullable(UInt32) DEFAULT NULL,
                paragraph_start_time Nullable(UInt32) DEFAULT NULL,
                status String,
                created_at DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree()
            ORDER BY (
                ems_data_set_example_id,
                ems_data_set_id
            )
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE ems_data_sets
        ');
        $this->getClient()->write('
            DROP TABLE ems_data_set_examples
        ');
    }
}
