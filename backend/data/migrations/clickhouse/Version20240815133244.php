<?php

namespace Clickhouse\Migrations;

class Version20240815133244 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            ADD COLUMN
                positive_example_text String DEFAULT \'\'
            AFTER
                guideline
        ');
        $this->getClient()->write('
              ALTER TABLE
                ems_data_sets
            ADD COLUMN
                positive_example_highlight String DEFAULT \'\'
            AFTER
                guideline
        ');
        $this->getClient()->write('
              ALTER TABLE
                ems_data_sets
            ADD COLUMN
                negative_example_text String DEFAULT \'\'
            AFTER
                guideline
        ');

        $this->getClient()->write('
            ALTER TABLE ems_data_sets 
                RENAME COLUMN confirmed_examples_last_update TO reviewed_calls_examples_last_update
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            ADD COLUMN
                analyzed_calls_examples_last_update Nullable(DATETIME32) DEFAULT NULL
            AFTER
                status
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            DROP COLUMN
                neutral_examples_last_update
        ');
        $this->getClient()->write('
             ALTER TABLE
                ems_data_set_examples
             MODIFY COLUMN
                example_source Nullable(Enum(\'confirmed\', \'neutral\', \'custom\', \'analyzed_calls_confirmed\', \'analyzed_calls_neutral\')) DEFAULT \'confirmed\'

        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE ems_data_sets 
                RENAME COLUMN reviewed_calls_examples_last_update TO confirmed_examples_last_update
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            ADD COLUMN
                neutral_examples_last_update Nullable(DATETIME32) DEFAULT NULL
            AFTER
                status
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            DROP COLUMN
                analyzed_calls_examples_last_update
        ');

        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            DROP COLUMN
                positive_example_text
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            DROP COLUMN
                positive_example_highlight
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            DROP COLUMN
                negative_example_text
        ');
    }
}
