<?php

namespace Clickhouse\Migrations;

class Version20221026163652 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls_paragraphs (
                company_id UInt32,
                call_id String,
                call_time DateTime DEFAULT now(),
                paragraph_number UInt16,
                speaker_number Nullable(UInt8),
                start_time Float32,
                end_time Float32,
                text String,
                en_text Nullable(String),
                created DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY toYYYYMM(call_time)
            ORDER BY (
                company_id,
                call_id,
                paragraph_number
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE calls_paragraphs
        ');
    }
}
