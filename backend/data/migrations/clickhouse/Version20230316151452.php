<?php

namespace Clickhouse\Migrations;

class Version20230316151452 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls_comments (
                comment_id String,
                company_id UInt32,
                user_id UInt32,
                call_id String,
                message_body String,
                created DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY toYYYYMM(created)
            ORDER BY (
                comment_id
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE IF EXISTS
                calls_comments
        ');
    }
}
