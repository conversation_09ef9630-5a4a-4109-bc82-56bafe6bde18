<?php

namespace Clickhouse\Migrations;

class Version20250117064508 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                is_checklist_completed Bool DEFAULT 0
            AFTER
                is_translated
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                 is_checklist_completed
        ');
    }
}
