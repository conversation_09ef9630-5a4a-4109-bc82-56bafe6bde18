<?php

namespace Clickhouse\Migrations;

class Version20240225150352 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN IF EXISTS
                is_fully_reviewed
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN IF NOT EXISTS
                is_fully_reviewed Bool DEFAULT 0
            AFTER is_reviewed
        ');
    }
}
