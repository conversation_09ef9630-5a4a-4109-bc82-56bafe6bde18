<?php

namespace Clickhouse\Migrations;

class Version20230621093425 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                fragments Array(Map(String, String))
            AFTER
                comments
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                fragments
        ');
    }
}
