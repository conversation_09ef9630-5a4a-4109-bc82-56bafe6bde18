<?php

namespace Clickhouse\Migrations;

class Version20240314173338 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                sourced_original_file_names Array(String)
            AFTER
                original_file_name
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                sourced_original_file_names
        ');
    }
}
