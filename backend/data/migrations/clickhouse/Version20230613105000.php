<?php

namespace Clickhouse\Migrations;

class Version20230613105000 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            RENAME TABLE calls_precalculated_values TO calls_precalculated
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            RENAME TABLE calls_precalculated TO calls_precalculated_values
        ');
    }
}
