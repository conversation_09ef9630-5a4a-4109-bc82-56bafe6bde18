<?php

namespace Clickhouse\Migrations;

class Version20230605125224 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_events_precalculated
            ADD COLUMN
                paragraph_start_time UInt32 DEFAULT 0
            AFTER paragraph
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_events_precalculated
            DROP COLUMN
                paragraph_start_time
        ');
    }
}
