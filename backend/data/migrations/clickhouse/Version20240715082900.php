<?php

namespace Clickhouse\Migrations;

class Version20240715082900 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            ADD COLUMN
                example_source Nullable(Enum(\'confirmed\', \'neutral\', \'custom\')) DEFAULT \'confirmed\'
            AFTER
                status
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            ADD COLUMN
                neutral_examples_last_automatic_update Nullable(DATETIME32) DEFAULT NULL
            AFTER
                last_automatic_update
        ');
        $this->getClient()->write('
            ALTER TABLE ems_data_sets
                RENAME COLUMN last_automatic_update TO confirmed_examples_last_automatic_update
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            DROP COLUMN
                example_source
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            DROP COLUMN
                neutral_examples_last_automatic_update
        ');
        $this->getClient()->write('
            ALTER TABLE ems_data_sets
                RENAME COLUMN confirmed_examples_last_automatic_update TO last_automatic_update
        ');
    }
}
