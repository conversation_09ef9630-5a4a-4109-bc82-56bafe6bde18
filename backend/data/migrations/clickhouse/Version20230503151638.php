<?php

namespace Clickhouse\Migrations;

class Version20230503151638 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated_values
            ADD COLUMN
                reviewed_time Nullable(DateTime)
            AFTER is_reviewed
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_precalculated_values
            DROP COLUMN
                reviewed_time
        ');
    }
}
