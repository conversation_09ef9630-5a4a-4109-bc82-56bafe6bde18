<?php

namespace Clickhouse\Migrations;

class Version20240603094848 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_sets
            ADD COLUMN
                user_id UInt128
            AFTER
                company_id
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
             ALTER TABLE
                ems_data_sets
            DROP COLUMN
                user_id
        ');
    }
}
