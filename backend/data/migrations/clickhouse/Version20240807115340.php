<?php

namespace Clickhouse\Migrations;

class Version20240807115340 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            ADD COLUMN
                en_text String DEFAULT \'\'
            AFTER
                text
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            ADD COLUMN
                en_highlight String DEFAULT \'\'
            AFTER
                highlight
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            DROP COLUMN
                en_text
        ');
        $this->getClient()->write('
            ALTER TABLE
                ems_data_set_examples
            DROP COLUMN
                en_highlight
        ');
    }
}
