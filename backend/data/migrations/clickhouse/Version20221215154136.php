<?php

namespace Clickhouse\Migrations;

class Version20221215154136 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {

        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                uploaded_user_id Nullable(UInt128) DEFAULT NULL
            AFTER s3_file_path
        ');

        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                uploaded_time DateTime DEFAULT now()
            AFTER uploaded_user_id
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                uploaded_user_id
        ');
        
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                uploaded_time
        ');
    }
}
