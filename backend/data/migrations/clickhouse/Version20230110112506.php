<?php

namespace Clickhouse\Migrations;

class Version20230110112506 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls_precalculated_values (
                company_id UInt32,
                call_id String,
                role_id UInt32,
                is_reviewed Bool DEFAULT 0,
                score Int32 DEFAULT 0,
                created DateTime DEFAULT now()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY company_id
            ORDER BY (
                company_id,
                call_id,
                role_id
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE calls_precalculated_values
        ');
    }
}
