<?php

namespace Clickhouse\Migrations;

class Version20250312104607 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                is_llm_events_detected Bool DEFAULT 0
            AFTER
                is_speakers_roles_detected
        ');

        $this->getClient()->write('
            ALTER TABLE
              calls
            UPDATE
              is_llm_events_detected = 1
            WHERE
              is_analyzed = 1
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                 is_llm_events_detected
        ');
    }
}
