<?php

namespace Clickhouse\Migrations;

class Version20250502111402 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_agents
            DROP COLUMN IF EXISTS total_forms_count
            SETTINGS max_execution_time=30000
        ');

        $this->getClient()->write('
            ALTER TABLE
                precalculated_agents
            DROP COLUMN IF EXISTS reviewed_forms_count
            SETTINGS max_execution_time=30000
        ');

        $this->getClient()->write('
            ALTER TABLE
                precalculated_agents
            DROP COLUMN IF EXISTS approved_forms_count
            SETTINGS max_execution_time=30000
        ');

        $this->getClient()->write('
            ALTER TABLE
                precalculated_agents
            DROP COLUMN IF EXISTS refused_forms_count
            SETTINGS max_execution_time=30000
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_agents
            ADD COLUMN
                total_forms_count UInt32 DEFAULT 0,
            ADD COLUMN
                reviewed_forms_count UInt32 DEFAULT 0,
            ADD COLUMN
                approved_forms_count UInt32 DEFAULT 0,
            ADD COLUMN
                refused_forms_count UInt32 DEFAULT 0
        ');
    }
}
