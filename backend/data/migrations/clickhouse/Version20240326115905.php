<?php

namespace Clickhouse\Migrations;

class Version20240326115905 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                is_approved Nullable(Bool) DEFAULT NULL
            AFTER
                is_reviewed
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                is_approved
        ');
    }
}
