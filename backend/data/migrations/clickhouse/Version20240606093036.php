<?php

namespace Clickhouse\Migrations;

class Version20240606093036 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
             ALTER TABLE
                ems_data_sets
             ADD COLUMN
                updated_at DateTime DEFAULT now()
             AFTER
                created_at
        ');
        $this->getClient()->write('
             ALTER TABLE
                ems_data_set_examples
             ADD COLUMN
                updated_at DateTime DEFAULT now()
             AFTER
                created_at
        ');
        $this->getClient()->write('
             ALTER TABLE
                ems_data_set_examples
             MODIFY COLUMN
                highlight Nullable(String) DEFAULT NULL
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
             ALTER TABLE
                ems_data_sets
             DROP COLUMN
                updated_at
        ');
        $this->getClient()->write('
             ALTER TABLE
                ems_data_set_examples
             DROP COLUMN
                updated_at
        ');
        $this->getClient()->write('
             ALTER TABLE
                ems_data_set_examples
             MODIFY COLUMN
                highlight String DEFAULT \'\'
        ');
    }
}
