<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250304134821 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->addColumn('is_export_enabled', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
        $table->addColumn('last_export_date', 'datetime', [
            'notnull' => false,
            'default' => null,
        ]);
        $table->addColumn('aws_s3_export_bucket_name', 'string', [
            'notnull' => false,
            'length' => 60,
        ]);
        $table->addColumn('aws_s3_export_bucket_region', 'string', [
            'notnull' => false,
            'length' => 60,
        ]);
        $table->addColumn('aws_s3_export_bucket_dir', 'string', [
            'notnull' => false,
            'length' => 60,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->dropColumn('aws_s3_export_bucket_name');
        $table->dropColumn('aws_s3_export_bucket_region');
        $table->dropColumn('aws_s3_export_bucket_dir');
        $table->dropColumn('last_export_date');
        $table->dropColumn('is_export_enabled');
    }
}
