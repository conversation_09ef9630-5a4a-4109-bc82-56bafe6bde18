<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240523105033 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        // add back only possibity actual APIs
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis
                (path, algo_api_category_id)
            VALUES
                ("https://ai-solutions.robonote.io/api/adapter_8_16/v17", 2),
                ("https://ai-solutions.robonote.io/api/adapter_16_32/v17", 2)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                algo_apis
            WHERE
                path IN (
                "https://ai-solutions.robonote.io/api/adapter_8_16/v17",
                "https://ai-solutions.robonote.io/api/adapter_16_32/v17"
            )
        ');
    }

}
