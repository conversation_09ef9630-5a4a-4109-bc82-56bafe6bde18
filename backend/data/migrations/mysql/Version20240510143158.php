<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240510143158 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                algo_apis
            WHERE
                path NOT LIKE "https://ai-solutions.robonote.io%"
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        // add back only possibity actual APIs
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis
                (path, algo_api_category_id)
            VALUES
                ("https://algo2.robonote.io/api/v3", 1),
                ("https://algo2.robonote.io/api/v4", 1),
                ("https://algo2.robonote.io/api/v5", 1),
                ("https://algo2.robonote.io/api/v6", 3),
                ("https://algo2.robonote.io/api/v7", 2),
                ("https://algo2.robonote.io/api/v8", 1)
        ');
    }

}
