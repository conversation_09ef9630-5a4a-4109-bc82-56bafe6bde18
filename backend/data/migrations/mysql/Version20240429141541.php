<?php

declare(strict_types=1);

namespace Migrations;

use <PERSON>trine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240429141541 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                companies_call_templates
            RENAME COLUMN
                agent_number TO agent_id_number
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                companies_call_templates
            RENAME COLUMN
                client_number TO client_id_number
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                companies_call_templates
            RENAME COLUMN
                agent_id_number TO agent_number
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                companies_call_templates
            RENAME COLUMN
                client_id_number TO client_number
        ');
    }

}
