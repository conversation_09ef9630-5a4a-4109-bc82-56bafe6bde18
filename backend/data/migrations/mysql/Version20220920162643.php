<?php

declare(strict_types=1);

namespace Migrations;

use <PERSON>trine\DBAL\Schema\Schema;
use <PERSON>trine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220920162643 extends AbstractMigration
{
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $this->connection->executeQuery('
            DELETE FROM features
        ');
        $this->connection->executeQuery('
            INSERT INTO
                features
            VALUES
                (1, "Company", null),
                (2, "Dashboard", null),
                (3, "Call statistics", null),
                (4, "Severe violations", null),
                (5, "Clients", null),
                (6, "Manager statistics", null),
                (7, "Users", null),
                (8, "Iconery", null),
                (9, "Payments", 1),
                (10, "Profile", 1),
                (11, "Users", 1),
                (12, "Teams", 1),
                (13, "Calls overview", 2),
                (14, "Flags", 2),
                (15, "Agents", 2),
                (16, "Clients", 2),
                (17, "Group name", 3),
                (18, "Flag statistics", 3),
                (19, "Time on phone", 3)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $this->connection->executeQuery('
            DELETE FROM features
        ');
        $this->connection->executeQuery('
            INSERT INTO
                features
                (
                    feature_id,
                    feature_name,
                    parent_feature_id
                )
            VALUES
                (1, "Dashboard", null),
                (2, "Call statistics", null),
                (3, "Severe violations", null),
                (4, "Clients", null),
                (5, "Manager statistics", null),
                (6, "Users", null),
                (7, "Payments", null),
                (8, "Iconery", null),
                (9, "Calls overview", 1),
                (10, "Flags", 1),
                (11, "Agents", 1),
                (12, "Clients", 1),
                (13, "Group name", 2),
                (14, "Flag statistics", 2),
                (15, "Time on phone", 2)
        ');
    }
    
}
