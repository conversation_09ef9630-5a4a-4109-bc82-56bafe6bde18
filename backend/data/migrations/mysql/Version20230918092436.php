<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230918092436 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                algo_api_category_id = 7
            WHERE
                path IN ("https://g4dn.robonote.io/api/v12")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                algo_api_category_id = 3
            WHERE
                path IN ("https://g4dn.robonote.io/api/v12")
        ');
    }

}
