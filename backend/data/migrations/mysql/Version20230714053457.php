<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230714053457 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $eventTable = $schema->getTable('events');
        $this->connection->executeQuery('
            DELETE FROM 
                events
            WHERE
                category_id IS NULL
        ');

        $eventTable->modifyColumn('category_id', [
            'notnull' => true,
            'unsigned' => true,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $eventTable = $schema->getTable('events');
        $eventTable->modifyColumn('category_id', [
            'notnull' => false,
            'unsigned' => true,
        ]);
    }
}
