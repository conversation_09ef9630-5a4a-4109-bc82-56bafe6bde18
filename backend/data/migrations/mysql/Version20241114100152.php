<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241114100152 extends AbstractMigration
{
    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('llm_events');
        $table->addColumn('id', 'bigint', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->setPrimaryKey(['id']);

        $table->addColumn('name', 'string', ['notnull' => true, 'length' => 255]);
        $table->addColumn('description', 'string', ['notnull' => true, 'length' => 255]);
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('llm_events');
    }
}
