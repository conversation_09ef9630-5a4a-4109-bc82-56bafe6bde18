<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230310095057 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('roles_displayed_columns');
        
        $table->addColumn('role_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('column', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->addColumn('sort', 'integer', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);

        $table->setPrimaryKey([
            'role_id',
            'column',
        ]);

        $table->addForeignKeyConstraint('roles', ['role_id'], ['role_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('roles_displayed_columns');
    }

}
