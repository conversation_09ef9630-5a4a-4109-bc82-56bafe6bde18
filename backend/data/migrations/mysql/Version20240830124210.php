<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240830124210 extends AbstractMigration {

    protected const ALGO_API_REMANES = [
        'https://ai-solutions.robonote.io/api/adapter_8_16/c25v11' => 'https://ai-solutions.robonote.io/api/c25v11/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/c6v12' => 'https://ai-solutions.robonote.io/api/c6v12/v1',
        'https://ai-solutions.robonote.io/api/common/v1' => 'https://ai-solutions.robonote.io/api/common/v1',
        'https://ai-solutions.robonote.io/api/liquinix/v1' => 'https://ai-solutions.robonote.io/api/liquinix/v1',
        'https://ai-solutions.robonote.io/api/lqi/v1' => 'https://ai-solutions.robonote.io/api/lqi/v1',
        'https://ai-solutions.robonote.io/api/mbac/v1' => 'https://ai-solutions.robonote.io/api/mbac/v1',
        'https://ai-solutions.robonote.io/api/objection/v2' => 'https://ai-solutions.robonote.io/api/objection/v3',
        'https://ai-solutions.robonote.io/api/sharktec/v1' => 'https://ai-solutions.robonote.io/api/sharktec/v1',
        'https://ai-solutions.robonote.io/api/tesla/v1' => 'https://ai-solutions.robonote.io/api/tesla/v1',
        'https://ai-solutions.robonote.io/api/travel/v1' => 'https://ai-solutions.robonote.io/api/travel/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v12' => 'https://ai-solutions.robonote.io/api/v12/v1',
        'https://ai-solutions.robonote.io/api/adapter_8_16/v11' => 'https://ai-solutions.robonote.io/api/v11/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v18' => 'https://ai-solutions.robonote.io/api/v18/v1',
        'https://ai-solutions.robonote.io/api/adapter_8_16/v14' => 'https://ai-solutions.robonote.io/api/v14/v1',
        'https://ai-solutions.robonote.io/api/vivavida/v1' => 'https://ai-solutions.robonote.io/api/vivavida/v1',
        'https://ai-solutions.robonote.io/api/adapter_8_16/v17' => 'https://ai-solutions.robonote.io/api/v17/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v17' => 'https://ai-solutions.robonote.io/api/v17/v2',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v23' => 'https://ai-solutions.robonote.io/api/v23/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v22' => 'https://ai-solutions.robonote.io/api/v22/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v8' => 'https://ai-solutions.robonote.io/api/v8/v1',
        'https://ai-solutions.robonote.io/api/adapter_8_16/v5' => 'https://ai-solutions.robonote.io/api/v5/v1',
        'https://ai-solutions.robonote.io/api/adapter_8_16/v7' => 'https://ai-solutions.robonote.io/api/v7/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v9' => 'https://ai-solutions.robonote.io/api/v9/v1',
        'https://ai-solutions.robonote.io/api/adapter_16_32/v21' => 'https://ai-solutions.robonote.io/api/loop/v2',
        'https://ai-solutions.robonote.io/api/mbac/v2' => 'https://ai-solutions.robonote.io/api/mbac/v2',
        'https://ai-solutions.robonote.io/api/lqiems/v1' => 'https://ai-solutions.robonote.io/api/lqi/v2',
        'https://ai-solutions.robonote.io/api/objection/v3' => 'https://ai-solutions.robonote.io/api/objection/v3',
    ];

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        foreach (self::ALGO_API_REMANES as $oldApiName => $newApiName) {
            $this->connection->executeQuery('
                UPDATE 
                    algo_apis 
                SET
                    path = "' . $newApiName .  '"
                WHERE
                    path = "' . $oldApiName .  '"
            ');
        }
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        foreach (self::ALGO_API_REMANES as $oldApiName => $newApiName) {
            $this->connection->executeQuery('
                UPDATE 
                    algo_apis 
                SET
                    path = "' . $oldApiName .  '"
                WHERE
                    path = "' . $newApiName .  '"
            ');
        }
    }

}
