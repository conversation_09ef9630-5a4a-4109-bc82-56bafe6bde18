<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240108114449 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('users_notifications_settings');

        $table->addColumn('user_notification_setting_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
            'autoincrement' => true,
        ]);
        $table->addColumn('user_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('type', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->addColumn('delivery_types', 'json', [
            'notnull' => true,
        ]);
        $table->setPrimaryKey([
            'user_notification_setting_id',
        ]);

        $table->addForeignKeyConstraint('users', ['user_id'], ['user_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('users_notifications_settings');
    }
}
