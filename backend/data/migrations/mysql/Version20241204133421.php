<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241204133421 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('companies_industries');

        $table->addColumn('company_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addColumn('industry_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addForeignKeyConstraint('industries', ['industry_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('companies_industries');
    }
}
