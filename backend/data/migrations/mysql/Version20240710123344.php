<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240710123344 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                users_companies_roles
            WHERE
                role_id IS NULL
        ');

        $table = $schema->getTable('users_companies_roles');
        $table->modifyColumn('role_id', [
            'notnull' => true,
        ]);

        $table->removeForeignKey('FK_EEC29EB9D60322AC');
        $table->addForeignKeyConstraint('roles', ['role_id'], ['role_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('users_companies_roles');
        $table->modifyColumn('role_id', [
            'notnull' => false,
        ]);

        $table->removeForeignKey('FK_EEC29EB9D60322AC');
        $table->addForeignKeyConstraint('roles', ['role_id'], ['role_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'SET NULL',
        ]);
    }

}
