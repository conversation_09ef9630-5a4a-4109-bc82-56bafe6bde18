<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221118120337 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('events_categories');
        $table->addColumn('category_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
            'autoincrement' => true,
        ]);
        $table->addColumn('role_id', 'bigint', [
            'notnull' => false,
            'unsigned' => true,
        ]);
        $table->addColumn('category_name', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->addColumn('color_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        
        $table->addForeignKeyConstraint('roles', ['role_id'], ['role_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
        
        $table->addForeignKeyConstraint('events_colors', ['color_id'], ['color_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
        
        $table->addUniqueIndex([
            'role_id',
            'category_name',
        ]);
        
        $table->setPrimaryKey([
            'category_id',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('events_categories');
    }

}
