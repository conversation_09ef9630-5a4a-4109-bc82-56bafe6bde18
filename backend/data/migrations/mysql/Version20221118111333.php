<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221118111333 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('events');
        $table->dropColumn('color');
        $table->dropColumn('is_algo_event_copy');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('events');
        $table->addColumn('is_algo_event_copy', 'boolean', [
            'notnull' => true,
            'default' => 0,
            'unsigned' => true,
        ]);
        $table->addColumn('color', 'string', [
            'notnull' => true,
        ]);
    }

}
