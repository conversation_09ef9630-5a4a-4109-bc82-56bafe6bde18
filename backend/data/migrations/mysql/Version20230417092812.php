<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230417092812 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('roles_displayed_columns');
        $table->dropPrimaryKey();
        $table->setPrimaryKey([
            'role_id',
            'report',
            'column',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('roles_displayed_columns');
        $table->dropPrimaryKey();
        $table->setPrimaryKey([
            'role_id',
            'column',
        ]);
    }

}
