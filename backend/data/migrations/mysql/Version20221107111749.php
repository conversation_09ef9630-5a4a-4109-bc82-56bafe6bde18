<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221107111749 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('events');
        $table->addUniqueIndex([
            'company_id',
            'event_name',
            'is_algo_event_copy',
        ], 'UNIQ_5387574A979B1AD641E832AD27583C94');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('events');
        $table->dropIndex('UNIQ_5387574A979B1AD641E832AD27583C94');
    }

}
