<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230421150103 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis
                (path, algo_api_category_id)
            VALUES
                ("https://algo2.robonote.io/api/v12", 1)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE FROM
                algo_apis
            WHERE
                path = "https://algo2.robonote.io/api/v12"
        ');
    }

}
