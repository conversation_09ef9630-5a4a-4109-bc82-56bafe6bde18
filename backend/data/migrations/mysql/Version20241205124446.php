<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241205124446 extends AbstractMigration {

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws SchemaException
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('llm_events');
        $table->renameColumn('id', 'llm_event_id');
    }

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws SchemaException
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('llm_events');
        $table->renameColumn('llm_event_id', 'id');
    }
}
