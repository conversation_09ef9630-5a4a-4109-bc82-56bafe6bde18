<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240515112022 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $schema->dropTable('client_crm_integration');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->createTable('client_crm_integration');
        $table->addColumn('integration_id', 'integer', [
            'autoincrement' => true,
            'notnull' => true,
            'unsigned' => true,
        ]);

        $table->addColumn('company_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        
        $table->addColumn('driver', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        
        $table->addColumn('database', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        
        $table->addColumn('username', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        
        $table->addColumn('password', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        
        $table->addColumn('hostname', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        $table->addColumn('table', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        
        $table->addColumn('port', 'integer', [
            'notnull' => true,
        ]);

        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->setPrimaryKey([
            'integration_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

}
