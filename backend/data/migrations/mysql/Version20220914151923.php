<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220914151923 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->createTable('users');
        $table->addColumn('user_id', 'bigint', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('user_name', 'string', [
            'notnull' => false,
            'length' => 255,
        ]);
        $table->addColumn('user_email', 'string', [
            'notnull' => false,
            'length' => 255,
        ]);
        $table->addColumn('user_avatar', 'text', [
            'notnull' => false,
            'length' => 65535,
        ]);
        $table->addColumn('user_password', 'string', [
            'notnull' => false,
            'length' => 255,
        ]);
        $table->addColumn('user_two_factor_secret', 'string', [
            'notnull' => false,
            'length' => 63,
        ]);
        $table->addColumn('registration_date', 'datetime', [
            'default' => 'CURRENT_TIMESTAMP',
        ]);

        $table->setPrimaryKey([
            'user_id',
        ]);
        
        $table->addUniqueIndex([
            'user_email',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $schema->dropTable('users');
    }

}
