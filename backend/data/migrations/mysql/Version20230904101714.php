<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230904101714 extends AbstractMigration {
    private const TABLE = 'transcribe_drivers_languages';
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        // to ignore unique key
        $this->connection->executeQuery('
            INSERT INTO
                ' . self::TABLE . '
            VALUES
                ("en", "deepgram"),
                ("fr", "deepgram"),
                ("de", "deepgram"),
                ("hi", "deepgram"),
                ("it", "deepgram"),
                ("ja", "deepgram"),
                ("ko", "deepgram"),
                ("pl", "deepgram"),
                ("pt", "deepgram"),
                ("es", "deepgram"),
                ("sv", "deepgram"),
                ("uk", "deepgram"),
                ("nl", "deepgram"),
                ("es-419", "deepgram"),
                ("en-AU", "deepgram"),
                ("en-GB", "deepgram"),
                ("en-IN", "deepgram"),
                ("en-NZ", "deepgram"),
                ("en-US", "deepgram"),
                ("fr-CA", "deepgram"),
                ("hi-Latn", "deepgram"),
                ("pt-BR", "deepgram"),
                ("pt-PT", "deepgram"),
                ("ar", "wordcab"),
                ("az", "wordcab"),
                ("he", "wordcab"),
                ("id", "wordcab"),
                ("hr", "wordcab"),
                ("ru", "wordcab"),
                ("tr", "wordcab"),
                ("sl", "wordcab"),
                ("cs", "wordcab"),
                ("th", "wordcab")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {

    }

}
