<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250401090951 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery(<<<SQL
            ALTER TABLE
                checklists
            ADD COLUMN
                calls_scope ENUM('all_calls', 'first_calls') NOT NULL DEFAULT 'all_calls'
            AFTER
                name;
        SQL);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('checklists');
        $table->dropColumn('calls_scope');
    }

}
