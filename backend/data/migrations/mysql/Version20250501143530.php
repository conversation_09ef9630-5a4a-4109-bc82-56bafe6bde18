<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250501143530 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $schema->dropTable('companies_emotional_api_thresholds');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->createTable('companies_emotional_api_thresholds');
        $table->addColumn('company_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('emotion', 'string', [
            'notnull' => true,
            'length' => 63,
        ]);
        $table->addColumn('threshold', 'decimal', [
            'type' => new \Doctrine\DBAL\Types\DecimalType(),
            'notnull' => true,
            'precision' => 5,
            'scale' => 2,
            'default' => 0.8,
        ]);
        $table->setPrimaryKey([
            'company_id',
            'emotion',
        ]);

        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }
}
