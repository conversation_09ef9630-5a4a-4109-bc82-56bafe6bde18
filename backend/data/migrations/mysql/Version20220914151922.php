<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220914151922 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->createTable('reports');
        $table->addColumn('report_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('report_name', 'string', [
            'notnull' => false,
            'length' => 255,
        ]);

        $table->setPrimaryKey([
            'report_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            INSERT INTO
                reports
            VALUES
                (1, "Admin"),
                (2, "QC Manager"),
                (3, "Sales Manager"),
                (4, "Agent"),
                (5, "Severe Violations"),
                (6, "Clients")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $schema->dropTable('reports');
    }

}
