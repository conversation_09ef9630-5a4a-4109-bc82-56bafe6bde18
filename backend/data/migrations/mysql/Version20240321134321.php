<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240321134321 extends AbstractMigration {

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('translation_drivers_languages');
        $table->addColumn('language', 'string', [
            'notnull' => true,
            'length' => 63,
        ]);
        $table->addColumn('driver', 'string', [
            'notnull' => true,
            'length' => 63,
        ]);
        $table->setPrimaryKey([
            'language',
            'driver',
        ]);
        $table->addUniqueIndex([
            'language',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('translation_drivers_languages');
    }
}
