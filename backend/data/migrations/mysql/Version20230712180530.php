<?php

declare(strict_types=1);

namespace Migrations;

use <PERSON>trine\DBAL\Schema\Schema;
use <PERSON>trine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230712180530 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery("
            INSERT INTO
                permissions
                (
                    permission_id,
                    parent_permission_id,
                    permission_name,
                    system_name,
                    is_read_permission_only,
                    is_write_permission_only
                )
            VALUES
                (23,NULL,'Reports','reports', 1, 0),
                (24,NULL,'Search','search', 1, 0),
                (25,NULL,'Call','call', 0, 0),
                (26,25,'Review call','review_call', 0, 1),
                (27,25,'Comments','comments', 0, 1),
                (28,25,'Speakers','speakers', 0, 1),
                (29,25,'Paragraphs','paragraphs', 0, 1)
        ");
        $this->connection->executeQuery("
            UPDATE
                permissions
            SET
                is_read_permission_only = 1
            WHERE
                permission_id IN (3,5)
        ");
        $this->connection->executeQuery("
            UPDATE
                permissions
            SET
                parent_permission_id = NULL
            WHERE
                permission_id IN (20, 21, 22)
        ");
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery("
            DELETE
            FROM
                permissions
            WHERE
                permission_id IN (23,24,25,26,27,28,29,30)
        ");
        $this->connection->executeQuery("
            UPDATE
                permissions
            SET
                parent_permission_id = 1
            WHERE
                permission_id IN (20, 21, 22)
        ");
    }

}
