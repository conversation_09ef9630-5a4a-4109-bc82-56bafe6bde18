<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230713131925 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        foreach ([23,24,25,26,27,28,29] as $permissionId) {
            $this->connection->executeQuery('
                INSERT INTO
                    roles_permissions
                    (
                        role_id,
                        permission_id,
                        access_level
                    )
                SELECT
                    role_id,
                    ' . $permissionId . ' permission_id,
                    \'write\' access_level
                FROM 
                    roles
            ');
        }
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                roles_permissions
            WHERE
                permission_id IN (23,24,25,26,27,28,29,30)
        ');
    }

}
