<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230125111712 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('client_crm_integration_mapper');
        $table->addColumn('integration_id', 'integer', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        
        $table->addColumn('original_column', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        
        $table->addColumn('target_column', 'string', [
            'notnull' => true,
            'length' => 127,
        ]);
        
        $table->addForeignKeyConstraint('client_crm_integration', ['integration_id'], ['integration_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
        
        $table->setPrimaryKey([
            'integration_id',
            'original_column',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('client_crm_integration_mapper');
    }

}
