<?php

declare(strict_types=1);

namespace STRabbit\Entity;

abstract class AbstractDaemon
{
    /**
     *
     * @var int
     */
    protected int $attemptsNumber = 3;

    /**
     *
     * @var bool
     */
    protected bool $doNotMakeNextAttempt = false;

    /**
     *
     * @var bool
     */
    protected bool $doNotAddToErrorQueue = false;

    /**
     *
     * @var \STLib\Expand\Collection|null
     */
    protected ?\STLib\Expand\Collection $params = null;

    /**
     *
     * @var string
     */
    protected string $exchangeName = '';

    /**
     *
     * @var string|null
     */
    protected ?string $queueName = null;

    /**
     *
     * @var string|null
     */
    protected ?string $errorQueueName = null;

    /**
     *
     * @var int
     */
    protected int $delay = 0;

    /**
     *
     * @var \PhpAmqpLib\Channel\AMQPChannel|null
     */
    protected ?\PhpAmqpLib\Channel\AMQPChannel $channel = null;

    /**
     *
     * @return int
     */
    public function getAttemptsNumber(): int
    {
        return $this->attemptsNumber;
    }

    /**
     *
     * @return string
     */
    public function getExchangeName(): string
    {
        return $this->exchangeName;
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return $this->queueName;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return $this->errorQueueName;
    }

    /**
     *
     * @return int
     */
    public function getDelay(): int
    {
        return $this->delay;
    }

    /**
     *
     * @return \PhpAmqpLib\Channel\AMQPChannel
     */
    public function getChannel(): \PhpAmqpLib\Channel\AMQPChannel
    {
        return $this->channel;
    }

    /**
     *
     * @param int $attemptsNumber
     * @return \STLib\Daemon\AbstractDaemon
     */
    public function setAttemptsNumber(int $attemptsNumber): AbstractDaemon
    {
        $this->attemptsNumber = $attemptsNumber;
        return $this;
    }

    /**
     *
     * @param string $exchangeName
     * @return AbstractDaemon
     */
    public function setExchangeName(string $exchangeName): AbstractDaemon
    {
        $this->exchangeName = $exchangeName;
        return $this;
    }

    /**
     *
     * @param string $queueName
     * @return \STLib\Daemon\AbstractDaemon
     */
    public function setQueueName(string $queueName): AbstractDaemon
    {
        $this->queueName = $queueName;
        return $this;
    }

    /**
     *
     * @param string $errorQueueName
     * @return \STLib\Daemon\AbstractDaemon
     */
    public function setErrorQueueName(string $errorQueueName): AbstractDaemon
    {
        $this->errorQueueName = $errorQueueName;
        return $this;
    }

    /**
     *
     * @param int $delay
     * @return AbstractDaemon
     */
    public function setDelay(int $delay): AbstractDaemon
    {
        $this->delay = $delay;
        return $this;
    }

    /**
     *
     * @param bool $doNotMakeNextAttempt
     * @return AbstractDaemon|bool
     */
    public function doNotMakeNextAttempt(bool $doNotMakeNextAttempt = null): AbstractDaemon|bool
    {
        if (is_null($doNotMakeNextAttempt)) {
            return $this->doNotMakeNextAttempt;
        }
        $this->doNotMakeNextAttempt = $doNotMakeNextAttempt;
        return $this;
    }

    /**
     *
     * @param bool $doNotAddToErrorQueue
     * @return AbstractDaemon|bool
     */
    public function doNotAddToErrorQueue(bool $doNotAddToErrorQueue = null): AbstractDaemon|bool
    {
        if (is_null($doNotAddToErrorQueue)) {
            return $this->doNotAddToErrorQueue;
        }
        $this->doNotAddToErrorQueue = $doNotAddToErrorQueue;
        return $this;
    }

    /**
     *
     * @param \PhpAmqpLib\Channel\AMQPChannel $channel
     * @return \STRabbit\Entity\AbstractDaemon
     */
    public function setChannel(\PhpAmqpLib\Channel\AMQPChannel $channel): AbstractDaemon
    {
        $this->channel = $channel;
        return $this;
    }

    /**
     *
     * @return \STLib\Expand\Collection
     */
    public function params(): \STLib\Expand\Collection
    {
        if (is_null($this->params)) {
            $this->params = new \STLib\Expand\Collection();
        }
        return $this->params;
    }

    /**
     *
     * @return bool
     */
    public function hasDelay(): bool
    {
        return $this->delay > 0;
    }

    /**
     *
     * @return AbstractDaemon
     */
    public function init(): AbstractDaemon
    {
        $this->getChannel()->queue_declare(
            queue: $this->getQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        $this->getChannel()->queue_declare(
            queue: $this->getErrorQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        return $this;
    }

    /**
     * Daemon handle
     *
     * @var string $data
     */
    abstract public function handle(string $message);

    /**
     *
     * @return void
     */
    public function catch(): void
    {
    }

    /**
     *
     * @return void
     */
    public function finally(): void
    {
    }
}
