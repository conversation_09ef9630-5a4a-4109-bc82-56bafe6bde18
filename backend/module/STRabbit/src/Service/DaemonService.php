<?php

declare(strict_types=1);

namespace STRabbit\Service;

class DaemonService
{
    use ProvidesRabbit;

    /**
     *
     * @var \STRabbit\Entity\AbstractDaemon|null
     */
    protected ?\STRabbit\Entity\AbstractDaemon $daemon = null;

    /**
     *
     * @return \STRabbit\Entity\AbstractDaemon
     */
    public function getDaemon(): \STRabbit\Entity\AbstractDaemon
    {
        return $this->daemon;
    }

    /**
     *
     * @param \STRabbit\Entity\AbstractDaemon $daemon
     * @return DaemonService
     */
    public function setDaemon(\STRabbit\Entity\AbstractDaemon $daemon): DaemonService
    {
        $this->daemon = $daemon;
        return $this;
    }

    /**
     *
     * @param \STRabbit\Entity\AbstractDaemon $daemon
     * @return void
     */
    public function run(\STRabbit\Entity\AbstractDaemon $daemon): void
    {
        $queue = $daemon->getQueueName();
        $channel = $this->rabbit()->getChannel();
        $daemon->setChannel($channel);
        $daemon->init();
        $channel->basic_qos(null, 1, false);
        $channel->basic_consume($queue, '', false, false, false, false, function ($message) use ($channel, $daemon) {
            $data = json_decode($message->body);
            try {
                $daemon->handle($message->body);
                $channel->basic_ack($message->delivery_info['delivery_tag']);
            } catch (\Throwable $e) {
                $data->attempt = $data->attempt ?? 0;
                $channel->basic_ack($message->delivery_info['delivery_tag']);
                if (++$data->attempt < $daemon->getAttemptsNumber() && !$daemon->doNotMakeNextAttempt()) {
                    $message = new \PhpAmqpLib\Message\AMQPMessage(json_encode($data), [
                        'application_headers' => new \PhpAmqpLib\Wire\AMQPTable([
                            'x-delay' => $daemon->getDelay(),
                        ]),
                    ]);
                    $channel->basic_publish($message, $daemon->getExchangeName(), $daemon->getQueueName());
                } elseif (!$daemon->doNotAddToErrorQueue()) {
                    $error = [
                        'code' => $e->getCode(),
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                    ];

                    $message = new \PhpAmqpLib\Message\AMQPMessage(
                        json_encode(
                            array_merge(['error' => $error], (array) $data)
                        ),
                    );
                    $channel->basic_publish($message, '', $daemon->getErrorQueueName());
                }
                $daemon->catch();
                if (!($e instanceof Daemon\NotThrowableException)) {
                    throw $e;
                }
            } finally {
                $daemon->finally();
            }
        });

        while (count($channel->callbacks)) {
            $channel->wait(timeout: 30 * 60);
        }
    }
}
