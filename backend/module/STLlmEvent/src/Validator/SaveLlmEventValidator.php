<?php

namespace STLlmEvent\Validator;

use <PERSON><PERSON>\Validator\StringLength;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Validator\Check\TextContent;
use STLib\Validator\Validator;
use STLlmEvent\Data\LlmEventsTable;

class SaveLlmEventValidator extends Validator
{
    protected const string ERROR_EVENT_DOES_NOT_EXIST = 'The llm event does not exists.';

    public function __construct(private readonly LlmEventsTable $llmEventsTable)
    {
    }

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['id' => $llmEventId, 'name' => $name, 'description' => $description] = $input;

        if (!is_null($llmEventId)) {
            try {
                $this->llmEventsTable->getLlmEvent($llmEventId);
            } catch (NotFoundApiException) {
                $this->addError('id', self::ERROR_EVENT_DOES_NOT_EXIST);

                return;
            }
        }

        $stringFiledErrorFormat = '%s must be not empty and less than %d symbols';

        $nameValidator = new StringLength([
            'min' => 1,
            'max' => 255,
        ]);
        $descriptionValidator = new StringLength([
            'min' => 1,
            'max' => 1024,
        ]);
        $descriptionContentValidator = new TextContent();

        if (!$nameValidator->isValid($name)) {
            $this->addError('name', sprintf($stringFiledErrorFormat, 'Name', 255));
        }
        if (!$descriptionValidator->isValid($description)) {
            $this->addError('description', sprintf($stringFiledErrorFormat, 'Description', 1024));
        }
        if (!$descriptionContentValidator->isValid($description)) {
            $errors = $descriptionContentValidator->getMessages();
            foreach ($errors as $error) {
                $this->addError('description', $error);
            }
        }
    }
}
