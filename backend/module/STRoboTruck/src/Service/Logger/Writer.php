<?php

namespace STRoboTruck\Service\Logger;

use Carbon\Carbon;
use Laminas\Log\Writer\AbstractWriter;
use PhpAmqpLib\Message\AMQPMessage;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\PusherService;

class Writer extends AbstractWriter
{
    private ?string $env;

    private static RabbitService $rabbit;

    public static function setRabbit(RabbitService $rabbit): void
    {
        static::$rabbit = $rabbit;
    }

    /**
     *
     * @param array $settings
     * @return $this
     */
    public function setSettings(array $settings): Writer
    {
        $this->env = $settings['env'] ?? 'local';

        return $this;
    }

    protected function doWrite(array $event): void
    {
        $channel = self::$rabbit->getChannel();

        $source = $event['extra']['source'];
        $eventName = $event['extra']['event_name'];
        unset($event['extra']['event_name'], $event['extra']['source']);

        $data = $event['extra'];
        $data['message'] = $event['message'];

        $bodyData = [
            'events' => [
                [
                    'env' => $this->env,
                    'name' => $eventName,
                    'time' => Carbon::now()->format('Y-m-d H:i:s.v'),
                    'source' => $source,
                    'properties' => $data
                ]
            ],
        ];
        $body = json_encode($bodyData);
        if (empty($body)) {
            error_log(print_r($bodyData, true));
        }

        $message = new AMQPMessage($body);
        $channel->basic_publish($message, '', PusherService::ROBO_TRUCK_QUEUE_NAME);
        $channel->close();
    }
}
