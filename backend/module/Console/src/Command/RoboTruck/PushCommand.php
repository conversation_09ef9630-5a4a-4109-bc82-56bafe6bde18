<?php

declare(strict_types=1);

namespace Console\Command\RoboTruck;

use Console\Command\BaseCommand;
use PhpAmqpLib\Channel\AMQPChannel;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Rs\JsonLines\Exception\InvalidJson;
use Rs\JsonLines\Exception\NonTraversable;
use RuntimeException;
use STRabbit\Service\RabbitService;
use STRedis\Service\MutexService;
use STRoboTruck\Service\PusherService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Lock\LockInterface;
use Throwable;

class PushCommand extends BaseCommand
{
    public const string ERROR_LIMIT_NOT_INTEGER = 'The limit parameter must be an positive integer.';
    public const string ERROR_LIMIT_TOO_BIG = 'The limit must be greater than 1. Limit entered: ';

    private const int DEFAULT_LIMIT = 100;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'robo-truck:push';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Push events to RoboTruck';

    protected function configure(): void
    {
        parent::configure();
        $this->addOption('limit', mode: InputOption::VALUE_REQUIRED);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws InvalidJson
     * @throws NonTraversable
     * @throws Throwable
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $lock = $this->getLock();
        if (!$lock->acquire()) {
            $output->writeln('RoboTruck push command is already running');
            return static::FAILURE;
        }

        try {
            $limit = $this->getLimit($input);

            /**
             * @var RabbitService $rabbit
             */
            $rabbit = $this->getServiceManager()->get(RabbitService::class);

            $channel = $rabbit->getChannel();
            $this->createQueues($channel);

            $events = $this->getEvents($channel);

            if (empty($events)) {
                $lock->release();

                return self::SUCCESS;
            }

            $this->pushEvents($events, $limit);

            $lock->release();

            return self::SUCCESS;
        } catch (Throwable $throwable) {
            $lock->release();
            throw $throwable;
        }
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private function getLock(): LockInterface
    {
        /**
         * @var MutexService $mutex
         */
        $mutex = $this->getServiceManager()->get(MutexService::class);

        return $mutex->getLock(PushCommand::class);
    }

    private function getLimit(InputInterface $input): int
    {
        $limit = $input->getOption('limit') ?? self::DEFAULT_LIMIT;
        $this->checkLimit($limit);

        return (int) $limit;
    }

    private function checkLimit(mixed $limit): void
    {
        if (!is_numeric($limit)) {
            throw new RuntimeException(self::ERROR_LIMIT_NOT_INTEGER);
        }

        if ($limit < 1) {
            throw new RuntimeException(self::ERROR_LIMIT_TOO_BIG . $limit);
        }
    }

    private function createQueues(AMQPChannel $channel): void
    {
        $channel->queue_declare(
            queue: PusherService::ROBO_TRUCK_QUEUE_NAME,
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        $channel->queue_declare(
            queue: PusherService::ROBO_TRUCK_QUEUE_ERROR_NAME,
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
    }

    private function getEvents(AMQPChannel $channel): array
    {
        $events = [];

        $isQueueEmpty = false;
        while (!$isQueueEmpty) {
            $message = $channel->basic_get(PusherService::ROBO_TRUCK_QUEUE_NAME, true);
            if ($message) {
                $data = json_decode($message->getBody(), true);
                if (is_null($data)) {
                    error_log('Getting message from queue: ' . $message->getBody());
                    continue;
                }
                if (!array_key_exists('events', $data)) {
                    error_log($message->getBody());
                } else {
                    $events = array_merge($events, $data['events']);
                }
            } else {
                $isQueueEmpty = true;
            }
        }

        return $events;
    }

    /**
     * @throws InvalidJson
     * @throws NonTraversable
     */
    private function pushEvents(array $events, int $limit): void
    {
        $pusher = $this->roboTruck()->pusher();

        $eventsToPush = [];
        while (true) {
            $eventsToPush[] = array_shift($events);
            if (count($eventsToPush) === $limit) {
                $pusher->push($eventsToPush);
                $eventsToPush = [];
            }

            if (empty($events)) {
                if (!empty($eventsToPush)) {
                    $pusher->push($eventsToPush);
                }
                break;
            }
        }
    }
}
