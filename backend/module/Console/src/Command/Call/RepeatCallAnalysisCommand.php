<?php

declare(strict_types=1);

namespace Console\Command\Call;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class RepeatCallAnalysisCommand extends \Console\Command\BaseCommand
{
    protected const UPLOADED_LATER_THAN_IN_HOURS = 6;
    protected const UPLOADED_BEFORE_THAN_IN_HOURS = 1;

    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:repeat-analysis';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Try to repeat analysis for calls in stuck';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('uploaded_later_than_in_hours', 'l', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', static::UPLOADED_LATER_THAN_IN_HOURS);
        $this->addOption('uploaded_before_than_in_hours', 'b', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', static::UPLOADED_BEFORE_THAN_IN_HOURS);
        $this->addOption('company_id', 'c', \Symfony\Component\Console\Input\InputOption::VALUE_OPTIONAL, '', null);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $uploadedLaterThanInHours = (int) $input->getOption('uploaded_later_than_in_hours');
        $uploadedBeforeThanInHours = (int) $input->getOption('uploaded_before_than_in_hours');
        $companyId = (int) $input->getOption('company_id');

        $companyIds = ($companyId > 0) ? [$companyId] : $this->company()->getCompanyIds();

        $callsCount = 0;
        foreach ($companyIds as $companyId) {
            $output->writeln('Company id is ' . $companyId);

            $company = $this->company()->getCompany($companyId);

            if (!$company->isPaidTranscribingTimeEnoughForAnalyze()) {
                $output->writeln('Company ' . $companyId . ' has no enough paid balance');
                continue;
            }

            $unprocessedCalls = $this->call()->getUnprocessedCall($companyId, $uploadedLaterThanInHours, $uploadedBeforeThanInHours);
            $callsCount += $unprocessedCalls->count();
            foreach ($unprocessedCalls as $call) {
                $queue = $this->call()->analysis()->getNextAnalysisStep($call);
                $output->writeln('-- Adding call "' . $call->getId() . '" to queue "' . $queue . '"');
                $this->call()->analysis()->addToQueue($call->getCompanyId(), $call, $queue);
            }
        }

        $output->writeln('Calls count: ' . $callsCount);

        return static::SUCCESS;
    }
}
