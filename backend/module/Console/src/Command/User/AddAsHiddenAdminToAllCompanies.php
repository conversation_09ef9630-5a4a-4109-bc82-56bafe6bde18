<?php

declare(strict_types=1);

namespace Console\Command\User;

use Symfony\Component\Console\Input\InputOption;

class AddAsHiddenAdminToAllCompanies extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'user:add-as-hidden-admin-to-all-companies';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Add user as hidden admin to all companies';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('user_id', null, InputOption::VALUE_REQUIRED, 'User id');
    }

    /**
     *
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    protected function launch(\Symfony\Component\Console\Input\InputInterface $input, \Symfony\Component\Console\Output\OutputInterface $output): int
    {
        $userId = (int) $input->getOption('user_id');
        if ($userId <= 0) {
            throw new \RuntimeException('Param "user_id" is required');
        }
        $companyIds = $this->company()->getCompanyIds();
        $usersCompaniesRolesTable = $this->getServiceManager()->get(\STCompany\Data\UsersCompaniesRolesTable::class);
        foreach ($companyIds as $companyId) {
            $roles = $this->company()->role()->getRoles($companyId);
            foreach ($roles as $role) {
                if ($role->isAdmin()) {
                    $usersCompaniesRolesTable->saveUserData($userId, $companyId, $role->getId());
                    $output->writeln('User has been added to company id=' . $companyId . ' with role "' . $role->getName() . '" (id: "' . $role->getId() . ')"');
                }
            }
        }

        return static::SUCCESS;
    }
}
