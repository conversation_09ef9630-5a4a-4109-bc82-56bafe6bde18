<?php

declare(strict_types=1);

namespace STRoboMetrics\Service;

class ChecklistFormatterService
{
    public function formatChecklistsStatistics(array $checklistsStats): array
    {
        $formattedStats = [];

        foreach ($checklistsStats as $i => $stat) {
            $formattedStats[$i]['company_name'] = $stat['company_name'];
            $formattedStats[$i]['billing_units'] = 0;
            $formattedStats[$i]['checklists'] = '';

            foreach ($stat['checklists'] as $checklist) {
                $formattedStats[$i]['checklists'] .= sprintf(
                    '%s: %d' . PHP_EOL,
                    $checklist['checklist_name'],
                    $checklist['total_checklist_conversations']
                );
                $formattedStats[$i]['billing_units'] += $checklist['billing_units'];
            }
        }

        return $formattedStats;
    }

    public function formatUsageStatistics(
        array $callsStats,
        array $chatStats,
        array $checklistsStats,
        array $summarizationStats
    ): array {
        // Index all statistics by company_name for easier lookup
        $callsStatsMap = $this->mapStatsByCompanyName($callsStats);
        $chatStatsMap = $this->mapStatsByCompanyName($chatStats);
        $checklistsStatsMap = $this->mapStatsByCompanyName($checklistsStats);
        $summarizationStatsMap = $this->mapStatsByCompanyName($summarizationStats);

        $allCompanyNames = array_unique(
            array_merge(
                array_keys($callsStatsMap),
                array_keys($chatStatsMap),
                array_keys($checklistsStatsMap),
                array_keys($summarizationStatsMap)
            )
        );

        // Create combined statistics array
        $combinedStats = [];
        foreach ($allCompanyNames as $companyName) {
            $combinedStats[] = $this->createCombinedStatForCompany(
                $companyName,
                $callsStatsMap,
                $chatStatsMap,
                $checklistsStatsMap,
                $summarizationStatsMap
            );
        }

        return $combinedStats;
    }

    private function mapStatsByCompanyName(array $stats): array
    {
        return $stats
            ? array_combine(array_column($stats, 'company_name'), $stats)
            : [];
    }

    /**
     * Create a combined statistic entry for a single company
     */
    private function createCombinedStatForCompany(
        string $companyName,
        array $callsStatsMap,
        array $chatStatsMap,
        array $checklistsStatsMap,
        array $summarizationStatsMap
    ): array {
        $combinedStat = [
            'company_name' => $companyName,
            'total_call_hours' => $callsStatsMap[$companyName]['total_call_hours'] ?? 0,
            'total_chats_count' => $chatStatsMap[$companyName]['total_chats_count'] ?? 0,
            'total_summarization_conversations' => $summarizationStatsMap[$companyName]['total_summarization_conversations'] ?? 0,
            'checklists' => '',
            'billing_units' => 0
        ];

        if (isset($checklistsStatsMap[$companyName])) {
            $combinedStat['checklists'] = $checklistsStatsMap[$companyName]['checklists'];
            $combinedStat['billing_units'] = $checklistsStatsMap[$companyName]['billing_units'];
        }

        return $combinedStat;
    }
}
