<?php

declare(strict_types=1);

namespace STCall\Service;

use Interop\Container\ContainerInterface;
use <PERSON>inas\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON>all\Data\CallsAlgoEventsTable;
use STCall\Data\CallsCommentsNotificationsTable;
use STCall\Data\CallsCommentsTable;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsReviewsTable;
use STCall\Data\CallsTable;
use STCall\Data\EventHappeningsChangesTable;
use ST<PERSON>all\Data\EventsRepository;
use STLib\Mvc\Hydrator\Hydrator;
use STUser\Data\UsersTable;

class CallServiceFactory implements FactoryInterface
{
    /**
     *
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return CallService
     */
    public function createService(ContainerInterface $container, $requestedName, array $options = null): CallService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return CallService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): CallService
    {
        $callsTable = $container->get(CallsTable::class);
        $paragraphTable = $container->get(CallsParagraphsTable::class);
        $commentsTable = $container->get(CallsCommentsTable::class);
        $callsCommentsNotificationsTable = $container->get(CallsCommentsNotificationsTable::class);
        $callsReviewsTable = $container->get(CallsReviewsTable::class);
        $callsAlgoEventsTable = $container->get(CallsAlgoEventsTable::class);
        $eventsRepository = $container->get(EventsRepository::class);
        $eventHappeningsChangesTable = $container->get(EventHappeningsChangesTable::class);
        $usersTable = $container->get(UsersTable::class);
        $hydrator = $container->get(Hydrator::class);
        $config = $container->get('config');
        $awsConfig = $config['aws'];
        return new CallService(
            $callsTable,
            $paragraphTable,
            $commentsTable,
            $callsCommentsNotificationsTable,
            $callsReviewsTable,
            $callsAlgoEventsTable,
            $eventsRepository,
            $eventHappeningsChangesTable,
            $usersTable,
            $hydrator,
            $awsConfig,
        );
    }
}
