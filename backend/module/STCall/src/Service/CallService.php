<?php

declare(strict_types=1);

namespace STCall\Service;

use Carbon\Carbon;
use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallCollection;
use ST<PERSON>all\Entity\CallFactory;
use STCall\Entity\EventHappening;
use STCall\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCompany\Entity\Company;
use STCompany\Entity\Event\EventCollection;
use STLib\Mvc\Hydrator\Hydrator;
use STUser\Service\UserAvatarService;

class CallService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;
    use AwsTrait;
    use \STUser\Entity\UserAvatarTrait;

    /**
     *
     * @var \STCall\Data\CallsTable
     */
    protected \STCall\Data\CallsTable $callsTable;

    /**
     *
     * @var \STCall\Data\CallsParagraphsTable
     */
    protected \STCall\Data\CallsParagraphsTable $callsParagraphsTable;

    /**
     *
     * @var \STCall\Data\CallsCommentsTable
     */
    protected \STCall\Data\CallsCommentsTable $callsCommentsTable;

    /**
     *
     * @var \STCall\Data\CallsCommentsNotificationsTable
     */
    protected \STCall\Data\CallsCommentsNotificationsTable $callsCommentsNotificationsTable;

    /**
     *
     * @var \STCall\Data\CallsReviewsTable
     */
    protected \STCall\Data\CallsReviewsTable $callsReviewsTable;

    /**
     *
     * @var \STCall\Data\CallsAlgoEventsTable
     */
    protected \STCall\Data\CallsAlgoEventsTable $callsAlgoEventsTable;

    /**
     *
     * @var \STCall\Data\EventsRepository
     */
    protected \STCall\Data\EventsRepository $eventsRepository;

    /**
     *
     * @var \STCall\Data\EventHappeningsChangesTable
     */
    protected \STCall\Data\EventHappeningsChangesTable $eventHappeningsChangesTable;

    /**
     *
     * @var \STUser\Data\UsersTable
     */
    protected \STUser\Data\UsersTable $usersTable;

    /**
     *
     * @param \STCall\Data\CallsTable $callsTable
     * @param \STCall\Data\CallsParagraphsTable $callsParagraphsTable
     * @param \STCall\Data\CallsCommentsTable $callsCommentsTable
     * @param \STCall\Data\CallsCommentsNotificationsTable $callsCommentsNotificationsTable
     * @param \STCall\Data\CallsReviewsTable $callsReviewsTable
     * @param \STCall\Data\CallsAlgoEventsTable $callsAlgoEventsTable
     * @param \STCall\Data\EventsRepository $eventsRepository
     * @param \STCall\Data\EventHappeningsChangesTable $eventHappeningsChangesTable
     * @param \STUser\Data\UsersTable $usersTable
     * @param array $awsConfig
     * @throws \UnexpectedValueException
     */
    public function __construct(
        \STCall\Data\CallsTable $callsTable,
        \STCall\Data\CallsParagraphsTable $callsParagraphsTable,
        \STCall\Data\CallsCommentsTable $callsCommentsTable,
        \STCall\Data\CallsCommentsNotificationsTable $callsCommentsNotificationsTable,
        \STCall\Data\CallsReviewsTable $callsReviewsTable,
        \STCall\Data\CallsAlgoEventsTable $callsAlgoEventsTable,
        \STCall\Data\EventsRepository $eventsRepository,
        \STCall\Data\EventHappeningsChangesTable $eventHappeningsChangesTable,
        \STUser\Data\UsersTable $usersTable,
        private readonly Hydrator $hydrator,
        array $awsConfig,
    ) {
        if (!isset($awsConfig['api']) || !isset($awsConfig['env'])) {
            throw new \UnexpectedValueException('AWS config must contain "api" and "env" settings');
        }

        $this->callsTable = $callsTable;
        $this->callsParagraphsTable = $callsParagraphsTable;
        $this->callsCommentsTable = $callsCommentsTable;
        $this->callsCommentsNotificationsTable = $callsCommentsNotificationsTable;
        $this->callsReviewsTable = $callsReviewsTable;
        $this->callsAlgoEventsTable = $callsAlgoEventsTable;
        $this->eventsRepository = $eventsRepository;
        $this->eventHappeningsChangesTable = $eventHappeningsChangesTable;
        $this->usersTable = $usersTable;
        $this->awsConfig = $awsConfig['api'];
        $this->env = $awsConfig['env'];
    }

    /**
     *
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param bool $isAnalyzedOnly
     * @return array
     */
    public function getCallIds(
        int $companyId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        bool $isAnalyzedOnly = true,
    ): array {
        return $this->callsTable->getCallIds($companyId, $startDate, $endDate, $isAnalyzedOnly);
    }

    /**
     *
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    public function getS3Files(
        int $companyId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): array {
        return $this->callsTable->getS3Files($companyId, $startDate, $endDate);
    }

    /**
     *
     * @param int $companyId
     * @param string $search
     * @return array
     */
    public function getCallIdsBySearchString(
        int $companyId,
        string $search
    ): array {
        return $this->callsTable->getCallIdsBySearchString($companyId, $search);
    }

    /**
     *
     * @param Company $company
     * @param int $roleId
     * @param \STClickhouse\Entity\Pagination\Pagination $pagination
     * @return \STClickhouse\Entity\Pagination\Pagination
     */
    public function getCallsWithPagination(
        Company $company,
        int $roleId,
        \STClickhouse\Entity\Pagination\Pagination $pagination,
    ): \STClickhouse\Entity\Pagination\Pagination {
        $this->callsTable->getCallsWithPagination($company->getId(), $roleId, $pagination);
        return $pagination;
    }

    /**
     *
     * @param Company $company
     * @param string $callId
     * @param \STCompany\Entity\Event\EventCollection|null $eventCollection
     * @param \STCompany\Entity\Event\Color|null $defaultFragmentColor
     * @return \STCall\Entity\Call
     */
    public function getCall(
        Company $company,
        int $roleId,
        int $userId,
        string $callId,
        ?\STCompany\Entity\Event\EventCollection $eventCollection = null,
        ?\STCompany\Entity\Event\Color $defaultFragmentColor = null,
    ): \STCall\Entity\Call {
        $callData = $this->callsTable->getCall($callId, $company->getId(), $roleId);
        /** @var \STCall\Entity\Call $call */
        $callFactory = new \STCall\Entity\CallFactory();
        $call = $callFactory->createCall($callData);
        $call->setParagraphs($this->getParagraphs($company, $callId));

        $comments = $this->getComments($company->getId(), $callId, $userId);
        $call->setComments($comments);

        if (
            $eventCollection instanceof \STCompany\Entity\Event\EventCollection
            && $defaultFragmentColor instanceof \STCompany\Entity\Event\Color
        ) {
            $eventsData = $this->eventsRepository->getEvents($company, $callId, $roleId);
            $eventChanges = $this->eventHappeningsChangesTable->getEventChanges($company, $callId, $roleId);
            $this->injectEventsToCall($call, $eventsData, $eventChanges, $eventCollection, $defaultFragmentColor);
        }
        return $call;
    }

    /**
     *
     * @param Company $company
     * @param array $roleIds
     * @param array $callIds
     * @param \STCompany\Entity\Event\EventCollection|null $eventCollection
     * @param \STCompany\Entity\Event\Color|null $defaultFragmentColor
     * @return \STCall\Entity\CallRoleCollection
     */
    public function getCallsWithRoleIds(
        Company $company,
        array $roleIds,
        array $callIds,
        ?\STCompany\Entity\Event\EventCollection $eventCollection = null,
        ?\STCompany\Entity\Event\Color $defaultFragmentColor = null,
    ): \STCall\Entity\CallRoleCollection {
        // collection of calls without role context
        $callCollection = new \STCall\Entity\CallCollection();
        // collection of calls with role context (separate element for every combination for call_id-role_id)
        $callRoleCollection = new \STCall\Entity\CallRoleCollection();

        $callsData = $this->callsTable->getCall($callIds, $company->getId());
        $callFactory = new \STCall\Entity\CallFactory();
        foreach ($callsData as $callData) {
            $callCollection->add($callFactory->createCall($callData, withConstructor: true));
        }

        // add paragraphs
        $paragraphsData = $this->callsParagraphsTable->getParagraphs(
            $company->getId(),
            $callIds,
            $company->getEncryptionKey()
        );
        foreach ($paragraphsData as $paragraphData) {
            if (!$callCollection->offsetExists($paragraphData['call_id'])) {
                continue;
            }
            $callCollection
                ->offsetGet($paragraphData['call_id'])
                ->getParagraphs()
                ->add($this->hydrate($paragraphData, \STCall\Entity\Paragraph::class, withConstructor: true));
        }

        // add comments
        $comments = $this->getComments($company->getId(), $callIds);
        foreach ($comments as $comment) {
            if (!$callCollection->offsetExists($comment->getCallId())) {
                continue;
            }
            $callCollection
                ->offsetGet($comment->getCallId())
                ->getComments()
                ->add($comment);
        }

        $eventsData = count($callIds) > 0 ? $this->eventsRepository->getEvents($company, $callIds, $roleIds) : [];
        $eventChanges = count($callIds) > 0 ? $this->eventHappeningsChangesTable->getEventChanges(
            $company,
            $callIds,
            $roleIds
        ) : [];

        // fill $callRoleCollection from $callCollection and add events
        foreach ($roleIds as $roleId) {
            $roleEventCollection = $eventCollection->slice(function ($event) use ($roleId) {
                return $event->getRole()->getId() === $roleId;
            });
            foreach ($callCollection as $call) {
                $roleEventsData = array_filter($eventsData, function ($eventData) use ($roleId, $call) {
                    return $eventData['role_id'] === $roleId && $eventData['call_id'] === $call->getId();
                });
                $roleEventChanges = array_filter($eventChanges, function ($eventChange) use ($roleId, $call) {
                    return $eventChange['role_id'] === $roleId && $eventChange['call_id'] === $call->getId();
                });
                $copier = new \DeepCopy\DeepCopy(true);
                $callWithRole = $this->hydrate([
                    'call' => $copier->copy($call),
                    'role_id' => $roleId,
                ], \STCall\Entity\CallRole::class);
                $this->injectEventsToCall(
                    $callWithRole->getCall(),
                    $roleEventsData,
                    $roleEventChanges,
                    $roleEventCollection,
                    $defaultFragmentColor
                );
                // add call to $callRoleCollection
                $callRoleCollection->add($callWithRole);
            }
        }

        $reviewValues = $this->callsReviewsTable->getReviews($company->getId(), $callIds, $roleIds);
        foreach ($reviewValues as $reviewValue) {
            if ($callRoleCollection->offsetExists($reviewValue['call_id'] . '-' . $reviewValue['role_id'])) {
                $call = $callRoleCollection
                    ->offsetGet($reviewValue['call_id'] . '-' . $reviewValue['role_id'])
                    ->getCall();
                $call
                    ->isReviewed($reviewValue['is_reviewed'])
                    ->setReviewedTime($reviewValue['reviewed_time']);

                if (isset($reviewValue['reviewer_user_id'])) {
                    $call
                        ->setReviewerUserId((int) $reviewValue['reviewer_user_id'])
                        ->addReviewer((int) $reviewValue['reviewer_user_id'], $reviewValue['reviewer_user_name']);
                }
            }
        }

        return $callRoleCollection;
    }

    /**
     *
     * @param int $companyId
     * @param array $callIds
     * @param bool $allowRemoved
     * @return CallCollection
     * @throws NotFoundApiException
     */
    public function getBasicCalls(
        int $companyId,
        array $callIds,
        bool $allowRemoved = false,
    ): \STCall\Entity\CallCollection {
        $callCollection = new \STCall\Entity\CallCollection();
        $callsData = $this->callsTable->getCall($callIds, $companyId, allowRemoved: $allowRemoved);
        $callFactory = new \STCall\Entity\CallFactory();
        foreach ($callsData as $callData) {
            $callCollection->add($callFactory->createCall($callData, withConstructor: true));
        }
        return $callCollection;
    }

    /**
     *
     * @param int $companyId
     * @param int $uploadedLaterThanInHours
     * @param int $uploadedBeforeThanInHours
     * @return \STCall\Entity\CallCollection
     */
    public function getUnprocessedCall(
        int $companyId,
        int $uploadedLaterThanInHours,
        int $uploadedBeforeThanInHours
    ): \STCall\Entity\CallCollection {
        $callCollection = new \STCall\Entity\CallCollection();
        $callsData = $this->callsTable->getUnprocessedCall(
            $companyId,
            $uploadedLaterThanInHours,
            $uploadedBeforeThanInHours
        );
        $callFactory = new \STCall\Entity\CallFactory();
        foreach ($callsData as $callData) {
            $callCollection->add($callFactory->createCall($callData));
        }
        return $callCollection;
    }

    /**
     * @param int $companyId
     * @param int $limit
     * @return array
     */
    public function getCallsIdsWithoutSpeakersRolesDetected(
        int $companyId,
        int $limit
    ): array {
        return $this->callsTable->getCallsIdsWithoutSpeakersRolesDetected($companyId, $limit);
    }

    /**
     *
     * @param int $companyId
     * @param string $callId
     * @param string $param
     * @return mixed
     */
    public function getCallParam(
        int $companyId,
        string $callId,
        string $param
    ): mixed {
        return $this->callsTable->getCallParam($companyId, $callId, $param);
    }

    /**
     *
     * @param Company $company
     * @param string $callId
     * @return string
     */
    public function getCallUrl(
        Company $company,
        string $callId
    ): string {
        $this->setCompany($company);
        $callData = $this->callsTable->getCall($callId, $company->getId());
        $callFactory = new \STCall\Entity\CallFactory();
        $call = $callFactory->createCall($callData);
        if (!($call instanceof Call)) {
            throw new \RuntimeException('Unavailable action for this type of Call');
        }
        if (is_null($call->getS3FilePath())) {
            throw new \RuntimeException('Object is not exists');
        }
        return $this->getPreSignedUrl($call->getS3FilePath());
    }

    /**
     *
     * @param Company $company
     * @param Call $call
     * @param int|null $startTime
     * @param int|null $endTime
     * @return string
     * @throws NotFoundApiException
     */
    public function getCallContent(
        Company $company,
        Call $call,
        ?int $startTime = null,
        ?int $endTime = null
    ): string {
        $this->setCompany($company);
        $callS3FilePath = $call->getS3FilePath();

        if (is_null($callS3FilePath)) {
            throw new NotFoundApiException('The call file doesnt exist');
        }

        $callContent = $this->getFileFromS3ByFileName($callS3FilePath);
        $callDuration = (int) $call->getDuration();

        if ($startTime === null && $endTime === null) {
            return $callContent;
        }

        $startTime = $startTime ?? 0;
        $endTime = $endTime ?? $callDuration;

        $filePath = sys_get_temp_dir() . '/' . uniqid();
        $croppedFilePath = getcwd() . '/data/cropped-audio/' . uniqid() . '.mp3';
        file_put_contents($filePath, $callContent);
        $ffmpeg = \FFMpeg\FFMpeg::create();
        $audio = $ffmpeg->open($filePath);
        $audio->filters()->clip(
            \FFMpeg\Coordinate\TimeCode::fromSeconds($startTime),
            \FFMpeg\Coordinate\TimeCode::fromSeconds($endTime - $startTime),
        );
        $audio->save(new \FFMpeg\Format\Audio\Mp3(), $croppedFilePath);
        $croppedContent = file_get_contents($croppedFilePath);
        unlink($filePath);
        unlink($croppedFilePath);

        return $croppedContent;
    }

    /**
     * @param Company $company
     * @param string $callId
     * @return ParagraphCollection
     * @throws ReflectionException
     */
    public function getParagraphs(
        Company $company,
        string $callId
    ): ParagraphCollection {
        $paragraphsData = $this->callsParagraphsTable->getParagraphs(
            $company->getId(),
            $callId,
            $company->getEncryptionKey()
        );

        $paragraphCollection = new ParagraphCollection();

        foreach ($paragraphsData as $paragraphData) {
            $paragraph = $this->hydrator->hydrateClass($paragraphData, Paragraph::class, withConstructor: true);
            $paragraphCollection->add($paragraph);
        }

        return $paragraphCollection;
    }

    /**
     *
     * @param Company $company
     * @param string $callId
     * @param int $paragraphNumber
     * @return Paragraph
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getParagraph(
        Company $company,
        string $callId,
        int $paragraphNumber
    ): \STCall\Entity\Paragraph {
        $paragraphData = $this->callsParagraphsTable->getParagraph(
            $company->getId(),
            $callId,
            $paragraphNumber,
            $company->getEncryptionKey()
        );
        /** @var Paragraph $paragraph */
        $paragraph = $this->hydrate($paragraphData, Paragraph::class);
        return $paragraph;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param string $commentId
     * @return \STCall\Entity\Comment
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getComment(
        int $companyId,
        int $userId,
        string $commentId
    ): \STCall\Entity\Comment {
        $commentData = $this->callsCommentsTable->getComment($companyId, $userId, $commentId);
        $userData = [
            'id' => $commentData['user_id'],
            'name' => $commentData['user_name'],
            'email' => $commentData['user_email'],
        ];
        $user = $this->hydrate($userData, \STCompany\Entity\User::class);
        /** @var \STCall\Entity\Comment $comment */
        $comment = $this->hydrate(array_merge($commentData, [
            'user' => $user,
        ]), \STCall\Entity\Comment::class, withConstructor: true);
        $mentionedUsersData = $this->usersTable->getUserById($comment->getMentionedUserIds());
        foreach ($mentionedUsersData as $mentionedUserData) {
            $comment->getMentionedUsers()->add(
                $this->hydrate((array) $mentionedUserData, \STCompany\Entity\User::class)
            );
        }
        return $comment;
    }

    /**
     *
     * @param int $companyId
     * @param string|array $callId
     * @param int|null $userId
     * @return \STCall\Entity\CommentCollection
     * @throws NotFoundApiException
     */
    public function getComments(
        int $companyId,
        string|array $callId,
        int $userId = null
    ): \STCall\Entity\CommentCollection {
        $commentsData = $this->callsCommentsTable->getComments($companyId, $callId, $userId);
        $commentCollection = new \STCall\Entity\CommentCollection();
        foreach ($commentsData as $commentData) {
            $userData = [
                'id' => $commentData['user_id'],
                'name' => $commentData['user_name'],
                'email' => $commentData['user_email'],
            ];
            $comment = $this->hydrate($commentData, \STCall\Entity\Comment::class, withConstructor: true);
            $user = $this->hydrate($userData, \STCompany\Entity\User::class);
            $comment->setUser($user);
            $commentCollection->add($comment);
        }

        $mentionedUserIds = $commentCollection->getMentionedUserIds();
        $mentionedUsersData = $this->usersTable->getUserById($mentionedUserIds);
        $mentionedUserCollection = new \STCompany\Entity\UserCollection();
        foreach ($mentionedUsersData as $mentionedUserData) {
            $mentionedUserCollection->add($this->hydrate((array) $mentionedUserData, \STCompany\Entity\User::class));
        }

        foreach ($commentCollection as $comment) {
            foreach ($comment->getMentionedUserIds() as $mentionedUserId) {
                if (!$mentionedUserCollection->offsetExists($mentionedUserId)) {
                    continue;
                }
                $comment->getMentionedUsers()->add($mentionedUserCollection->offsetGet($mentionedUserId));
            }
        }

        return $commentCollection;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param array $teamIds
     * @return int
     */
    public function getUnreadCallNotificationsCount(
        int $companyId,
        int $userId,
        array $teamIds = []
    ): int {
        return $this->callsCommentsNotificationsTable->getUnreadCallNotificationsCount($companyId, $userId, $teamIds);
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param array $unreadComments
     * @return array
     */
    public function getCallIdsWithNotifications(
        int $companyId,
        int $userId,
        array $unreadComments = []
    ): array {
        return $this->callsCommentsNotificationsTable->getCallIdsWithNotifications(
            $companyId,
            $userId,
            $unreadComments
        );
    }

    /**
     *
     * @return array
     */
    public function getCompaniesWithDecreasingCallsAmount(): array
    {
        return $this->callsTable->getCompaniesWithDecreasingCallsAmount();
    }

    /**
     *
     * @param Paragraph $paragraph
     * @param Company $company
     * @return Paragraph
     */
    public function swapParagraphSpeakerRoles(
        Paragraph $paragraph,
        Company $company
    ): \STCall\Entity\Paragraph {
        $paragraph->swapSpeakerRole();
        $this->callsParagraphsTable->saveParagraph($paragraph, $company->getEncryptionKey());
        return $paragraph;
    }

    /**
     *
     * @param ParagraphCollection $paragraphs
     * @param Company $company
     * @return ParagraphCollection
     */
    public function swapCallSpeakerRoles(
        ParagraphCollection $paragraphs,
        Company $company
    ): ParagraphCollection {
        /** @var Paragraph $paragraph */
        foreach ($paragraphs as $paragraph) {
            $paragraph->swapSpeakerRole([
                Call::CALL_SPEAKER_ROLE_AGENT,
                Call::CALL_SPEAKER_ROLE_CLIENT,
            ]);
        }

        $this->callsParagraphsTable->saveParagraphs($paragraphs, $company->getEncryptionKey());

        return $paragraphs;
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return int
     */
    public function saveCall(
        \STCall\Entity\Call $call
    ): int {
        return $this->callsTable->saveCall($call);
    }

    /**
     *
     * @param Company $company
     * @param \STCall\Entity\Paragraph $paragraph
     * @return int
     */
    public function saveParagraph(
        Company $company,
        \STCall\Entity\Paragraph $paragraph
    ): int {
        return $this->callsParagraphsTable->saveParagraph($paragraph, $company->getEncryptionKey());
    }

    /**
     *
     * @param \STCall\Entity\Comment $comment
     * @param array $mentionedUserIds
     * @return \STCall\Entity\Comment
     */
    public function saveComment(
        \STCall\Entity\Comment $comment,
        array $mentionedUserIds = []
    ): \STCall\Entity\Comment {
        $this->callsCommentsTable->saveComment($comment);

        $notifications = new \STCall\Entity\NotificationCollection();
        $userToBeNotifiedIds = [];
        $userId = $comment->getUserId();
        $callComments = $this->getComments($comment->getCompanyId(), $comment->getCallId(), $userId);
        foreach ($callComments as $callComment) {
            if ($callComment->getUserId() !== $userId) {
                $userToBeNotifiedIds[] = $callComment->getUserId();
            }
        }

        foreach (array_unique(array_merge($userToBeNotifiedIds, $mentionedUserIds)) as $userToBeNotifiedId) {
            $notification = $this->hydrate([
                'user_id' => $userToBeNotifiedId,
                'call_id' => $comment->getCallId(),
                'company_id' => $comment->getCompanyId(),
                'comment_id' => $comment->getCommentId(),
            ], \STCall\Entity\Notification::class);
            $notifications->add($notification);
            $comment->addNotifiedUserId($userToBeNotifiedId);
        }
        $this->callsCommentsNotificationsTable->saveNotifications($notifications);

        return $comment;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param string $callId
     * @return int
     */
    public function setCallsCommentsToRead(
        int $companyId,
        int $userId,
        string $callId
    ): int {
        $notificationsData = $this->callsCommentsNotificationsTable->getNotifications($companyId, $userId, $callId);
        $notifications = new \STCall\Entity\NotificationCollection();
        foreach ($notificationsData as $notificationData) {
            $notification = $this->hydrate($notificationData, \STCall\Entity\Notification::class);
            $notification
                ->isUnread(false)
                ->initCreated(force: true);
            $notifications->add($notification);
        }
        return $this->callsCommentsNotificationsTable->saveNotifications($notifications);
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return void
     */
    public function deleteCallParagraphs(
        \STCall\Entity\Call $call
    ): void {
        $this->callsParagraphsTable->deleteByCall($call);
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return void
     */
    public function deleteCallAlgoEvents(
        \STCall\Entity\Call $call
    ): void {
        $this->callsAlgoEventsTable->deleteByCall($call);
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return void
     */
    public function deleteCallEventHappeningChanges(
        \STCall\Entity\Call $call
    ): void {
        $this->eventHappeningsChangesTable->deleteByCall($call);
    }

    /**
     * @param int $companyId
     * @param string $callId
     * @return int
     */
    public function getCallAlgoEventsCount(
        int $companyId,
        string $callId
    ): int {
        return $this->callsAlgoEventsTable->getCallAlgoEventsCount($callId, $companyId);
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @param array $eventsData
     * @param array $eventChanges
     * @param \STCompany\Entity\Event\EventCollection $eventCollection
     * @param \STCompany\Entity\Event\Color $defaultFragmentColor
     * @return \STCall\Entity\Call
     */
    protected function injectEventsToCall(
        \STCall\Entity\Call $call,
        array $eventsData,
        array $eventChanges,
        \STCompany\Entity\Event\EventCollection $eventCollection,
        \STCompany\Entity\Event\Color $defaultFragmentColor,
    ): \STCall\Entity\Call {
        // add event happenings to paragraphs
        foreach ($eventsData as $eventData) {
            $paragraph = $call
                ->getParagraphs()
                ->offsetGet($eventData['paragraph_number']);
            $created = $call->getUploadedTime();
            // event happenings inject implementation
            $this->injectEventHappeningToParagraph($paragraph, $eventCollection, $eventData, $created);
        }

        // apply event changes
        foreach ($eventChanges as $eventChange) {
            $paragraph = $call
                ->getParagraphs()
                ->offsetGet($eventChange['paragraph_number']);
            if ($paragraph instanceof \STCall\Entity\Paragraph) {
                // apply event changes implementation
                $this->applyEventChanges($paragraph, $eventCollection, $eventChange);
                // apply reviewers
                $call
                    ->addReviewer($eventChange['user_id'], $eventChange['user_name']);
            }
        }

        // slice fragments
        $paragraphManager = new CallFragment\ParagraphManager();
        $fragments = $paragraphManager
            ->setParagraphCollection($call->getParagraphs())
            ->getCallFragments(
                $call->getCallType(),
                \STCall\Service\CallFragment\Slicer\BaseSlicer::CALL_CIRCLES_PURPOSE,
            );
        foreach ($fragments as $fragment) {
            $fragment->setColor($defaultFragmentColor);
        }
        $call->setFragments($fragments);

        // precalculate values
        $call->precalculateValues(true);

        return $call;
    }

    /**
     *
     * @param \STCall\Entity\Paragraph $paragraph
     * @param \STCompany\Entity\Event\EventCollection $eventCollection
     * @param array $eventData
     * @param Carbon $created
     * @return \STCall\Entity\Paragraph
     */
    protected function injectEventHappeningToParagraph(
        \STCall\Entity\Paragraph $paragraph,
        \STCompany\Entity\Event\EventCollection $eventCollection,
        array $eventData,
        Carbon $created = null,
    ): \STCall\Entity\Paragraph {
        $event = $eventCollection->offsetGet($eventData['event_id']);
        if (is_null($event)) {
            return $paragraph;
        }

        $eventHappening = $this->hydrate([
            'event' => $event,
            'text' => $paragraph->getText() ?? '',
            'en_text' => $paragraph->getEnText() ?? '',
            'score' => round($eventData['score'] * 100),
            'main_point_phrase' => $eventData['main_point_phrase'] ?? '',
            'en_main_point_phrase' => $eventData['en_main_point_phrase'] ?? '',
        ], \STCall\Entity\EventHappening::class);
        $historyRecord = $this->hydrate([
            'event' => $event,
            'author' => match ($eventData['event_source']) {
                \STCall\Data\EventsRepository::ALGO_EVENTS_SOURCE_EVENT => \STCall\Entity\EventHappening\HistoryRecord::ALGO_EVENT_AUTHOR,
                \STCall\Data\EventsRepository::SEARCH_WORDS_SOURCE_EVENT => \STCall\Entity\EventHappening\HistoryRecord::SEARCH_WORD_AUTHOR,
            },
            'created' => $created,
            'score' => round($eventData['score'] * 100),
        ], \STCall\Entity\EventHappening\HistoryRecord::class);
        $eventHappening->getHistoryRecords()->add($historyRecord);
        $paragraph
            ->getEventHappenings()
            ->add($eventHappening);
        return $paragraph;
    }

    /**
     *
     * @param \STCall\Entity\Paragraph $paragraph
     * @param \STCompany\Entity\Event\EventCollection $eventCollection
     * @param array $eventChange
     * @return \STCall\Entity\Paragraph
     *
     * @todo Move feature to separate class and make logic more easier
     */
    protected function applyEventChanges(
        \STCall\Entity\Paragraph $paragraph,
        \STCompany\Entity\Event\EventCollection $eventCollection,
        array $eventChange,
    ): \STCall\Entity\Paragraph {
        $correctedEventId = $eventChange['corrected_event_id'];
        $originalEventId = $eventChange['original_event_id'];

        $correctedEvent = $correctedEventId > 0 && $eventCollection->offsetExists($correctedEventId)
            ? $eventCollection->offsetGet($correctedEventId)
            : null;
        $originalEvent = $originalEventId > 0 && $eventCollection->offsetExists($originalEventId)
            ? $eventCollection->offsetGet($originalEventId)
            : null;

        $eventHappenings = $paragraph->getEventHappenings();

        // confirm event
        if (
            $correctedEventId === $originalEventId
            && empty($eventChange['event_highlight'])
            && empty($eventChange['event_en_highlight'])
            && $eventHappenings->offsetExists($correctedEventId)
        ) {
            if (!$eventCollection->offsetExists($correctedEventId)) {
                return $paragraph;
            }

            /** @var EventHappening $eventHappening */
            $eventHappening = $eventHappenings->offsetGet($correctedEventId);
            $eventHappening
                ->isConfirmed(true)
                ->setScore(100)
                ->isDeleted(false);

            $historyRecord = $this->hydrate([
                'event' => $correctedEvent,
                'author' => $eventChange['user_name'],
                'created' => $eventChange['created'],
                'personal_avatar_file' => $this->getAvatarFile($eventChange['user_id']),
                'is_robonote_avatar' => false,
            ], \STCall\Entity\EventHappening\HistoryRecord::class);
            $eventHappening->getHistoryRecords()->add($historyRecord);
            return $paragraph;
        }

        // change event
        if ($correctedEventId > 0 && $originalEventId > 0 && $correctedEvent instanceof \STCompany\Entity\Event\Event) {
            if (!$eventCollection->offsetExists($originalEventId)) {
                return $paragraph;
            }
            if (!$eventHappenings->offsetExists($originalEventId)) {
                return $paragraph;
            }
            /** @var EventHappening $eventHappening */
            $eventHappening = clone $eventHappenings->offsetGet($originalEventId);
            $eventHappening
                ->setEvent($correctedEvent)
                ->isChanged(true)
                ->isConfirmed(true)
                ->setScore(100)
                ->isDeleted(false);

            if (!empty($eventChange['event_highlight'])) {
                $eventHappening->setMainPointPhrase($eventChange['event_highlight']);
            }
            if (!empty($eventChange['event_en_highlight'])) {
                $eventHappening->setEnMainPointPhrase($eventChange['event_en_highlight']);
            }

            $eventHappenings->offsetUnset($originalEventId);
            $eventHappenings->add($eventHappening);

            $historyRecord = $this->hydrate([
                'event' => $correctedEvent,
                'author' => $eventChange['user_name'],
                'created' => $eventChange['created'],
                'personal_avatar_file' => $this->getAvatarFile($eventChange['user_id']),
                'is_robonote_avatar' => false,
            ], \STCall\Entity\EventHappening\HistoryRecord::class);
            $eventHappening->getHistoryRecords()->add($historyRecord);
            return $paragraph;
        }

        // new event
        if (
            $correctedEventId > 0
            && $originalEventId <= 0
            && $correctedEvent instanceof \STCompany\Entity\Event\Event
            && (
                !$eventHappenings->offsetExists($correctedEventId)
                ||
                $eventHappenings->offsetGet($correctedEventId)->isDeleted()
            )
        ) {
            /** @var EventHappening $eventHappening */
            $eventHappening = $eventHappenings->offsetExists($correctedEventId)
                ? $eventHappenings->offsetGet($correctedEventId)
                : $this->hydrate([
                    'event' => $correctedEvent,
                    'text' => $paragraph->getText() ?? '',
                    'en_text' => $paragraph->getEnText() ?? '',
                    'main_point_phrase' => $paragraph->getText() ?? '',
                    'en_main_point_phrase' => $paragraph->getEnText() ?? '',
                ], \STCall\Entity\EventHappening::class);
            $eventHappening
                ->isChanged(true)
                ->isConfirmed(true)
                ->setScore(100)
                ->isDeleted(false);

            $eventHappenings->add($eventHappening);

            if (!empty($eventChange['event_highlight'])) {
                $eventHappening->setMainPointPhrase($eventChange['event_highlight']);
            }
            if (!empty($eventChange['event_en_highlight'])) {
                $eventHappening->setEnMainPointPhrase($eventChange['event_en_highlight']);
            }

            $historyRecords = $eventHappening->getHistoryRecords();

            if ($historyRecords->isEmpty()) {
                $eventHappening->getHistoryRecords()->add(
                    $this->hydrate(
                        [
                            'event' => null,
                            'author' => EventHappening\HistoryRecord::ALGO_EVENT_AUTHOR,
                            'created' => Carbon::now(),
                            'personal_avatar_file' => UserAvatarService::DEFAULT_USER_AVATAR_DIR,
                            'is_robonote_avatar' => true,
                        ],
                        \STCall\Entity\EventHappening\HistoryRecord::class
                    )
                );
            }

            $historyRecord = $this->hydrate([
                'event' => $correctedEvent,
                'author' => $eventChange['user_name'],
                'created' => $eventChange['created'],
                'personal_avatar_file' => $this->getAvatarFile($eventChange['user_id']),
                'is_robonote_avatar' => false,
            ], \STCall\Entity\EventHappening\HistoryRecord::class);
            $eventHappening->getHistoryRecords()->add($historyRecord);

            return $paragraph;
        }

        // delete event
        if ($eventHappenings->offsetExists($originalEventId) && $correctedEventId <= 0) {
            /** @var EventHappening $eventHappening */
            $eventHappening = $eventHappenings->offsetGet($originalEventId);
            $eventHappening
                ->isConfirmed(true)
                ->setScore(100)
                ->isChanged(true)
                ->isDeleted(true);
            $historyRecord = $this->hydrate([
                'event' => $originalEvent,
                'author' => $eventChange['user_name'],
                'created' => $eventChange['created'],
                'personal_avatar_file' => $this->getAvatarFile($eventChange['user_id']),
                'is_robonote_avatar' => false,
                'is_empty_icon' => true,
            ], \STCall\Entity\EventHappening\HistoryRecord::class);
            $eventHappening->getHistoryRecords()->add($historyRecord);
            return $paragraph;
        }

        return $paragraph;
    }

    /**
     *
     * @param Company $company
     * @return array
     */
    public function getCallStatusesByCompany(
        Company $company
    ): array {
        return $this->callsTable->getCallStatusesByCompanyId($company->getId());
    }

    /**
     *
     * @param Company $company
     * @return array
     */
    public function getCallLanguagesByCompany(
        Company $company
    ): array {
        return $this->callsTable->getCallLanguagesByCompanyId($company->getId());
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getCallsDateRange(
        int $companyId
    ): array {
        return $this->callsTable->getCallsDateRange($companyId);
    }

    public function getClientCallsByDateRange(
        Company $company,
        string $clientId,
        string $dateColumn = 'created_at',
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): CallCollection {
        $callIds = $this->callsTable->getClientCallIdsByDateRange(
            $company->getId(),
            $clientId,
            $dateColumn,
            $startDate,
            $endDate
        );

        if (empty($callIds)) {
            return new CallCollection();
        }

        $callCollection = new CallCollection();
        $callsData = $this->callsTable->getCall($callIds, $company->getId());
        $callFactory = new CallFactory();

        foreach ($callsData as $callData) {
            $callCollection->add($callFactory->createCall($callData, withConstructor: true));
        }

        $callCollection->usort(function ($a, $b) {
            return $a->getTime()->lt($b->getTime()) ? -1 : 1;
        });

        $paragraphsData = $this->callsParagraphsTable->getParagraphs(
            $company->getId(),
            $callIds,
            $company->getEncryptionKey()
        );

        foreach ($paragraphsData as $paragraphData) {
            if (!$callCollection->offsetExists($paragraphData['call_id'])) {
                continue;
            }

            $callCollection
                ->offsetGet($paragraphData['call_id'])
                ->getParagraphs()
                ->add($this->hydrate($paragraphData, Paragraph::class, withConstructor: true));
        }

        return $callCollection;
    }

    public function getClientIdsByCompanyId(int $companyId): array
    {
        return $this->callsTable->getClientIdsByCompanyId($companyId);
    }

    /**
     *
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return void
     */
    public function deleteCalls(
        int $companyId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): void {
        $this->callsTable->deleteCalls($companyId, $startDate, $endDate);
    }
}
