<?php

declare(strict_types=1);

namespace STCall\Service\Export;

use Aws\S3\S3Client;
use RuntimeException;

class ExternalFileService
{
    public const string DEFAULT_AWS_S3_REGION = 'eu-west-3';

    public function __construct(private readonly AwsSdkFactory $awsSdkFactory)
    {
    }

    public function saveFileContent(string $fileName, string $content, string $bucketName, string $region = null): void
    {
        $s3Client = $this->getS3Client($region);

        $result = $s3Client->putObject([
            'Bucket' => $bucketName,
            'Body' => $content,
            'Key' => $fileName,
        ]);

        if (200 !== $result['@metadata']['statusCode']) {
            throw new RuntimeException('File upload to s3 was failed');
        }
    }

    public function getTemporaryUrl(
        string $fileName,
        string $bucketName,
        string $region = null,
        int $timeInMinutes = 60
    ): string {
        $s3Client = $this->getS3Client($region);

        if (!$s3Client->doesObjectExist($bucketName, $fileName)) {
            throw new RuntimeException('Object is not exists');
        }

        $cmd = $s3Client->getCommand('GetObject', [
            'Bucket' => $bucketName,
            'Key' => $fileName,
        ]);

        $request = $s3Client->createPresignedRequest($cmd, '+' . $timeInMinutes . ' minutes');

        return (string) $request->getUri();
    }

    private function getS3Client(?string $region): S3Client
    {
        $region = $region ?: self::DEFAULT_AWS_S3_REGION;

        $awsSdk = $this->awsSdkFactory->create($region);

        return $awsSdk->createS3();
    }
}
