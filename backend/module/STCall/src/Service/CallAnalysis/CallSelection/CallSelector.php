<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\CallSelection;

use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;

class CallSelector
{
    public function __construct(
        private readonly CallsTable $callsTable,
        private readonly CallFactory $callFactory,
    ) {
    }

    /**
     * @throws NotFoundApiException
     */
    public function getCall(string $callId, int $companyId): Call
    {
        $callData = $this->callsTable->getCall($callId, $companyId);

        return $this->callFactory->createCall($callData);
    }
}
