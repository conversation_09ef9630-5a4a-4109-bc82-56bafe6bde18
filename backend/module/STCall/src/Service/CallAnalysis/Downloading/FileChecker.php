<?php

namespace STCall\Service\CallAnalysis\Downloading;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use STApi\Entity\Exception\CallUpload\FileIsTooBigException;

class FileChecker
{
    public const MAX_FILE_SIZE_IN_BYTES = 350 * 1024 * 1024;

    /**
     * @throws GuzzleException
     */
    public function checkFileSize(string $filePath): void
    {
        try {
            $response = (new Client())->head($filePath, [
                'timeout' => 10,
                'connect_timeout' => 2,
            ],);
            $contentLength = $response->getHeaderLine('Content-Length');

            if (!empty($contentLength)) {
                $size = (int) $contentLength;

                if ($size > self::MAX_FILE_SIZE_IN_BYTES) {
                    throw new FileIsTooBigException('File size is ' . $size . ' bytes');
                }
            }
        } catch (Exception $e) {
        }
    }
}
