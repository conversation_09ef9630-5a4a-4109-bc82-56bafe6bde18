<?php

namespace STCall\Service\CallAnalysis\Downloading;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use STApi\Entity\Exception\CallUpload\FileIsTooBigException;
use ST<PERSON><PERSON>\Entity\Exception\NoAccessToFileApiException;

class FileDownloader
{
    public function __construct(private readonly FileChecker $fileChecker)
    {
    }

    /**
     * @throws NoAccessToFileApiException
     * @throws FileIsTooBigException
     * @throws GuzzleException
     */
    public function getCallFileContent(string $fileUrl): string
    {
        $content = '';
        $possibleUrls = array_unique([
            $fileUrl,
            urldecode($fileUrl),
        ]);

        foreach ($possibleUrls as $possibleUrl) {
            if (!empty($content)) {
                break;
            }

            $this->fileChecker->checkFileSize($possibleUrl);

            try {
                $fileResponseBody = (new Client())->get(
                    $possibleUrl,
                    [
                        'timeout' => 60,
                        'headers' => [
                            //'Range' => 'bytes=0-' . self::MAX_FILE_SIZE_IN_BYTES,
                        ],
                        'connect_timeout' => 2,
                    ],
                )->getBody();

                $size = $fileResponseBody->getSize();

                if ($size > FileChecker::MAX_FILE_SIZE_IN_BYTES) {
                    throw new FileIsTooBigException('File size is ' . $size . ' bytes');
                }

                $content = $fileResponseBody->getContents();
            } catch (FileIsTooBigException $e) {
                throw $e;
            } catch (Exception $e) {
            }
        }

        if (empty($content)) {
            throw new NoAccessToFileApiException('There is no access to file "' . implode(', ', $possibleUrls) . '"');
        }

        $fileSize = strlen($content);

        if ($fileSize >= FileChecker::MAX_FILE_SIZE_IN_BYTES) {
            throw new FileIsTooBigException('File size is ' . $fileSize . ' bytes');
        }

        return $content;
    }
}
