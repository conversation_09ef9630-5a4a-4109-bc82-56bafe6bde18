<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

use ST<PERSON>all\Entity\ParagraphCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Salad extends AbstractParagraphResponseConverter
{
    use BaseHydratorTrait;

    /**
     *
     * @inheritdoc
     */
    public function convert(mixed $response): ParagraphCollection
    {
        $paragraphCollection = new \STCall\Entity\ParagraphCollection();

        foreach ($response->sentence_level_timestamps as $paragraphNumber => $paragraphData) {
            $paragraphCollection->add($this->hydrate([
                'paragraph_number' => $paragraphNumber,
                'start_time' => (float) $paragraphData->start,
                'end_time' => (float) $paragraphData->end,
                'speaker_number' => $this->getSpeakerNumber($paragraphData->speaker),
                'text' => $paragraphData->text,
            ], \STCall\Entity\Paragraph::class));
        }

        return $paragraphCollection;
    }

    /**
     * @param string $speaker
     * @return int
     */
    private function getSpeakerNumber(string $speaker): int
    {
        if (preg_match('/(\d+)$/', $speaker, $matches)) {
            return (int) $matches[0];
        }

        // Salad sometimes returns "Unknown" for speaker
        return 0;
    }
}
