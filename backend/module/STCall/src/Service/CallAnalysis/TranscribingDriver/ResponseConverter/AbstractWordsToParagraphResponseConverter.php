<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

use ST<PERSON>all\Entity\Paragraph;
use ST<PERSON>all\Entity\ParagraphCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

abstract class AbstractWordsToParagraphResponseConverter implements ResponseConverterInterface
{
    use BaseHydratorTrait;

    /**
     *
     * @inheritdoc
     */
    public function convert(mixed $response): ParagraphCollection
    {
        $paragraphCollection = new ParagraphCollection();
        $text = '';
        $paragraphStartTime = 0.0;
        $paragraphEndTime = 0.0;
        $wordItems = $this->getWordsItems($response);
        $currentSpeaker = null;

        if (!empty($wordItems)) {
            for ($i = 0, $iMax = \count($wordItems); $i < $iMax; $i++) {
                // If there is no speaker label in the item we assume the speaker hasn't changed
                $currentSpeaker = $this->getSpeaker($wordItems[$i]) ?? $currentSpeaker;
                $startTime = $wordItems[$i]->start_time ?? null;
                $endTime = $wordItems[$i]->end_time ?? null;
                $isPunctuation = $this->isPunctuation($wordItems[$i]);

                if ($paragraphStartTime === 0.0) {
                    $paragraphStartTime = $startTime;
                }

                if ($endTime !== null) {
                    $paragraphEndTime = $endTime;
                }

                $word = $wordItems[$i]->alternatives[0]->content;

                // If this is a start of a paragraph(text is empty) or the item is punctuation we add it to the text, otherwise we add it to the text with a space
                $text .= $text === '' || $isPunctuation ? $word : ' ' . $word;

                if (isset($wordItems[$i + 1])) {
                    $nextSpeaker = $this->getSpeaker($wordItems[$i + 1]);

                    // If the next item speaker is not the same as a current one we assume this is the end of a paragraph, so we can save it and start a new paragraph
                    if ($nextSpeaker !== null && $currentSpeaker !== null && $nextSpeaker !== $currentSpeaker) {
                        $paragraphCollection->add($this->hydrate([
                            'paragraph_number' => $paragraphCollection->count(),
                            'start_time' => (float) $paragraphStartTime,
                            'end_time' => (float) $paragraphEndTime,
                            'speaker_number' => $currentSpeaker,
                            'text' => $text,
                        ], Paragraph::class));
                        $text = '';
                        $paragraphStartTime = 0.0;
                    }
                }
            }

            $paragraphCollection->add($this->hydrate([
                'paragraph_number' => $paragraphCollection->count(),
                'start_time' => (float) $paragraphStartTime,
                'end_time' => (float) $paragraphEndTime,
                'speaker_number' => $currentSpeaker,
                'text' => $text,
            ], Paragraph::class));
        }

        return $paragraphCollection;
    }

    /**
     * @param $responseItem
     * @return bool
     */
    protected function isPunctuation($responseItem): bool
    {
        return $responseItem->type === 'punctuation';
    }

    /**
     * @param mixed $response
     * @return iterable
     */
    abstract protected function getWordsItems(mixed $response): iterable;

    /**
     * @param $responseItem
     * @return int|null
     */
    abstract protected function getSpeaker($responseItem): ?int;
}
