<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use ST<PERSON>all\Helper\AwsHelper;

class AwsDriver extends AbstractDriver implements TwoStepsDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const STATUS_COMPLETED = 'COMPLETED';
    protected const STATUS_FAILED = 'FAILED';
    protected const STATUS_IN_PROGRESS = 'IN_PROGRESS';

    protected const JOB_IS_ALREADY_EXISTS_TEXT = 'The requested job name already exists. Use a different job name.';

    protected const DEFAULT_RESULT_FILE_EXTENSION = 'json';

    protected const LANGUAGES = [
        'af' => 'af-ZA',
        'ar' => 'ar-SA',
        'ar-AE' => 'ar-AE',
        'ar-SA' => 'ar-SA',
        'zh' => 'zh-CN',
        'zh-CN' => 'zh-CN',
        'zh-TW' => 'zh-TW',
        'da' => 'da-DK',
        'nl' => 'nl-NL',
        'en-AU' => 'en-AU',
        'en-GB' => 'en-GB',
        'en-IN' => 'en-IN',
        'en-IE' => 'en-IE',
        'en-NZ' => 'en-NZ',
        'en-AB' => 'en-AB',
        'en-ZA' => 'en-ZA',
        'en-US' => 'en-US',
        'en-WL' => 'en-WL',
        'fr' => 'fr-FR',
        'fr-FR' => 'fr-FR',
        'fr-CA' => 'fr-CA',
        'fa' => 'fa-IR',
        'de' => 'de-DE',
        'de-DE' => 'de-DE',
        'de-CH' => 'de-CH',
        'he' => 'he-IL',
        'hi' => 'hi-IN',
        'id' => 'id-ID',
        'it' => 'it-IT',
        'ja' => 'ja-JP',
        'ko' => 'ko-KR',
        'ms' => 'ms-MY',
        'pt' => 'pt-PT',
        'pt-PT' => 'pt-PT',
        'pt-BR' => 'pt-BR',
        'ru' => 'ru-RU',
        'es' => 'es-ES',
        'es-ES' => 'es-ES',
        'es-US' => 'es-US',
        'sv' => 'sv-SE',
        'ta' => 'ta-IN',
        'te' => 'te-IN',
        'th' => 'th-TH',
        'tr' => 'tr-TR',
        'vi' => 'vi-VN',
    ];

    /**
     *
     * @return string
     * @throws \Aws\TranscribeService\Exception\TranscribeServiceException
     */
    public function createJob(): string
    {
        $transcriptionName = AwsHelper::getAwsCompatibleFileName($this->call->getId());
        $awsSdk = $this->getAwsSdk();
        $transcribeServiceClient = $awsSdk->createTranscribeService();
        $language = static::LANGUAGES[$this->call->getLanguage()] ?? null;
        $requestParams = [
            'Media' => [
                'MediaFileUri' => $this->getObjectUrl($this->call->getS3FilePath()),
            ],
            'TranscriptionJobName' => $transcriptionName,
            'OutputBucketName' => $this->getBucketName(),
            'OutputKey' => $this->getCompanyDirName() . $transcriptionName . '.' . static::DEFAULT_RESULT_FILE_EXTENSION,
            'Settings' => [
                'ShowSpeakerLabels' => true,
                'MaxSpeakerLabels' => 2,
            ],
        ];
        if (!is_null($language)) {
            $requestParams['LanguageCode'] = $language;
        } else {
            $requestParams['IdentifyLanguage'] = true;
        }
        try {
            $transcribeServiceClient->startTranscriptionJob($requestParams);
        } catch (\Aws\TranscribeService\Exception\TranscribeServiceException $e) {
            if ($e->getAwsErrorMessage() === static::JOB_IS_ALREADY_EXISTS_TEXT) {
                return $transcriptionName;
            }
            throw $e;
        }

        $this->createTranscribingRequestLog($transcriptionName, \STCall\Service\CallAnalysis\TranscribingDistributionStep::TRANSCRIBING_AWS_DRIVER);

        return $transcriptionName;
    }

    /**
     *
     * @param string $transcriptionName
     * @return \STCall\Entity\ParagraphCollection
     * @throws \RuntimeException
     * @throws \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException
     */
    public function collectData(string $transcriptionName): \STCall\Entity\ParagraphCollection
    {
        $awsSdk = $this->getAwsSdk();
        $transcribeServiceClient = $awsSdk->createTranscribeService();

        $transcriptionJob = $transcribeServiceClient->getTranscriptionJob([
            'TranscriptionJobName' => $transcriptionName,
        ]);
        $transcriptionJobStatus = $transcriptionJob['TranscriptionJob']['TranscriptionJobStatus'];

        if ($transcriptionJobStatus === static::STATUS_FAILED) {
            throw new \RuntimeException('Transcribing job "' . $transcriptionName . '" failed');
        }
        if ($transcriptionJobStatus === static::STATUS_IN_PROGRESS) {
            throw new \STCall\Service\CallAnalysis\Transcribing\TwoSteps\NotReadyException();
        }
        if ($transcriptionJobStatus !== static::STATUS_COMPLETED) {
            throw new \RuntimeException('Unknown status "' . $transcriptionJobStatus . '" for job"' . $transcriptionName . '"');
        }

        $resultFile = $this->getBucketName() . '/' . $this->getCompanyDirName() . $transcriptionName . '.' . static::DEFAULT_RESULT_FILE_EXTENSION;
        $content = $this->getFileFromS3ByObjectPath($resultFile);
        $result = json_decode($content);

        return $this->getResponseToCollectionConverter()->convertDriverResponseToParagraphCollection(
            $result,
            ResponseConverter\ResponseToCollectionConverter::AWS,
            $this->call
        );
    }

    /**
     *
     * @param array $items
     * @return array
     */
    protected function getParagraphsData(array $items): array
    {
        $currentSpeaker = null;
        $startTime = null;
        $endTime = null;
        $paragraphs = [];
        $currentParagraph = '';

        foreach ($items as $item) {
            if (isset($item->speaker_label) && $currentSpeaker !== $item->speaker_label) {
                $paragraphs[] = [
                    'text' => $currentParagraph,
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'speaker_number' => substr($item->speaker_label, -1),
                ];
                $startTime = $item->start_time;
                $endTime = $item->end_time;
                $currentParagraph = '';
                $currentSpeaker = $item->speaker_label;
            }
            if ($item->type === 'pronunciation' && $currentParagraph !== '') {
                $currentParagraph .= ' ';
            }
            $currentParagraph .= current($item->alternatives)->content;
            $endTime = $item->end_time ?? $endTime;
        }
        return $paragraphs;
    }
}
