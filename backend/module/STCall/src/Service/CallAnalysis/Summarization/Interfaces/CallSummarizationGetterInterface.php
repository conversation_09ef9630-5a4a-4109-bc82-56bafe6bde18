<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\Summarization\Interfaces;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ReflectionException;
use ST<PERSON><PERSON>\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallSummarization;
use STCompany\Entity\Company;

interface CallSummarizationGetterInterface
{
    /**
     * @param Company $company
     * @param Call $call
     * @return CallSummarization
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     * @throws GuzzleException
     * @throws JsonException
     */
    public function getSummarization(Company $company, Call $call): CallSummarization;
}
