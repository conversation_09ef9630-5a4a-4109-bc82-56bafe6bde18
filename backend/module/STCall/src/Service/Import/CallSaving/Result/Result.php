<?php

namespace STCall\Service\Import\CallSaving\Result;

class Result
{
    /**
     * @var \STCall\Entity\Call
     */
    protected \STCall\Entity\Call $call;

    /**
     * @var bool
     */
    protected bool $isUpdate = false;

    /**
     * @return \STCall\Entity\Call
     */
    public function getCall(): \STCall\Entity\Call
    {
        return $this->call;
    }

    /**
     * @param \STCall\Entity\Call $call
     * @return Result
     */
    public function setCall(\STCall\Entity\Call $call): Result
    {
        $this->call = $call;
        return $this;
    }

    /**
     * @return bool
     */
    public function getIsUpdate(): bool
    {
        return $this->isUpdate;
    }

    /**
     * @param bool $isUpdate
     * @return Result
     */
    public function setIsUpdate(bool $isUpdate): Result
    {
        $this->isUpdate = $isUpdate;
        return $this;
    }

    /**
     * @param bool|null $isUpdate
     * @return bool|Result
     */
    public function isUpdate(?bool $isUpdate = null): bool|Result
    {
        if (is_null($isUpdate)) {
            return $this->isUpdate;
        }
        $this->isUpdate = $isUpdate;
        return $this;
    }
}
