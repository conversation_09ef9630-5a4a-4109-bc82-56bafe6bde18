<?php

namespace STCall\Service\Import\CallSaving;

class ChatCallSaving extends AbstractCallSaving
{
    /**
     * @return Result\Result
     * @throws \STApi\Entity\Exception\CallUpload\IdenticalFileException
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function saveCall(): \STCall\Service\Import\CallSaving\Result\Result
    {
        $this->defineExistedCall();
        $this->defineIsUpdate();
        $this->defineExistedParagraphs();
        $this->validateCall();
        $this->defineAdditionalParams();
        $this->updateCallIfNeeded();

        $this->callsParagraphsTable->saveParagraphs($this->getCall()->getParagraphs(), $this->uploadParams->getCompany()->getEncryptionKey());
        $this->callsTable->saveCall($this->getCall());

        return (new Result\Result())
            ->setCall($this->getCall())
            ->isUpdate($this->isUpdate());
    }

    /**
     * @description Existed paragraphs can not be updated. Overwrite sent paragraphs by existed
     *
     * @return \STCall\Entity\ParagraphCollection
     */
    protected function defineExistedParagraphs(): \STCall\Entity\ParagraphCollection
    {
        $call = $this->getCall();
        $company = $this->uploadParams->getCompany();
        $paragraphsData = $this->callsParagraphsTable->getParagraphs($company->getId(), $call->getId(), $company->getEncryptionKey());
        foreach ($paragraphsData as $paragraphData) {
            /** @var \STCall\Entity\Paragraph $paragraph */
            $paragraph = $this->hydrate($paragraphData, \STCall\Entity\Paragraph::class, withConstructor: true);
            $call
                ->getParagraphs()
                ->add($paragraph, $paragraph->getParagraphNumber());
        }
        return $call->getParagraphs();
    }

    /**
     * @return void
     * @throws \STApi\Entity\Exception\CallUpload\IdenticalFileException
     */
    protected function validateCall(): void
    {
        if (is_null($this->isUpdate())) {
            throw new \RuntimeException('isUpdated property is not defined');
        }
        $call = $this->getCall();
        if (!$this->isUpdate()) {
            $existedCallId = $this->callsTable->getCallIdByFileHashAndDuration($this->uploadParams->getCompany()->getId(), $call->getFileHash(), $call->getDuration());
            if ($existedCallId && $call->getId() !== $existedCallId) {
                throw new \STApi\Entity\Exception\CallUpload\IdenticalFileException('An identical chat has been already added - ' . $existedCallId);
            }
        }
    }

    /**
     * @return \STCall\Entity\Call
     */
    protected function defineAdditionalParams(): \STCall\Entity\Call
    {
        $this->getCall()
            ->isSentToTranscribing(true)
            ->setAgentId($this->agentId)
            ->setUploadedUserId(!is_null($this->uploadParams->getUser()) ? $this->uploadParams->getUser()->getId() : null)
            ->setUploadedTime(\Carbon\Carbon::now());
        return $this->getCall();
    }

    /**
     * @return \STCall\Entity\Call
     */
    protected function updateCallIfNeeded(): \STCall\Entity\Call
    {
        $existedCall = $this->existedCall;
        if ($this->isUpdate()) {
            // if update, run translation and analyze again
            $this->getCall()
                ->isAnalyzed(false)
                ->isTranslated(false)
                ->setLanguage($existedCall['call_language'])
                ->isTranscribed($existedCall['is_transcribed'])
                ->isSpeakersRolesDetected($existedCall['is_speakers_roles_detected'])
                ->isChecklistCompleted($existedCall['is_checklist_completed'])
                ->isSummarizationCompleted($existedCall['is_summarization_completed'])
                ->isLlmEventsDetected($existedCall['is_llm_events_detected'])
                ->setAnalyzedAt($existedCall['analyzed_at'])
                ->setFileHash($existedCall['file_hash'])
                ->setTranscribingDriver($existedCall['transcribing_driver'])
                ->setTranslationDriver($existedCall['translation_driver']);
        }
        return $this->getCall();
    }
}
