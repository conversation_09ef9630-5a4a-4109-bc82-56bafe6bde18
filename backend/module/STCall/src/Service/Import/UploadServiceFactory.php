<?php

declare(strict_types=1);

namespace STCall\Service\Import;

class UploadServiceFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return UploadService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): UploadService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return UploadService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): UploadService
    {
        $callsTable = $container->get(\STCall\Data\CallsTable::class);
        $clientsTable = $container->get(\STCompany\Data\ClientsTable::class);
        $usersTable = $container->get(\STUser\Data\UsersTable::class);
        $roles = $container->get(\STCompany\Data\RolesTable::class);
        $usersCompaniesRolesTable = $container->get(\STCompany\Data\UsersCompaniesRolesTable::class);
        $teamsTable = $container->get(\STCompany\Data\TeamsTable::class);
        $usersTeamsTable = $container->get(\STCompany\Data\UsersTeamsTable::class);
        $callsParagraphsTable = $container->get(\STCall\Data\CallsParagraphsTable::class);
        $apiApplicationsTable = $container->get(\STApi\Data\ApiApplicationsTable::class);
        $config = $container->get('config');
        $awsConfig = $config['aws'];
        return new UploadService(
            $callsTable,
            $clientsTable,
            $usersTable,
            $roles,
            $usersCompaniesRolesTable,
            $teamsTable,
            $usersTeamsTable,
            $callsParagraphsTable,
            $apiApplicationsTable,
            $awsConfig,
        );
    }
}
