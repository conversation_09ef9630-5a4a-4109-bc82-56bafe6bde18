<?php

declare(strict_types=1);

namespace STCall\Service\Webhooks\Services;

use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;

class SummarizationWebhookService implements WebhookServiceInterface
{
    public function getType(): string
    {
        return self::WEBHOOK_TYPE_SUMMARIZATION;
    }

    public function filterData(array $data): array
    {
        unset($data['call_id'], $data['company_id']);

        return $data;
    }
}
