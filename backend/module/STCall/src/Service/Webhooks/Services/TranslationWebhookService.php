<?php

declare(strict_types=1);

namespace STCall\Service\Webhooks\Services;

use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;

class TranslationWebhookService extends BaseWebhookService implements WebhookServiceInterface
{
    public function getType(): string
    {
        return self::WEBHOOK_TYPE_TRANSLATION;
    }

    public function filterData(array $data): array
    {
        $keysToKeep = [
            'call_time',
            'paragraph_number',
            'speaker_number',
            'text',
            'en_text'
        ];

        return $this->filterInnerArrays($data, $keysToKeep);
    }
}
