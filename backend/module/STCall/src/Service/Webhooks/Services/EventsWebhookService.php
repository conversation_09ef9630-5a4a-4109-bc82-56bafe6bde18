<?php

declare(strict_types=1);

namespace STCall\Service\Webhooks\Services;

use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;

class EventsWebhookService extends BaseWebhookService implements WebhookServiceInterface
{
    public function getType(): string
    {
        return self::WEBHOOK_TYPE_EVENTS;
    }

    public function filterData(array $data): array
    {
        $filteredData = [];
        foreach ($data as $paragraphData) {
            $events = [];
            foreach ($paragraphData['event_happenings'] as $eventData) {
                $events[] = [
                    'role_id' => $eventData['event']['role']['id'],
                    'role_name' => $eventData['event']['role']['name'],
                    'event_name' => $eventData['event']['name'],
                    'main_point_phrase' => $eventData['main_point_phrase'],
                    'en_main_point_phrase' => $eventData['en_main_point_phrase'],
                    'text' => $eventData['text'],
                    'en_text' => $eventData['en_text'],
                ];
            }

            $filteredData[] = [
                'call_time' => $paragraphData['call_time'],
                'paragraph_number' => $paragraphData['paragraph_number'],
                'paragraph_start_time' => $paragraphData['start_time'],
                'speaker_role' => $paragraphData['speaker_role'],
                'events' => $events,
            ];
        }

        return $filteredData;
    }
}
