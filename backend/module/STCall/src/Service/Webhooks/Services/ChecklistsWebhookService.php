<?php

declare(strict_types=1);

namespace STCall\Service\Webhooks\Services;

use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;

class ChecklistsWebhookService implements WebhookServiceInterface
{
    public function getType(): string
    {
        return self::WEBHOOK_TYPE_CHECKLISTS;
    }

    public function filterData(array $data): array
    {
        $filteredData = [];
        foreach ($data as $item) {
            unset($item['checklist']['company_id'], $item['checklist']['calls_teams'], $item['checklist']['description']);

            $filteredChecklistPoints = [];
            foreach ($item['checklist_points'] as $checklistPointData) {
                unset($checklistPointData['checklist_id'], $checklistPointData['order'], $checklistPointData['updated_at']);
                $filteredChecklistPoints[] = $checklistPointData;
            }
            $item['checklist_points'] = $filteredChecklistPoints;
            $filteredData[] = $item;
        }

        return $filteredData;
    }
}
