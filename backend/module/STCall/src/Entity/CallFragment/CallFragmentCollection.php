<?php

declare(strict_types=1);

namespace STCall\Entity\CallFragment;

class CallFragmentCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $callFragment
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $callFragment, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($callFragment instanceof \STCall\Entity\CallFragment\CallFragment)) {
            throw new \RuntimeException('Call fragment must be an instace of "\STCall\Entity\CallFragment\CallFragment"');
        }
        parent::add($callFragment, $key ?? null);
        return $this;
    }

    /**
     *
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = []): array
    {
        $result = [];
        foreach ($this as $callFragment) {
            $result[] = $callFragment->toArray($attributes);
        }
        return $result;
    }

    /**
     *
     * @param int $paragraphNumber
     * @return CallFragment
     * @throws \RuntimeException
     */
    public function getFragmentByParagraphNumber(int $paragraphNumber): CallFragment
    {
        foreach ($this as $fragment) {
            if ($fragment->hasParagraph($paragraphNumber)) {
                return $fragment;
            }
        }
        throw new \RuntimeException('There is no fragment for paragraph number ' . $paragraphNumber);
    }
}
