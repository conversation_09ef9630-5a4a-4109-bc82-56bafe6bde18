<?php

declare(strict_types=1);

namespace STCall\Entity\Import;

class ChatApiUploadDriver extends BaseUploadDriver implements ChatCallDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const ID_PREFIX = '';

    /**
     *
     * @var array
     */
    protected array $options;

    /**
     *
     * @var array
     */
    protected array $clientOptions;

    /**
     *
     * @param array $options
     * @return DriverInterface
     * @throws \RuntimeException
     */
    public function setOptions(array $options): DriverInterface
    {
        if (!isset($options['time'])) {
            throw new \STApi\Entity\Exception\InvalidCompanyApiRequestException('Empty "time" option');
        }
        if (!isset($options['agent_id'])) {
            throw new \STApi\Entity\Exception\InvalidCompanyApiRequestException('Empty "agent_id" option');
        }
        if (!isset($options['client_id'])) {
            throw new \STApi\Entity\Exception\InvalidCompanyApiRequestException('Empty "client_id" option');
        }

        $options['time'] = \Carbon\Carbon::parse($options['time']);
        $options['company_id'] = $this->getCompany()->getId();
        $options['call_id'] = $options['chat_call_id'] ?? uniqid(static::ID_PREFIX, true);
        $options['client_name'] = $options['client_name'] ?? $options['client_id'];
        $options['call_status'] = isset($options['chat_call_status']) ? strtolower($options['chat_call_status']) : \STCall\Data\CallsTable::CALL_STATUS_END;
        $options['company_team_id'] = $options['team_id'] ?? $options['team_name'] ?? null;
        $options['team_name'] = $options['team_name'] ?? $options['company_team_id'] ?? null;
        $options['company_agent_id'] = $options['agent_id'] ?? null;
        $options['origin'] = \STCall\Data\CallsTable::CALL_ORIGIN_API;
        $options['paragraphs_count'] = count($options['chat_call']) ?? 0;
        $options['s3_file_path'] = !empty($options['recording_file'])
                ? ($options['call_id'] . '.' . $this->getFileExtension($options['recording_file']))
                : null;
        $options['file_hash'] = $this->getFileHash();

        unset($options['team_id']);
        unset($options['agent_id']);
        $this->options = $options;

        $clientOptions = [];
        $clientOptions['client_id'] = $options['client_id'];
        $clientOptions['client_name'] = $options['client_name'] ?? $options['client_id'];
        $clientOptions['client_source'] = isset($options['client_source']) ? strtolower($options['client_source']) : null;
        $clientOptions['client_status'] = isset($options['client_status']) ? strtolower($options['client_status']) : null;
        $clientOptions['client_country'] = isset($options['client_country']) ? strtolower($options['client_country']) : null;
        $clientOptions['client_acquisition_date'] = isset($options['client_acquisition_date']) && !empty($options['client_acquisition_date']) ? \Carbon\Carbon::parse($options['client_acquisition_date']) : null;
        $clientOptions['client_converted_date'] = isset($options['client_converted_date']) && !empty($options['client_converted_date']) ? \Carbon\Carbon::parse($options['client_converted_date']) : null;
        $clientOptions['client_is_converted'] = isset($options['client_is_converted']) ? (bool) $options['client_is_converted'] : null;
        $clientOptions['client_last_transaction_date'] = isset($options['client_last_transaction_date']) && !empty($options['client_last_transaction_date']) ? \Carbon\Carbon::parse($options['client_last_transaction_date']) : null;
        $clientOptions['client_campaign_id'] = isset($options['client_campaign_id']) ? $options['client_campaign_id'] : null;
        $clientOptions['client_value'] = isset($options['client_value']) ? (float) $options['client_value'] : null;

        $this->clientOptions = $clientOptions;

        return $this;
    }

    /**
     *
     * @return \STCall\Entity\Import\Result\Result
     */
    public function run(): \STCall\Entity\Import\Result\Result
    {
        $result = new Result\Result();

        $result->getAgent()->setId($this->options['company_agent_id']);
        $result->getAgent()->setName($this->options['agent_name'] ?? $this->options['company_agent_id']);
        $result->getTeam()->setId($this->options['company_team_id']);
        $result->getTeam()->setName($this->options['team_name']);

        /** @var \STCall\Entity\ChatCall $call */
        $call = $this->hydrate($this->options, \STCall\Entity\ChatCall::class);
        $paragraphCollection = new \STCall\Entity\ParagraphCollection();
        $paragraphs = $this->options['chat_call'];
        usort($paragraphs, function ($paragraphA, $paragraphB) {
            $paragraphATime = \Carbon\Carbon::parse($paragraphA['time']);
            $paragraphBTime = \Carbon\Carbon::parse($paragraphB['time']);

            return $paragraphATime->greaterThanOrEqualTo($paragraphBTime) ? 1 : -1;
        });

        foreach ($paragraphs as $paragraphNumber => $chatCallMessage) {
            if (empty($chatCallMessage['text'])) {
                continue;
            }
            $paragraphCollection->add($this->hydrate([
                'company_id' => $this->getCompany()->getId(),
                'call_id' => $call->getId(),
                'call_time' => $call->getTime(),
                'paragraph_number' => $paragraphNumber,
                'start_time' => $paragraphNumber,
                'end_time' => $paragraphNumber + 1,
                'timestamp' => \Carbon\Carbon::parse($chatCallMessage['time']),
                'speaker_number' => $chatCallMessage['is_agent'] ? \STCall\Entity\ChatCall::AGENT_NUMBER : \STCall\Entity\ChatCall::CLIENT_NUMBER,
                'speaker_role' => $chatCallMessage['is_agent'] ? \STCall\Entity\Call::CALL_SPEAKER_ROLE_AGENT : \STCall\Entity\Call::CALL_SPEAKER_ROLE_CLIENT,
                'text' => $chatCallMessage['text'],
            ], \STCall\Entity\Paragraph::class, withConstructor: true));
        }
        $call->setParagraphs($paragraphCollection);
        $result->setCall($call);

        $client = $this->hydrate($this->clientOptions, \STCompany\Entity\Client::class);
        $result->setClient($client);
        return $result;
    }

    /**
     *
     * @param string $fileName
     * @return string
     */
    protected function getFileExtension(string $fileName): string
    {
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        return !empty($extension) ? $extension : 'mp3';
    }

    /**
     *
     * @return string
     */
    protected function getFileHash(): string
    {
        return md5(implode(' | ', array_column($this->getUploadParams()->getOptions()['chat_call'], 'text')));
    }
}
