<?php

declare(strict_types=1);

namespace STCall\Entity\Import;

abstract class CallTemplateUploadDriver extends BaseUploadDriver implements CallDriverInterface
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const ID_PREFIX = 'call_';

    protected const FAKE_CLIENT_NAME = 'No client';
    protected const FAKE_AGENT_NAME = 'No agent';

    /**
     *
     * @var string
     */
    protected string $fileName;

    /**
     *
     * @var string
     */
    protected string $s3FilePath;

    /**
     *
     * @var \Carbon\Carbon
     */
    protected \Carbon\Carbon $date;

    /**
     *
     * @var int
     */
    protected int $duration = 0;

    /**
     *
     * @var string
     */
    protected string $fileHash = '';

    /**
     *
     * @var \STCompany\Entity\User|null
     */
    protected ?\STCompany\Entity\User $agent = null;

    /**
     *
     * @param array $options
     * @return DriverInterface
     * @throws \RuntimeException
     */
    public function setOptions(array $options): DriverInterface
    {
        if (!isset($options['file_name'])) {
            throw new \RuntimeException('Empty "file_name" option');
        }

        $this->agent = $options['agent'] ?? null;
        $this->date = $options['date'] ?? \Carbon\Carbon::now();
        $this->fileName = $options['file_name'];
        $this->s3FilePath = $options['s3_file_path'];
        $this->duration = $this->getAudioDuration($this->getUploadParams()->getContent());
        $this->fileHash = $this->getFileHash();

        return $this;
    }

    /**
     *
     * @return \STCall\Entity\Import\Result\Result
     */
    public function run(): \STCall\Entity\Import\Result\Result
    {
        $result = new Result\Result();
        $callInfo = $this->getCallData();
        /** @var \STCall\Entity\Call $call */
        $call = $this->hydrate([
            'id' => uniqid(static::ID_PREFIX, true),
            'company_id' => $this->getCompany()->getId(),
            'time' => $callInfo['time'],
            'client_id' => $callInfo['client_id'],
            'client_name' => $callInfo['client_id'],
            'origin' => static::ORIGIN,
            'original_file_name' => $this->fileName,
            'duration' => $this->duration,
            's3_file_path' => $this->s3FilePath,
            'file_hash' => $this->fileHash,
            'is_sent_to_transcribing' => true,
        ], \STCall\Entity\Call::class);

        $result->setCall($call);

        $result->getClient()->setId($callInfo['client_id']);
        $result->getClient()->setName($callInfo['client_id']);

        if ($this->agent instanceof \STCompany\Entity\User) {
            $result->setAgent($this->agent);
        } else {
            $result->getAgent()->setId($callInfo['agent_id']);
        }

        return $result;
    }

    /**
     *
     * @return array
     * @throws \RuntimeException
     */
    protected function getCallData(): array
    {
        foreach ($this->getCompany()->getCallTemplates() as $template) {
            $matches = [];
            preg_match('/' . $template->getRegex() . '/', $this->fileName, $matches);

            if (!empty($matches)) {
                return [
                    'time' => \Carbon\Carbon::parse($matches[$template->getDateNumber()] . ' ' . $matches[$template->getTimeNumber()]),
                    'client_id' => $matches[$template->getClientIdNumber()] ?? static::FAKE_CLIENT_NAME,
                    'agent_id' => $matches[$template->getAgentIdNumber()] ?? static::FAKE_AGENT_NAME,
                    'call_id' => $matches[$template->getCallIdNumber()] ?? null,
                ];
            }
        }

        return [
            'time' => $this->date,
            'client_id' => static::FAKE_CLIENT_NAME,
            'agent_id' => static::FAKE_AGENT_NAME,
            'call_id' => null,
        ];
    }
}
