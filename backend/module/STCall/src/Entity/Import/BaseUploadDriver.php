<?php

declare(strict_types=1);

namespace STCall\Entity\Import;

abstract class BaseUploadDriver implements DriverInterface
{
    /**
     *
     * @var \STCall\Service\Import\UploadParams\UploadParams
     */
    protected \STCall\Service\Import\UploadParams\UploadParams $uploadParams;

    /**
     *
     * @return \STCall\Service\Import\UploadParams\UploadParams
     */
    public function getUploadParams(): \STCall\Service\Import\UploadParams\UploadParams
    {
        return $this->uploadParams;
    }

    /**
     *
     * @param \STCall\Service\Import\UploadParams\UploadParams $uploadParams
     * @return BaseUploadDriver
     */
    public function setUploadParams(\STCall\Service\Import\UploadParams\UploadParams $uploadParams): BaseUploadDriver
    {
        $this->uploadParams = $uploadParams;
        $this->setOptions($uploadParams->getOptions());
        return $this;
    }

    /**
     *
     * @return \STCompany\Entity\Company
     */
    public function getCompany(): \STCompany\Entity\Company
    {
        return $this->getUploadParams()->getCompany();
    }

    /**
     *
     * @param string|null $content
     * @return int
     */
    protected function getAudioDuration(?string $content): int
    {
        if (is_null($content)) {
            return 0;
        }
        $filePath = sys_get_temp_dir() . '/' . uniqid();
        file_put_contents($filePath, $content);
        $ffprobe = \FFMpeg\FFProbe::create();
        $duration = (int) ceil((float) $ffprobe->format($filePath)->get('duration'));
        unlink($filePath);
        return $duration;
    }

    /**
     *
     * @return string|null
     */
    protected function getFileHash(): ?string
    {
        if (is_null($this->getUploadParams()->getContent())) {
            return null;
        }
        return md5($this->getUploadParams()->getContent());
    }
}
