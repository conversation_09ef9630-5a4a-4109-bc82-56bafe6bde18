<?php

declare(strict_types=1);

namespace STCall\Entity;

use Carbon\Carbon;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Paragraph
{
    use BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var string
     */
    protected string $callId;

    /**
     *
     * @var Carbon
     */
    protected Carbon $callTime;

    /**
     *
     * @var int
     */
    protected int $paragraphNumber;

    /**
     *
     * @var float
     */
    protected float $startTime;

    /**
     *
     * @var float
     */
    protected float $endTime;

    /**
     *
     * @var Carbon|null
     */
    protected ?Carbon $timestamp = null;

    /**
     *
     * @var int
     */
    protected int $speakerNumber;

    /**
     *
     * @var string
     */
    protected string $speakerRole = Call::CALL_SPEAKER_ROLE_UNCLEAR;

    /**
     *
     * @var string
     */
    protected string $text;

    /**
     *
     * @var string|null
     */
    protected ?string $enText = null;

    /**
     *
     * @var Carbon|null
     */
    protected ?Carbon $created = null;

    /**
     *
     * @var EventHappeningCollection|null
     */
    protected ?EventHappeningCollection $eventHappenings = null;

    /**
     *
     * File constructor
     */
    public function __construct()
    {
        $this->eventHappenings = new EventHappeningCollection();
    }

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string
     */
    public function getCallId(): string
    {
        return $this->callId;
    }

    /**
     *
     * @return Carbon
     */
    public function getCallTime(): Carbon
    {
        return $this->callTime;
    }

    /**
     *
     * @return int
     */
    public function getParagraphNumber(): int
    {
        return $this->paragraphNumber;
    }

    /**
     *
     * @return int
     */
    public function getSpeakerNumber(): int
    {
        return $this->speakerNumber;
    }

    /**
     *
     * @return float
     */
    public function getStartTime(): float
    {
        return $this->startTime;
    }

    /**
     *
     * @return float
     */
    public function getEndTime(): float
    {
        return $this->endTime;
    }

    /**
     *
     * @return Carbon|null
     */
    public function getTimestamp(): ?Carbon
    {
        return $this->timestamp;
    }

    /**
     *
     * @return string
     */
    public function getText(): string
    {
        return $this->text;
    }

    /**
     *
     * @return string|null
     */
    public function getEnText(): ?string
    {
        return $this->enText;
    }

    /**
     *
     * @return Carbon
     */
    public function getCreated(): Carbon
    {
        if (is_null($this->created)) {
            $this->created = Carbon::now();
        }
        return $this->created;
    }

    /**
     *
     * @return EventHappeningCollection|null
     */
    public function getEventHappenings(): ?EventHappeningCollection
    {
        return $this->eventHappenings;
    }

    /**
     *
     * @param int $companyId
     * @return Paragraph
     */
    public function setCompanyId(int $companyId): Paragraph
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param string $callId
     * @return Paragraph
     */
    public function setCallId(string $callId): Paragraph
    {
        $this->callId = $callId;
        return $this;
    }

    /**
     *
     * @param string|Carbon $callTime
     * @return Paragraph
     */
    public function setCallTime(string|Carbon $callTime): Paragraph
    {
        $this->callTime = is_string($callTime) ? Carbon::parse($callTime) : $callTime;
        return $this;
    }

    /**
     *
     * @param int $paragraphNumber
     * @return Paragraph
     */
    public function setParagraphNumber(int $paragraphNumber): Paragraph
    {
        $this->paragraphNumber = $paragraphNumber;
        return $this;
    }

    /**
     *
     * @param int $speakerNumber
     * @return Paragraph
     */
    public function setSpeakerNumber(int $speakerNumber): Paragraph
    {
        $this->speakerNumber = $speakerNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSpeakerRole(): ?string
    {
        return $this->speakerRole;
    }

    /**
     * @param string $speakerRole
     * @return Paragraph
     */
    public function setSpeakerRole(string $speakerRole): Paragraph
    {
        $this->speakerRole = $speakerRole;
        return $this;
    }

    /**
     *
     * @param float $startTime
     * @return Paragraph
     */
    public function setStartTime(float $startTime): Paragraph
    {
        $this->startTime = $startTime;
        return $this;
    }

    /**
     *
     * @param float $endTime
     * @return Paragraph
     */
    public function setEndTime(float $endTime): Paragraph
    {
        $this->endTime = $endTime;
        return $this;
    }

    /**
     *
     * @param null|Carbon|string $timestamp
     * @return Paragraph
     */
    public function setTimestamp(null|Carbon|string $timestamp): Paragraph
    {
        $this->timestamp = is_string($timestamp) ? Carbon::parse($timestamp) : $timestamp;
        return $this;
    }

    /**
     *
     * @param string $text
     * @return Paragraph
     */
    public function setText(string $text): Paragraph
    {
        $this->text = $text;
        return $this;
    }

    /**
     *
     * @param string|null $enText
     * @return Paragraph
     */
    public function setEnText(?string $enText): Paragraph
    {
        $this->enText = $enText;
        return $this;
    }

    /**
     *
     * @param string|Carbon $created
     * @return Paragraph
     */
    public function setCreated(string|Carbon $created): Paragraph
    {
        $this->created = is_string($created) ? Carbon::parse($created) : $created;
        return $this;
    }

    /**
     *
     * @param EventHappeningCollection|null $eventHappeningCollection
     * @return Paragraph
     */
    public function setEventHappenings(?EventHappeningCollection $eventHappeningCollection): Paragraph
    {
        $this->eventHappenings = $eventHappeningCollection;
        return $this;
    }

    /**
     *
     * @param array|null $speakerRoleValues
     * @return Paragraph
     */
    public function swapSpeakerRole(?array $speakerRoleValues = []): Paragraph
    {
        if (empty($speakerRoleValues)) {
            $speakerRoleValues = [
                Call::CALL_SPEAKER_ROLE_AGENT,
                Call::CALL_SPEAKER_ROLE_CLIENT,
                Call::CALL_SPEAKER_ROLE_UNCLEAR,
            ];
        }
        $this->speakerRole = $this->getNextSpeakerRole($this->speakerRole, $speakerRoleValues);

        return $this;
    }

    /**
     *
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = []): array
    {
        $result = $this->extract($this);
        $result['event_happenings'] = !is_null($this->eventHappenings) ? $this->eventHappenings->toArray() : null;
        if (empty($attributes)) {
            return $result;
        }
        return array_filter($result, function ($attribute) use ($attributes) {
            return in_array($attribute, $attributes);
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     *
     * @param string $currentRole
     * @param array $possibleRoleValues
     * @return string
     */
    protected function getNextSpeakerRole(string $currentRole, array $possibleRoleValues): string
    {
        $speakerRoles = new \SplDoublyLinkedList();
        foreach ($possibleRoleValues as $possibleRoleValue) {
            $speakerRoles->push($possibleRoleValue);
        }
        for ($speakerRoles->rewind(); $speakerRoles->valid(); $speakerRoles->next()) {
            if ($speakerRoles->current() === $currentRole) {
                $speakerRoles->next();
                return $speakerRoles->valid() ? $speakerRoles->current() : $speakerRoles->bottom();
            }
        }
        return Call::CALL_SPEAKER_ROLE_UNCLEAR;
    }
}
