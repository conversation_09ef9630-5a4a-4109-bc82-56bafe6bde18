<?php

declare(strict_types=1);

namespace STCall\Entity;

class AlgoEvent
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var string
     */
    protected string $callId;

    /**
     *
     * @var \Carbon\Carbon
     */
    protected \Carbon\Carbon $callTime;

    /**
     *
     * @var int
     */
    protected int $paragraphNumber;

    /**
     *
     * @var int
     */
    protected int $algoApiId;

    /**
     *
     * @var int
     */
    protected int $industryId;

    /**
     *
     * @var string
     */
    protected string $event;

    /**
     *
     * @var float
     */
    protected float $score;

    /**
     *
     * @var array[int]
     */
    protected array $mainPointLocation;

    /**
     *
     * @var string
     */
    protected string $mainPointPhrase;

    /**
     *
     * @var string
     */
    protected string $enMainPointPhrase = '';

    /**
     *
     * @var \Carbon\Carbon
     */
    protected ?\Carbon\Carbon $created = null;

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string
     */
    public function getCallId(): string
    {
        return $this->callId;
    }

    /**
     *
     * @return \Carbon\Carbon
     */
    public function getCallTime(): \Carbon\Carbon
    {
        return $this->callTime;
    }

    /**
     *
     * @return int
     */
    public function getParagraphNumber(): int
    {
        return $this->paragraphNumber;
    }

    /**
     *
     * @return int
     */
    public function getAlgoApiId(): int
    {
        return $this->algoApiId;
    }

    /**
     *
     * @return int
     */
    public function getIndustryId(): int
    {
        return $this->industryId;
    }

    /**
     *
     * @return string
     */
    public function getEvent(): string
    {
        return $this->event;
    }

    /**
     *
     * @return float
     */
    public function getScore(): float
    {
        return $this->score;
    }

    /**
     *
     * @return array
     */
    public function getMainPointLocation(): array
    {
        return $this->mainPointLocation;
    }

    /**
     *
     * @return string
     */
    public function getMainPointPhrase(): string
    {
        return $this->mainPointPhrase;
    }

    /**
     *
     * @return string
     */
    public function getEnMainPointPhrase(): string
    {
        return $this->enMainPointPhrase;
    }

    /**
     *
     * @return \Carbon\Carbon|null
     */
    public function getCreated(): ?\Carbon\Carbon
    {
        if (is_null($this->created)) {
            $this->created = \Carbon\Carbon::now();
        }
        return $this->created;
    }

    /**
     *
     * @param int $companyId
     * @return AlgoEvent
     */
    public function setCompanyId(int $companyId): AlgoEvent
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param string $callId
     * @return AlgoEvent
     */
    public function setCallId(string $callId): AlgoEvent
    {
        $this->callId = $callId;
        return $this;
    }

    /**
     *
     * @param string|\Carbon\Carbon $callTime
     * @return AlgoEvent
     */
    public function setCallTime(string|\Carbon\Carbon $callTime): AlgoEvent
    {
        $this->callTime = is_string($callTime) ? \Carbon\Carbon::parse($callTime) : $callTime;
        return $this;
    }

    /**
     *
     * @param int $paragraphNumber
     * @return AlgoEvent
     */
    public function setParagraphNumber(int $paragraphNumber)
    {
        $this->paragraphNumber = $paragraphNumber;
        return $this;
    }

    /**
     *
     * @param int $algoApiId
     * @return AlgoEvent
     */
    public function setAlgoApiId(int $algoApiId): AlgoEvent
    {
        $this->algoApiId = $algoApiId;
        return $this;
    }

    /**
     *
     * @param string $industryId
     * @return AlgoEvent
     */
    public function setIndustryId(int $industryId): AlgoEvent
    {
        $this->industryId = $industryId;
        return $this;
    }

    /**
     *
     * @param string $event
     * @return AlgoEvent
     */
    public function setEvent(string $event): AlgoEvent
    {
        $this->event = $event;
        return $this;
    }

    /**
     *
     * @param float $score
     * @return AlgoEvent
     */
    public function setScore(float $score): AlgoEvent
    {
        $this->score = $score;
        return $this;
    }

    /**
     *
     * @param array $mainPointLocation
     * @return AlgoEvent
     */
    public function setMainPointLocation(array $mainPointLocation): AlgoEvent
    {
        $this->mainPointLocation = $mainPointLocation;
        return $this;
    }

    /**
     *
     * @param string $mainPointPhrase
     * @return AlgoEvent
     */
    public function setMainPointPhrase(string $mainPointPhrase): AlgoEvent
    {
        $this->mainPointPhrase = $mainPointPhrase;
        return $this;
    }

    /**
     *
     * @param string $enMainPointPhrase
     * @return AlgoEvent
     */
    public function setEnMainPointPhrase(string $enMainPointPhrase): AlgoEvent
    {
        $this->enMainPointPhrase = $enMainPointPhrase;
        return $this;
    }

    /**
     *
     * @param string|\Carbon\Carbon $created
     * @return AlgoEvent
     */
    public function setCreated(string|\Carbon\Carbon $created): AlgoEvent
    {
        $this->created = is_string($created) ? \Carbon\Carbon::parse($created) : $created;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
