<?php

declare(strict_types=1);

namespace STCall\Entity;

class CallSummarization
{
    private string $callId;
    private int $companyId;
    private string $keyPoints;
    private string $customerSentiment;
    private string $nextSteps;
    private string $primaryPurpose;
    private string $mainTopics;
    private string $customerProblems;
    private string $keyActionItems;
    private string $businessOpportunities;
    private string $risks = '';
    private string $conversationType = '';
    private string $overview;
    private string $details;

    public function setCallId(string $callId): void
    {
        $this->callId = $callId;
    }

    public function getCallId(): string
    {
        return $this->callId;
    }

    public function setCompanyId(int $companyId): void
    {
        $this->companyId = $companyId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setKeyPoints(string $keyPoints): void
    {
        $this->keyPoints = $keyPoints;
    }

    public function getKeyPoints(): string
    {
        return $this->keyPoints;
    }

    public function setCustomerSentiment(string $customerSentiment): void
    {
        $this->customerSentiment = $customerSentiment;
    }

    public function getCustomerSentiment(): string
    {
        return $this->customerSentiment;
    }

    public function setNextSteps(string $nextSteps): void
    {
        $this->nextSteps = $nextSteps;
    }

    public function getNextSteps(): string
    {
        return $this->nextSteps;
    }

    public function setPrimaryPurpose(string $primaryPurpose): void
    {
        $this->primaryPurpose = $primaryPurpose;
    }

    public function getPrimaryPurpose(): string
    {
        return $this->primaryPurpose;
    }

    public function setMainTopics(string $mainTopics): void
    {
        $this->mainTopics = $mainTopics;
    }

    public function getMainTopics(): string
    {
        return $this->mainTopics;
    }

    public function setCustomerProblems(string $customerProblems): void
    {
        $this->customerProblems = $customerProblems;
    }

    public function getCustomerProblems(): string
    {
        return $this->customerProblems;
    }

    public function setKeyActionItems(string $keyActionItems): void
    {
        $this->keyActionItems = $keyActionItems;
    }

    public function getKeyActionItems(): string
    {
        return $this->keyActionItems;
    }

    public function setBusinessOpportunities(string $businessOpportunities): void
    {
        $this->businessOpportunities = $businessOpportunities;
    }

    public function getBusinessOpportunities(): string
    {
        return $this->businessOpportunities;
    }

    public function setRisks(string $risks): void
    {
        $this->risks = $risks;
    }

    public function getRisks(): string
    {
        return $this->risks;
    }

    public function setConversationType(string $conversationType): void
    {
        $this->conversationType = $conversationType;
    }

    public function getConversationType(): string
    {
        return $this->conversationType;
    }

    public function setOverview(string $overview): void
    {
        $this->overview = $overview;
    }

    public function getOverview(): string
    {
        return $this->overview;
    }

    public function setDetails(string $details): void
    {
        $this->details = $details;
    }

    public function getDetails(): string
    {
        return $this->details;
    }

    public function toArray(): array
    {
        return [
            'call_id' => $this->getCallId(),
            'company_id' => $this->getCompanyId(),
            'key_points' => $this->getKeyPoints(),
            'customer_sentiment' => $this->getCustomerSentiment(),
            'next_steps' => $this->getNextSteps(),
            'primary_purpose' => $this->getPrimaryPurpose(),
            'main_topics' => $this->getMainTopics(),
            'customer_problems' => $this->getCustomerProblems(),
            'key_action_items' => $this->getKeyActionItems(),
            'business_opportunities' => $this->getBusinessOpportunities(),
            'overview' => $this->getOverview(),
            'details' => $this->getDetails(),
            'risks' => $this->getRisks(),
            'conversation_type' => $this->getConversationType()
        ];
    }
}
