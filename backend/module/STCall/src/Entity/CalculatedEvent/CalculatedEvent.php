<?php

declare(strict_types=1);

namespace STCall\Entity\CalculatedEvent;

class CalculatedEvent
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected int $count = 0;

    /**
     *
     * @var \STCompany\Entity\Event\Event
     */
    protected \STCompany\Entity\Event\Event $event;

    /**
     *
     * Class constructor
     *
     * @param \STCompany\Entity\Event\Event $event
     */
    public function __construct(\STCompany\Entity\Event\Event $event)
    {
        $this->event = $event;
    }

    /**
     *
     * @param \STCompany\Entity\Event\Event $event
     * @return CalculatedEvent
     */
    public function setEvent(\STCompany\Entity\Event\Event $event): CalculatedEvent
    {
        $this->event = $event;
        return $this;
    }

    /**
     *
     * @param int $event
     * @return CalculatedEvent
     */
    public function setCount(int $count): CalculatedEvent
    {
        $this->count = $count;
        return $this;
    }


    /**
     *
     * @return \STCompany\Entity\Event\Event
     */
    public function getEvent(): \STCompany\Entity\Event\Event
    {
        return $this->event;
    }


    /**
     *
     * @return int
     */
    public function getCount(): int
    {
        return $this->count;
    }

    /**
     *
     * @return CalculatedEvent
     */
    public function increment(): CalculatedEvent
    {
        $this->count++;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['event'] = $this->getEvent()->toArray();
        return $result;
    }
}
