<?php

declare(strict_types=1);

namespace STCall\Data;

use STLib\Db\AbstractTable;

class AbstractDriversLanguagesTable extends AbstractTable
{
    /**
     *
     * @param string $language
     * @return string|null
     */
    public function getDriverNameByLanguage(string $language): ?string
    {
        $result = $this->tableGateway->select(['language' => $language]);

        if ($result->count() === 0) {
            return null;
        }

        return $result->toArray()[0]['driver'];
    }

    /**
     *
     * @return array
     */
    public function getDriversWithLanguages(): array
    {
        return $this->tableGateway->select()->toArray();
    }

    /**
     *
     * @param string $language
     * @param string $driver
     * @return bool
     */
    public function saveLanguageDriver(string $language, string $driver): bool
    {
        return $this->insertOrUpdate([
            'language' => $language,
            'driver' => $driver
        ], [
            'language' => $language,
            'driver' => $driver,
        ]);
    }
}
