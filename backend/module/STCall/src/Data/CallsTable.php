<?php

declare(strict_types=1);

namespace STCall\Data;

use Carbon\Carbon;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\TooManyRequestsException;
use ST<PERSON>lickhouse\Entity\Pagination\Pagination;

class CallsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STClickhouse\Data\CallsWhereCondition;
    use \STApi\Service\RequestsLimitTrait;

    public const DAY_CALENDAR_DATE_DIMENSIONS = 'day';
    public const WEEK_CALENDAR_DATE_DIMENSIONS = 'week';
    public const MONTH_CALENDAR_DATE_DIMENSIONS = 'month';

    public const CLIENT_STATUSES = [
        'lead',
        'converted',
    ];

    public const CALL_STATUS_END = 'end';
    public const CALL_STATUS_ANSWERED = 'answered';
    public const CALL_STATUS_RINGING = 'ringing';
    public const CALL_STATUS_HANG_UP = 'hang up';

    public const CALL_ORIGIN_API = 'api';
    public const CALL_ORIGIN_MANUAL = 'manual';
    public const CALL_ORIGIN_S3 = 's3';

    public const LANGUAGES = [
        'af' => 'Afrikaans',
        'am' => 'Amharic',
        'ar' => 'Arabic',
        'ar-AE' => 'Arabic (Gulf)',
        'ar-SA' => 'Arabic (Modern Standard)',
        'as' => 'Assamese',
        'az' => 'Azerbaijani',
        'ba' => 'Bashkir',
        'be' => 'Belarusian',
        'bg' => 'Bulgarian',
        'bn' => 'Bengali',
        'bo' => 'Tibetan',
        'br' => 'Breton',
        'bs' => 'Bosnian',
        'ca' => 'Catalan',
        'cs' => 'Czech',
        'cy' => 'Welsh',
        'da' => 'Danish',
        'da-DK' => 'Danish (Denmark)',
        'de' => 'German',
        'de-CH' => 'German (Switzerland)',
        'el' => 'Greek',
        'en' => 'English',
        'en-AB' => 'English (Scotland)',
        'en-AU' => 'English (Australia)',
        'en-GB' => 'English (United Kingdom)',
        'en-IE' => 'English (Ireland)',
        'en-IN' => 'English (India)',
        'en-NZ' => 'English (New Zealand)',
        'en-US' => 'English (United States)',
        'es' => 'Spanish',
        'es-419' => 'Spanish (Latin America)',
        'es-US' => 'Spanish (United States)',
        'et' => 'Estonian',
        'eu' => 'Basque',
        'fa' => 'Persian',
        'fi' => 'Finnish',
        'fo' => 'Faroese',
        'fr' => 'French',
        'fr-CA' => 'French (Canada)',
        'gl' => 'Galician',
        'gu' => 'Gujarati',
        'ha' => 'Hausa',
        'haw' => 'Hawaiian',
        'he' => 'Hebrew',
        'hi' => 'Hindi',
        'hi-Latn' => 'Hindi (Roman Script)',
        'hr' => 'Croatian',
        'ht' => 'Haitian creole',
        'hu' => 'Hungarian',
        'hy' => 'Armenian',
        'id' => 'Indonesian',
        'is' => 'Icelandic',
        'it' => 'Italian',
        'ja' => 'Japanese',
        'jw' => 'Javanese',
        'ka' => 'Georgian',
        'kk' => 'Kazakh',
        'km' => 'Khmer',
        'kn' => 'Kannada',
        'ko' => 'Korean',
        'ko-KR' => 'Korean (south)',
        'la' => 'Latin',
        'lb' => 'Luxembourgish',
        'ln' => 'Lingala',
        'lo' => 'Lao',
        'lt' => 'Lithuanian',
        'lv' => 'Latvian',
        'mg' => 'Malagasy',
        'mi' => 'Maori',
        'mk' => 'Macedonian',
        'ml' => 'Malayalam',
        'mn' => 'Mongolian',
        'mr' => 'Marathi',
        'ms' => 'Malay',
        'mt' => 'Maltese',
        'my' => 'Myanmar',
        'ne' => 'Nepali',
        'nl' => 'Dutch',
        'nl-BE' => 'Dutch (Belgium)',
        'nn' => 'Nynorsk',
        'no' => 'Norwegian',
        'oc' => 'Occitan',
        'pa' => 'Punjabi',
        'pl' => 'Polish',
        'ps' => 'Pashto',
        'pt' => 'Portuguese',
        'pt-BR' => 'Portuguese (Brazil)',
        'pt-PT' => 'Portuguese (Portugal)',
        'ro' => 'Romanian',
        'ru' => 'Russian',
        'sa' => 'Sanskrit',
        'sd' => 'Sindhi',
        'si' => 'Sinhala',
        'sk' => 'Slovak',
        'sl' => 'Slovenian',
        'sn' => 'Shona',
        'so' => 'Somali',
        'sq' => 'Albanian',
        'sr' => 'Serbian',
        'su' => 'Sundanese',
        'sv' => 'Swedish',
        'sv-SE' => 'Swedish (Sweden)',
        'sw' => 'Swahili',
        'ta' => 'Tamil',
        'te' => 'Telugu',
        'tg' => 'Tajik',
        'th' => 'Thai',
        'th-TH' => 'Thai (Thailand)',
        'tk' => 'Turkmen',
        'tl' => 'Tagalog',
        'tr' => 'Turkish',
        'tt' => 'Tatar',
        'uk' => 'Ukrainian',
        'ur' => 'Urdu',
        'uz' => 'Uzbek',
        'vi' => 'Vietnamese',
        'yi' => 'Yiddish',
        'yo' => 'Yoruba',
        'zh' => 'Chinese',
        'zh-CN' => 'Chinese (China)',
        'zh-TW' => 'Chinese (Taiwan)',
    ];

    /**
     *
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param bool $isAnalyzedOnly
     * @return array
     */
    public function getCallIds(
        int $companyId,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
        bool $isAnalyzedOnly = true,
    ): array {
        $sql = '
            SELECT DISTINCT
                call_id
            FROM
                (
                ' . $this->getFinalTableSql('calls', [
                'call_id',
                'company_id',
            ], 'created_at') . '
                )
            WHERE
                company_id = \'' . $companyId . '\'
                AND is_deleted = 0
        ';
        if ($isAnalyzedOnly) {
            $sql .= '
                AND is_analyzed = 1
            ';
        }
        if ($startDate instanceof Carbon) {
            $sql .= '
                AND call_time >= toDateTime(\'' . $startDate . '\')
            ';
        }
        if ($endDate instanceof Carbon) {
            $sql .= '
                AND call_time <= toDateTime(\'' . $endDate . '\')
            ';
        }
        return $this->getClient()->selectColumn($sql, 'call_id');
    }

    /**
     *
     * @param int $companyId
     * @param ?Carbon $startDate
     * @param ?Carbon $endDate
     * @return array
     */
    public function getS3Files(
        int $companyId,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $sql = '
            SELECT DISTINCT
                s3_file_path
            FROM
                calls
            WHERE
                company_id = \'' . $companyId . '\'
                AND s3_file_path IS NOT NULL
        ';

        if ($startDate instanceof Carbon) {
            $sql .= '
                AND call_time >= toDateTime(\'' . $startDate . '\')
            ';
        }

        if ($endDate instanceof Carbon) {
            $sql .= '
                AND call_time <= toDateTime(\'' . $endDate . '\')
            ';
        }

        return $this->getClient()->selectColumn($sql, 's3_file_path');
    }

    /**
     *
     * @param int $companyId
     * @param string $search
     * @return array
     */
    public function getCallIdsBySearchString(int $companyId, string $search): array
    {
        $sql = '
            SELECT 
                DISTINCT call_id
            FROM
                calls
            WHERE
                company_id = \'' . $companyId . '\'
                AND call_id LIKE \'%' . $search . '%\'
            ORDER BY position(call_id, \'' . $search . '\')
            LIMIT 20
        ';

        return $this->getClient()->selectColumn($sql, 'call_id');
    }

    /**
     * @param int $companyId
     * @param int $roleId
     * @param Pagination $pagination
     * @return Pagination
     * @throws TooManyRequestsException
     */
    public function getCallsWithPagination(
        int $companyId,
        int $roleId,
        Pagination $pagination,
    ): Pagination {
        $this->checkRequestLimit('get-calls-pagination', requestLimit: 3, perSecond: 10);

        $pagination->setColumnMapper([
            'call_time' => 'c.call_time',
            'time' => 'c.call_time',
            'call_type' => 'c.call_type',
            'language' => 'call_language',
            'call_status' => 'call_status',
            'status' => 'call_status',
            'call_origin' => 'c.call_origin',
            'origin' => 'c.call_origin',
            'is_reviewed' => 'pc.is_reviewed',
            'agent_id' => 'c.agent_id',
            'client_id' => 'c.client_id',
        ]);

        $countSql = '
            SELECT
                COUNT(*) value
            FROM
            (
                ' .
            $this->getFinalTableSqlUsingGroupBy('calls', [
                'company_id',
                'call_id',
            ], 'created', [
                'call_time',
                'call_type',
                'call_language',
                'call_status',
                'call_duration',
                'call_origin',
                'original_file_name',
                'agent_id',
                'client_id',
                's3_file_path',
                'uploaded_user_id',
                'uploaded_time',
                'transcribing_driver',
                'translation_driver',
                'is_transcribed',
                'is_translated',
                'is_speakers_roles_detected',
                'is_analyzed',
                'is_run_manually',
                'is_sent_to_transcribing',
                'is_deleted',
                'created',
            ], $this->getCallsWhereConditionFromPagination($companyId, $pagination)) . '
            ) c
            LEFT JOIN
            (
                ' .
            // phpcs:disable
            $this->getFinalTableSqlUsingGroupBy
            (
                'precalculated_calls',
                [
                    'company_id',
                    'call_id',
                    'role_id',
                ],
                'created',
                [
                    'is_reviewed',
                    'score',
                    'risk_rank',
                    'comments',
                    'fragments',
                    'reviewed_time',
                ],
                $this->getCallsWhereConditionFromPagination($companyId, $pagination, $roleId)
            )
            // phpcs:enable
            . '
            ) pc
            ON pc.call_id = c.call_id
            AND pc.role_id = ' . $roleId . '
        ';

        $pagination->setGroupBy([
            'c.call_id',
            'c.company_id',
            'is_transcribed',
            'is_translated',
            'is_speakers_roles_detected',
            'is_analyzed',
            'c.call_time',
            'c.call_type',
        ]);

        $sql = '
            SELECT
                c.company_id company_id,
                c.call_id id,
                c.call_id call_id,
                any(c.call_time) time,
                any(c.call_time) call_time,
                any(c.call_type) call_type,
                any(c.call_language) call_language,
                any(c.call_status) call_status,
                any(c.call_duration) call_duration,
                any(c.paragraphs_count) paragraphs_count,
                any(c.call_origin) call_origin,
                any(c.original_file_name) original_file_name,
                any(c.agent_id) agent_id,
                any(c.client_id) client_id,
                any(c.s3_file_path) s3_file_path,
                any(c.uploaded_user_id) uploaded_user_id,
                any(c.uploaded_time) uploaded_time,
                any(c.transcribing_driver) transcribing_driver,
                any(c.translation_driver) translation_driver,
                c.is_transcribed is_transcribed,
                c.is_translated is_translated,
                c.is_speakers_roles_detected is_speakers_roles_detected,
                c.is_analyzed is_analyzed,
                any(c.is_run_manually) is_run_manually,
                any(c.is_sent_to_transcribing) is_sent_to_transcribing,
                any(c.created) created,
                any(cl.client_name) client_name,
                any(cl.status) client_status,
                any(u.user_name) uploaded_user_name,
                any(ua.user_name) agent_name,
                any(pc.is_reviewed) is_reviewed,
                any(pc.reviewed_time) reviewed_time,
                any(pc.score) score,
                arrayFilter(fragment -> fragment[\'end_time\'] <> \'0\', any(pc.fragments)) fragments,
                arrayFilter(event -> event[\'id\'] <> \'0\',
                    groupArray(
                        map
                        (
                            \'id\', toString(pce.event_id),
                            \'paragraph_number\', toString(pce.paragraph),
                            \'name\', pce.event_name,
                            \'icon\', event_icon,
                            \'fill_color_hex\', event_fill_color_hex,
                            \'outline_color_hex\', event_outline_color_hex,
                            \'value\', toString(pce.value)
                        )
                    )
                ) events,
                arrayMap(
                    comment -> (
                        map
                        (
                            \'id\', comment[\'comment_id\'],
                            \'user_id\', comment[\'user_id\'],
                            \'user_name\', comment[\'user_name\'],
                            \'date\', toString(formatDateTime(CAST(comment[\'date\'] AS DATETIME), \'%F %T\')),
                            \'text\', comment[\'text\']
                        )
                    ), 
                    any(pc.comments)
                ) comments
            FROM
            (
                ' .
            $this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'company_id',
                    'call_id',
                ],
                'created',
                [
                    'call_time',
                    'call_type',
                    'call_language',
                    'call_status',
                    'call_duration',
                    'paragraphs_count',
                    'call_origin',
                    'original_file_name',
                    'agent_id',
                    'client_id',
                    's3_file_path',
                    'uploaded_user_id',
                    'uploaded_time',
                    'transcribing_driver',
                    'translation_driver',
                    'is_transcribed',
                    'is_translated',
                    'is_speakers_roles_detected',
                    'is_analyzed',
                    'is_run_manually',
                    'is_sent_to_transcribing',
                    'is_deleted',
                    'created',
                ],
                $this->getCallsWhereConditionFromPagination($companyId, $pagination)
            )
            . '
            ) c
            LEFT JOIN
                dictionary(users) u
                ON u.user_id = c.uploaded_user_id
            LEFT JOIN
                dictionary(users) ua
                ON ua.user_id = c.agent_id
            LEFT JOIN
                (
                    ' .
            // phpcs:disable
            $this->getFinalTableSqlUsingGroupBy
            (
                'precalculated_calls',
                [
                    'company_id',
                    'call_id',
                    'role_id',
                ],
                'created',
                [
                    'is_reviewed',
                    'score',
                    'risk_rank',
                    'comments',
                    'fragments',
                    'reviewed_time',
                ],
                $this->getCallsWhereConditionFromPagination($companyId, $pagination, $roleId)
            )
            // phpcs:enable
            . '
                ) pc
                ON pc.call_id = c.call_id
            LEFT JOIN
                (
                    ' . $this->getFinalTableSqlUsingGroupBy('clients', [
                'company_id',
                'client_id',
            ], 'created', [
                'client_name',
                'status',
            ], [
                'company_id' => $companyId,
            ]) . '
                ) cl
                ON cl.client_id = c.client_id
            LEFT JOIN
                (
                    SELECT 
                        company_id,
                        call_id,
                        event_id,
                        any(paragraph) paragraph,
                        any(event_name) event_name,
                        any(event_icon) event_icon,
                        any(event_fill_color_hex) event_fill_color_hex,
                        any(event_outline_color_hex) event_outline_color_hex,
                        COUNT(*) value
                    FROM 
                        (
                        
                            ' .
            // phpcs:disable
            $this->getFinalTableSqlUsingGroupBy
            (
                'precalculated_calls_events',
                [
                    'company_id',
                    'role_id',
                    'call_id',
                    'paragraph',
                    'event_id',
                ],
                'created',
                [
                    'event_is_pinned',
                    'event_is_deleted',
                    'event_name',
                    'event_icon',
                    'event_fill_color_hex',
                    'event_outline_color_hex',
                ],
                $this->getCallsWhereConditionFromPagination($companyId, $pagination, $roleId)
            )
            // phpcs:enable
            . '
                        )
                    WHERE
                        company_id = ' . $companyId . '
                        AND role_id = ' . $roleId . '
                        AND event_is_pinned = 1
                        AND event_is_deleted = 0
                    GROUP BY
                        company_id,
                        call_id,
                        event_id
                ) pce
                ON c.company_id = pce.company_id
                AND c.call_id = pce.call_id
        ';

        $pagination
            ->addFilter('c.company_id', $companyId)
            ->addFilter('c.is_deleted', 0);
        return $this->selectWithPagination($sql, $pagination, $countSql);
    }

    /**
     *
     * @param string|array $callId
     * @param int|null $companyId
     * @param int|null $roleId
     * @param bool $allowRemoved
     * @return array
     * @throws NotFoundApiException
     */
    public function getCall(string|array $callId, int $companyId = null, int $roleId = null, bool $allowRemoved = false): array
    {
        $callIds = is_array($callId) ? $callId : [$callId];
        $sql = '
            SELECT
        ';
        if (is_int($roleId)) {
            $sql .= '
                cr.is_reviewed is_reviewed,
                cr.reviewed_time reviewed_time,
                cr.reviewer_user_id reviewer_user_id,
                ur.user_name reviewer_user_name,
            ';
        }
        $sql .= '
                c.company_id company_id,
                c.call_id call_id,
                c.call_time call_time,
                c.transcribed_at transcribed_at,
                c.call_type call_type,
                c.call_language call_language,
                c.call_status call_status,
                c.call_duration call_duration,
                c.paragraphs_count paragraphs_count,
                c.call_origin call_origin,
                c.original_file_name original_file_name,
                c.agent_id agent_id,
                c.client_id client_id,
                cl.client_name client_name,
                cl.status client_status,
                cl.value client_value,
                cl.source client_source,
                c.s3_file_path s3_file_path,
                c.file_hash file_hash,
                c.uploaded_user_id uploaded_user_id,
                c.uploaded_time uploaded_time,
                c.transcribing_driver transcribing_driver,
                c.translation_driver translation_driver,
                c.is_transcribed is_transcribed,
                c.is_translated is_translated,
                c.is_speakers_roles_detected is_speakers_roles_detected,
                c.is_checklist_completed is_checklist_completed,
                c.is_summarization_completed is_summarization_completed,
                c.is_llm_events_detected is_llm_events_detected,
                c.analyzed_at analyzed_at,
                c.is_analyzed is_analyzed,
                c.is_deleted is_deleted,
                c.is_run_manually is_run_manually,
                c.is_sent_to_transcribing is_sent_to_transcribing,
                c.created created,
                uu.user_name uploaded_user_name,
                ua.user_name agent_name,
                com.max_score max_score,
                com.min_score min_score,
                com.base_score base_score
            FROM
            (
                ' .
            $this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'call_id',
                    'company_id',
                ],
                'created_at',
                [
                    'call_time',
                    'call_type',
                    'call_language',
                    'call_status',
                    'call_duration',
                    'paragraphs_count',
                    'call_origin',
                    'original_file_name',
                    'agent_id',
                    'client_id',
                    's3_file_path',
                    'file_hash',
                    'uploaded_user_id',
                    'uploaded_time',
                    'transcribing_driver',
                    'translation_driver',
                    'is_transcribed',
                    'is_translated',
                    'is_speakers_roles_detected',
                    'is_checklist_completed',
                    'is_summarization_completed',
                    'is_llm_events_detected',
                    'analyzed_at',
                    'is_analyzed',
                    'is_deleted',
                    'is_run_manually',
                    'is_sent_to_transcribing',
                    'created',
                    'transcribed_at',
                ],
                [
                    'call_id' => $callId,
                    'company_id' => $companyId,
                ]
            ) . '
            ) c
            LEFT JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(users)
                ) uu
                ON uu.user_id = c.uploaded_user_id
            LEFT JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(users)
                ) ua
                ON ua.user_id = c.agent_id
            LEFT JOIN
                dictionary(companies) com
                ON c.company_id = com.company_id
            LEFT JOIN
                (
                ' .
            $this->getFinalTableSqlUsingGroupBy(
                'clients',
                [
                    'client_id',
                    'company_id',
                ],
                'created',
                [
                    'client_name',
                    'status',
                    'value',
                    'source',
                ],
                [
                    'company_id' => $companyId,
                    [
                        'type' => 'expression',
                        'value' => '
                                    client_id IN
                                    (
                                        SELECT DISTINCT
                                            client_id
                                        FROM
                                            calls
                                        WHERE 
                                            company_id = ' . $companyId . '
                                            AND call_id IN (\'' . implode('\',\'', $callIds) . '\')
                                    )
                                ',
                    ],
                ]
            ) . '
                ) cl
                ON cl.client_id = c.client_id
                AND cl.company_id = c.company_id
        ';
        if (is_int($roleId)) {
            $sql .= '
            LEFT JOIN
                (
                ' .
                $this->getFinalTableSqlUsingGroupBy(
                    'calls_reviews',
                    [
                        'call_id',
                        'company_id',
                        'role_id',
                    ],
                    'reviewed_time',
                    [
                        'is_reviewed',
                        'reviewed_time',
                        'reviewer_user_id',
                    ],
                    [
                        'call_id' => $callId,
                        'company_id' => $companyId,
                        'role_id' => $roleId,
                    ]
                ) . '
                ) cr
                ON cr.call_id = c.call_id
                AND cr.company_id = c.company_id
            LEFT JOIN
                dictionary(users) ur
                ON ur.user_id = cr.reviewer_user_id
            ';
        }
        $sql .= '
            WHERE
                call_id IN (\'' . implode('\',\'', $callIds) . '\')
        ';

        if (!$allowRemoved) {
            $sql .= ' AND is_deleted = 0';
        }

        if (is_int($companyId)) {
            $sql .= '
                AND company_id = ' . $companyId . '
            ';
        }
        if (is_string($callId)) {
            $result = $this->getClient()->selectOne($sql);
            if (is_null($result)) {
                throw new NotFoundApiException('Call not found');
            }
        } elseif (is_array($callId)) {
            $result = $this->getClient()->selectAll($sql);
        }
        return $result;
    }

    public function isCallExists(string $callId, int $companyId): bool
    {
        $groupSql = $this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                    'call_id',
                    'company_id',
                ],
            'created_at',
            ['is_deleted'],
            [
                    'call_id' => $callId,
                    'company_id' => $companyId,
                ]
        );

        $sql = 'SELECT * FROM (' . $groupSql . ') WHERE is_deleted = 0 LIMIT 1';

        return !is_null($this->getClient()->selectOne($sql));
    }

    /**
     *
     * @param int $companyId
     * @param int $uploadedLaterThanInHours
     * @param int $uploadedBeforeThanInHours
     * @return array
     */
    public function getUnprocessedCall(
        int $companyId,
        int $uploadedLaterThanInHours,
        int $uploadedBeforeThanInHours
    ): array {
        $sql = '
            SELECT
                *
            FROM
                (
                    ' . $this->getFinalTableSql('calls', ['call_id', 'company_id',], 'created_at') . '
                )
            WHERE
                company_id = ' . $companyId . '
                AND is_sent_to_transcribing = true
                AND is_analyzed = false
                AND is_deleted = false
                AND created > NOW() - INTERVAL ' . $uploadedLaterThanInHours . ' HOUR
                AND created < NOW() - INTERVAL ' . $uploadedBeforeThanInHours . ' HOUR
        ';
        return $this->getClient()->selectAll($sql);
    }


    /**
     *
     * @param int $companyId
     * @param string $callId
     * @param string $param
     * @return mixed
     */
    public function getCallParam(int $companyId, string $callId, string $param): mixed
    {
        $sql = '
            SELECT 
                last_value_respect_nulls(' . $param . ')
            FROM
            (
                SELECT
                    *
                FROM
                    calls
                WHERE
                    call_id = \'' . $callId . '\'
                    AND company_id = ' . $companyId . '
                ORDER BY
                    created
            )
        ';
        return $this->getClient()->selectValue($sql);
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return array
     */
    public function getAgentsTodayCalls(
        \STCompany\Entity\Company $company,
    ): array {
        $sql = '
            SELECT
            DISTINCT
                c1.agent_id user_id,
                c2.calls_count calls_count
            FROM
            (
                ' . $this->getFinalTableSql('calls', [
                'call_id',
                'company_id',
            ], 'created_at') . '
            ) c1
            LEFT JOIN
                (
                SELECT
                    agent_id,
                    COUNT(call_id) calls_count
                FROM
                    (
                        ' . $this->getFinalTableSql('calls', [
                'call_id',
                'company_id',
            ], 'created_at') . '
                    )
                WHERE
                    company_id = ' . $company->getId() . '
                    AND call_time > yesterday()
                GROUP BY
                    agent_id
                ) c2
                ON c2.agent_id = c1.agent_id
            WHERE
                c1.company_id = ' . $company->getId() . '
                AND c1.is_deleted = 0
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param int $companyId
     * @param string $hash
     * @param int $duration
     * @return string|null
     */
    public function getCallIdByFileHashAndDuration(int $companyId, string $hash, int $duration): ?string
    {
        $sql = '
            SELECT DISTINCT 
                call_id
            FROM
                (
                    ' .
            $this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'call_id',
                    'company_id',
                ],
                'created_at',
                [
                    'call_time',
                    'file_hash',
                    'call_duration',
                    'is_deleted',
                ],
                [
                    'company_id' => $companyId,
                    'call_duration' => $duration,
                    'file_hash' => $hash,
                ]
            ) . '
                )
            WHERE
                is_deleted = 0
        ';
        return $this->getClient()->selectValue($sql);
    }

    /**
     *
     * @param int $companyId
     * @return int
     */
    public function getCountOfTranscribedCallsForLast24HoursByCompanyId(int $companyId): int
    {
        // phpcs:disable
        $sql = '
            SELECT
                count()
            FROM
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'call_id',
                ],
                'created',
                [],
                [
                    'company_id' => $companyId,
                    'is_transcribed' => true,
                    'created' => [
                        'type' => 'expression',
                        'value' => '
                                created >= NOW() - INTERVAL 1 DAY
                            ',
                    ]
                ]
            ) . '
            )
        ';
        // phpcs:enable

        return (int) $this->getClient()->selectValue($sql);
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getCallStatusesByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT
                DISTINCT(c.call_status) status
            FROM 
                (
                    ' . $this->getFinalTableSql('calls', ['call_id', 'company_id',], 'created_at') . '
                ) c
            WHERE
                c.company_id = ' . $companyId . '
        ';

        return array_values(
            array_filter(
                $this->getClient()->selectColumn($sql, 'status')
            )
        );
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getCallLanguagesByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT
                DISTINCT(c.call_language) language
            FROM 
                (
                    ' . $this->getFinalTableSql('calls', ['call_id', 'company_id',], 'created_at') . '
                ) c
            WHERE
                c.company_id = ' . $companyId . '
        ';

        return array_values(
            array_filter(
                $this->getClient()->selectColumn($sql, 'language')
            )
        );
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getCallsDateRange(int $companyId): array
    {
        $sql = '
            SELECT 
                min(call_time) first_time,
                max(call_time) last_time
            FROM
                calls
            WHERE 
                company_id = ' . $companyId . '
        ';

        return $this->getClient()->selectOne($sql);
    }

    /**
     *
     * @return array
     */
    public function getCompaniesWithDecreasingCallsAmount(): array
    {
        $sql = <<<SQL
            SELECT
                avg_calls_count,
                previous_day_calls_count,
                company_id,
                c.company_name
            FROM (
                SELECT 
                    avg(calls_avg.calls_count) avg_calls_count, 
                    last_value_respect_nulls(cpd.calls_count) previous_day_calls_count, 
                    company_id
                FROM (
                    SELECT
                        company_id,
                        uniq(call_id) calls_count
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                'company_id',
                'call_id',
            ],
            'created',
            [
                'call_duration',
                'call_time'
            ],
            [
                [
                    'column' => 'call_time',
                    'value' => (new Carbon())->subDays(3)->startOfDay(),
                    'type' => 'date',
                    'compare' => '>=',
                ],
                [
                    'column' => 'call_time',
                    'value' => (new Carbon())->subDay()->startOfDay(),
                    'type' => 'date',
                    'compare' => '<',
                ],
            ]
        )})
                    GROUP BY company_id 
                ) calls_avg
                LEFT JOIN (
                    SELECT
                        company_id,
                        uniq(call_id) calls_count
                    FROM ({$this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                'company_id',
                'call_id',
            ],
            'created',
            [
                'call_duration',
                'call_time'
            ],
            [
                [
                    'column' => 'call_time',
                    'value' => (new Carbon())->subDay()->startOfDay(),
                    'type' => 'date',
                    'compare' => '>=',
                ],
                [
                    'column' => 'call_time',
                    'value' => (new Carbon())->subDay()->endOfDay(),
                    'type' => 'date',
                    'compare' => '<=',
                ],
            ]
        )})
                    GROUP BY company_id
                ) cpd ON cpd.company_id = calls_avg.company_id
                GROUP BY 
                    company_id
                HAVING 
                    previous_day_calls_count < round(avg_calls_count / 100 * 30)
            ) stats
            INNER JOIN dictionary(companies) c
                ON c.company_id = stats.company_id
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param int $companyId
     * @param int|array $agentId
     * @return array
     */
    public function getClientIdsByAgentId(int $companyId, int|array $agentId): array
    {
        $agentIds = is_array($agentId) ? $agentId : [$agentId];
        $sql = '
            SELECT DISTINCT
                client_id
            FROM
                calls
            WHERE
                company_id = ' . $companyId . '
                AND agent_id IN (' . implode(',', $agentIds) . ')
        ';
        return $this->getClient()->selectColumn($sql, 'client_id');
    }

    public function getClientIdsByCompanyId(int $companyId): array
    {
        $sql = '
            SELECT DISTINCT
                client_id
            FROM
                calls
            WHERE
                company_id = ' . $companyId . '
        ';

        return $this->getClient()->selectColumn($sql, 'client_id');
    }

    /**
     *
     * @param int $companyId
     * @param string $clientId
     * @return int
     */
    public function getCallsCountByClientId(int $companyId, string $clientId): int
    {
        $sql = '
            SELECT
                uniq(call_id)
            FROM
                calls
            WHERE
                company_id = ' . $companyId . '
                AND client_id = \'' . $clientId . '\'
        ';
        return (int) $this->getClient()->selectValue($sql);
    }

    public function getClientCallIdsByDateRange(
        int $companyId,
        string $clientId,
        string $dateColumn = 'created_at',
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): array {
        $where = ' WHERE company_id = :companyId AND client_id = :clientId AND is_deleted = 0 AND is_analyzed = 1';
        $bindings = [
            'companyId' => $companyId,
            'clientId' => $clientId,
        ];

        if ($startDate !== null) {
            $where .= ' AND ' . $dateColumn . ' >= toDateTime(\'' . $startDate . '\')';
        }
        if ($endDate !== null) {
            $where .= ' AND ' . $dateColumn . ' <= toDateTime(\'' . $endDate . '\')';
        }

        $sql = <<<SQL
            SELECT DISTINCT
                call_id
            FROM
                calls
            {$where}
            ORDER BY
                call_time
        SQL;

        return $this->getClient()->selectColumn($sql, 'call_id', $bindings);
    }

    /**
     * @param int $companyId
     * @param int $limit
     * @return array
     */
    public function getCallsIdsWithoutSpeakersRolesDetected(int $companyId, int $limit): array
    {
        $sql = '
            SELECT 
                call_id
            FROM
                (
                    ' .
            $this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'call_id',
                    'company_id',
                ],
                'created_at',
                [
                    'is_deleted',
                    'is_transcribed',
                    'is_speakers_roles_detected',
                ],
                [
                    'company_id' => $companyId,
                    'call_type' => 'call',
                ]
            ) . '
                )
                WHERE
                    is_deleted = 0 
                    AND is_transcribed = 1 
                    AND is_speakers_roles_detected = 0
                LIMIT ' . $limit . '
        ';

        return $this->getClient()->selectColumn($sql, 'call_id');
    }

    /**
     *
     * @param int $companyId
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return void
     */
    public function deleteCalls(int $companyId, ?Carbon $startDate = null, ?Carbon $endDate = null): void
    {
        $this->getClient()->write(
            <<<SQL
                INSERT INTO calls
                (
                    company_id,
                    call_id,
                    call_time,
                    call_type,
                    call_language,
                    call_status,
                    call_duration,
                    paragraphs_count,
                    call_origin,
                    original_file_name,
                    agent_id,
                    client_id,
                    s3_file_path,
                    file_hash,
                    transcribing_driver,
                    translation_driver,
                    uploaded_user_id,
                    uploaded_time,
                    is_transcribed,
                    is_translated,
                    is_speakers_roles_detected,
                    is_checklist_completed,
                    is_summarization_completed,
                    is_llm_events_detected,
                    analyzed_at,
                    is_analyzed,
                    is_deleted,
                    is_run_manually,
                    is_sent_to_transcribing,
                    created,
                    transcribed_at,
                    created_at
                )
                SELECT
                    company_id,
                    call_id,
                    call_time,
                    call_type,
                    call_language,
                    call_status,
                    call_duration,
                    paragraphs_count,
                    call_origin,
                    original_file_name,
                    agent_id,
                    client_id,
                    s3_file_path,
                    file_hash,
                    transcribing_driver,
                    translation_driver,
                    uploaded_user_id,
                    uploaded_time,
                    is_transcribed,
                    is_translated,
                    is_speakers_roles_detected,
                    is_checklist_completed,
                    is_summarization_completed,
                    is_llm_events_detected,
                    analyzed_at,
                    is_analyzed,
                    true,
                    is_run_manually,
                    is_sent_to_transcribing,
                    NOW(),
                    transcribed_at,
                    NOW()
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'company_id',
                    'call_id',
                ],
                'created',
                [
                    'call_time',
                    'call_type',
                    'call_language',
                    'call_status',
                    'call_duration',
                    'paragraphs_count',
                    'call_origin',
                    'original_file_name',
                    'agent_id',
                    'client_id',
                    's3_file_path',
                    'file_hash',
                    'transcribing_driver',
                    'translation_driver',
                    'uploaded_user_id',
                    'uploaded_time',
                    'is_transcribed',
                    'is_translated',
                    'is_speakers_roles_detected',
                    'is_checklist_completed',
                    'is_summarization_completed',
                    'is_llm_events_detected',
                    'analyzed_at',
                    'is_analyzed',
                    'is_run_manually',
                    'is_deleted',
                    'is_sent_to_transcribing',
                    'transcribed_at',
                ],
                $this->getCallsWhereConditionsFromMainParams($companyId, $startDate, $endDate),
            )}) calls
            WHERE is_deleted = 0
            SQL
        );
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return int
     */
    public function saveCall(\STCall\Entity\Call $call): int
    {
        return $this->saveCalls([
            $call,
        ]);
    }

    /**
     *
     * @param \STCall\Entity\Call[] $calls
     * @return int
     * @todo drop unsets and change to param from array to \STCall\Entity\CallCollection
     *
     */
    public function saveCalls(array $calls): int
    {
        if (count($calls) === 0) {
            return 0;
        }
        foreach (array_chunk($calls, 1000) as $callsChunk) {
            $data = [];
            foreach ($callsChunk as $call) {
                $callData = $call->toArray();
                $callData['call_id'] = $callData['id'];
                $callData['call_time'] = $callData['time'];
                $callData['call_language'] = $callData['language'];
                $callData['call_duration'] = $callData['duration'];
                $callData['call_origin'] = $callData['origin'];
                $callData['is_transcribed'] = (int) $callData['is_transcribed'];
                $callData['is_translated'] = (int) $callData['is_translated'];
                $callData['is_speakers_roles_detected'] = (int) $callData['is_speakers_roles_detected'];
                $callData['is_analyzed'] = (int) $callData['is_analyzed'];
                $callData['is_deleted'] = (int) $callData['is_deleted'];
                $callData['is_run_manually'] = (int) $callData['is_run_manually'];
                $callData['is_sent_to_transcribing'] = (int) $callData['is_sent_to_transcribing'];
                $callData['created'] = Carbon::now();
                unset($callData['id']);
                unset($callData['time']);
                unset($callData['language']);
                unset($callData['duration']);
                unset($callData['paragraphs']);
                unset($callData['origin']);
                unset($callData['fragments']);
                unset($callData['uploaded_user_name']);
                unset($callData['client_name']);
                unset($callData['client_status']);
                unset($callData['client_source']);
                unset($callData['client_value']);
                unset($callData['client_converted_date']);
                unset($callData['client_last_transaction_date']);
                unset($callData['agent_name']);
                unset($callData['is_reviewed']);
                unset($callData['score']);
                unset($callData['risk_rank']);
                unset($callData['reviewed_event_happenings']);
                unset($callData['reviewers']);
                unset($callData['reviewed_time']);
                unset($callData['calculated_events']);
                unset($callData['reviewed_score']);
                unset($callData['min_score']);
                unset($callData['max_score']);
                unset($callData['base_score']);
                unset($callData['comments']);
                unset($callData['reviewer_user_id']);
                unset($callData['reviewed_event_ids']);
                unset($callData['reviewed_event_category_ids']);
                unset($callData['is_partly_reviewed']);
                unset($callData['questions_count']);
                unset($callData['topics']);
                $data[] = $callData;
            }
            $columns = array_keys(current($data));
            $this->getClient()->insert($this->getTableName(), $data, $columns);
        }
        return count($calls);
    }
}
