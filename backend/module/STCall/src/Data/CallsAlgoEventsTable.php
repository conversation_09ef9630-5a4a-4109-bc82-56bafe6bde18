<?php

declare(strict_types=1);

namespace STCall\Data;

use STClickhouse\Data\QueriesTrait;
use STClickhouse\Entity\BaseTable;

class CallsAlgoEventsTable extends BaseTable
{
    use QueriesTrait;

    public const string NEUTRAL_EVENT = 'neutral';

    /**
     *
     * @param string $callId
     * @param int $companyId
     * @return int
     */
    public function getCallAlgoEventsCount(string $callId, int $companyId): int
    {
        $sql = '
            SELECT
                count(*)
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy(
            'calls_algo_events',
            [
                'company_id',
                'call_id',
                'paragraph_number',
                'algo_api_id',
                'event',
            ],
            'created',
            [],
            [
                    'company_id' => $companyId,
                    'call_id' => $callId,
            ]
        ) . '
            )   
        ';

        return (int) $this->getClient()->selectValue($sql);
    }

    /**
     *
     * @param int $companyId
     * @param array|string $callIds
     * @param string $key
     * @return array
     */
    public function getEvents(int $companyId, array|string $callIds, string $key): array
    {
        $callIdsInArray = is_array($callIds) ? $callIds : [$callIds];

        $sql = '
            SELECT
                *,
                decrypt(\'aes-256-ofb\', main_point_phrase, \'' . $key . '\') main_point_phrase
            FROM 
            (
                ' . $this->getFinalTableSqlUsingGroupBy('calls_algo_events', [
                        'company_id',
                        'call_id',
                        'paragraph_number',
                        'algo_api_id',
                        'event',
                    ], 'created', [
                        'main_point_phrase',
                        'industry_id',
                        'call_time',
                        'score',
                        'main_point_location',
                        'main_point_phrase',
                        'en_main_point_phrase',
                        'created',
                    ], [
                        'company_id' => $companyId,
                        'call_id' => $callIdsInArray,
                    ]) . '
            )   
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param \STCall\Entity\AlgoEventCollection $algoEventCollection
     * @param string $key
     * @return int
     */
    public function saveEvents(\STCall\Entity\AlgoEventCollection $algoEventCollection, string $key): int
    {
        if ($algoEventCollection->count() === 0) {
            return 0;
        }
        foreach ($algoEventCollection->chunk(100) as $eventsChunk) {
            $data = [];
            foreach ($eventsChunk as $event) {
                $record = $event->toArray();
                $escapedMainPointPhrase = $this->getClient()->escapeChars($record['main_point_phrase']);
                $escapedEnMainPointPhrase = $this->getClient()->escapeChars($record['en_main_point_phrase']);
                $record['main_point_phrase'] = new \ClickHouseDB\Query\Expression\Raw("encrypt('aes-256-ofb', '" . $escapedMainPointPhrase . "', '" . $key . "')");
                $record['en_main_point_phrase'] = new \ClickHouseDB\Query\Expression\Raw("encrypt('aes-256-ofb', '" . $escapedEnMainPointPhrase . "', '" . $key . "')");
                $record['created'] = \Carbon\Carbon::now();
                $data[] = $record;
            }
            $columns = array_keys(current($data));
            $this->getClient()->insert($this->getTableName(), $data, $columns);
        }
        return $algoEventCollection->count();
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return void
     */
    public function deleteByCall(\STCall\Entity\Call $call): void
    {
        $this->getClient()->softDelete($this->getTableDataName(), [['call_id', '=', $call->getId()]]);
    }
}
