<?php

declare(strict_types=1);

namespace STCall\Data;

use Carbon\Carbon;
use ST<PERSON>all\Entity\Call;
use ST<PERSON><PERSON>house\Data\CallsWhereCondition;
use ST<PERSON><PERSON>house\Data\QueriesTrait;
use ST<PERSON>lickhouse\Entity\BaseTable;

class CallsChecklistsPointsTable extends BaseTable
{
    use QueriesTrait;
    use CallsWhereCondition;

    public const STATUS_PASSED = 'passed';
    public const STATUS_NOT_PASSED = 'not_passed';
    public const STATUS_NOT_ENCOUNTERED = 'not_encountered';

    public function getAgentsTotalStatistics(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?int $agentId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql($teamIds, $startDate, $endDate);
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($agentId !== null) {
            $where .= ' AND agent_id = :agentId';
            $bindings['agentId'] = $agentId;
        }

        $sql = <<<SQL
            SELECT
                last_value_respect_nulls(u.user_name) agent_name,
                ccp.agent_id agent_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(cp.title) checklist_point_name,
                SUM(IF(ccp.is_passed, 1, 0)) passed_calls_count,
                {$this->getStatusCountsSql()},
                FLOOR((SUM(IF(ccp.is_passed, 1, 0)) * 100.0 / COUNT(ccp.call_id)), 0) AS passed_percentage
            FROM (
                SELECT
                    agent_id,
                    call_id,
                    checklist_point_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                SELECT
                    user_name,
                    user_id
                FROM dictionary(users)
            ) u ON u.user_id = ccp.agent_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.agent_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.agent_id, ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['agent_id', 'checklist_point_id'], $bindings);
    }

    public function getAgentsCallsSummary(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?int $agentId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql($teamIds, $startDate, $endDate);
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($agentId !== null) {
            $where .= ' AND agent_id = :agentId';
            $bindings['agentId'] = $agentId;
        }

        $sql = <<<SQL
            SELECT
                calls_summary.agent_id agent_id,
                count() calls_count,
                1 agents_count,
                uniq(client_id) clients_count,
                sum(calls_summary.calls_duration) calls_duration
            FROM (
                SELECT
                    agent_id,
                    last_value_respect_nulls(client_id) client_id,
                    last_value_respect_nulls(ccp.call_duration) calls_duration
                FROM (
                    SELECT
                        checklist_point_id,
                        call_id,
                        agent_id,
                        client_id,
                        call_duration
                    FROM
                         calls_checklists_points
                    WHERE {$where}
                ) ccp
                INNER JOIN (
                    SELECT
                        title,
                        checklist_id,
                        checklist_point_id
                    FROM dictionary(checklists_points)
                ) cp ON ccp.checklist_point_id = cp.checklist_point_id
                INNER JOIN (
                    SELECT
                        user_name,
                        user_id
                    FROM dictionary(users)
                ) u ON u.user_id = ccp.agent_id
                {$this->getChecklistFilterSql($checklistId)}
                GROUP BY
                    agent_id,
                    ccp.call_id
            ) calls_summary
            GROUP BY calls_summary.agent_id
        SQL;

        return $this->getClient()->selectAsTree($sql, ['agent_id'], $bindings);
    }

    public function getAgentCallsStatistics(
        int $companyId,
        array $teamIds,
        int $agentId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql($teamIds, $startDate, $endDate) . ' AND agent_id = :agentId';
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);
        $bindings['agentId'] = $agentId;

        $sql = <<<SQL
            SELECT
                ccp.call_id call_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(ccp.call_duration) call_duration,
                last_value_respect_nulls(ccp.is_passed) is_passed,
                last_value_respect_nulls(ccp.status) status,
                last_value_respect_nulls(ccp.explanation) explanation,
                last_value_respect_nulls(ccp.call_type) call_type,
                last_value_respect_nulls(ccp.call_time) call_date,
                last_value_respect_nulls(cp.title) checklist_point_name
            FROM (
                SELECT
                    agent_id,
                    call_duration,
                    call_time,
                    call_type,
                    explanation,
                    call_id,
                    checklist_point_id,
                    call_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.call_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['call_id', 'checklist_point_id'], $bindings);
    }

    public function getClientsTotalStatistics(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?string $clientId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql($teamIds, $startDate, $endDate);
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($clientId !== null) {
            $where .= ' AND client_id = :clientId';
            $bindings['clientId'] = $clientId;
        }

        $sql = <<<SQL
            SELECT
                last_value_respect_nulls(cl.client_name) client_name,
                ccp.client_id client_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(cp.title) checklist_point_name,
                SUM(IF(ccp.is_passed, 1, 0)) passed_calls_count,
                {$this->getStatusCountsSql()},
                FLOOR((SUM(IF(ccp.is_passed, 1, 0)) * 100.0 / COUNT(ccp.call_id)), 0) AS passed_percentage
            FROM (
                SELECT
                    call_id,
                    client_id,
                    checklist_point_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                {$this->getFinalTableSqlUsingGroupBy(
                    'clients',
                    [
                        'company_id',
                        'client_id',
                    ],
                    'created',
                    [
                        'client_name',
                    ],
                    [
                        [
                            'type' => 'expression',
                            'value' => '
                                client_id IN
                                (
                                    SELECT DISTINCT
                                        client_id
                                    FROM
                                        calls_checklists_points
                                    WHERE ' . $where . '

                                )
                            ',
                        ],
                    ]
                )}
            ) cl ON ccp.client_id = cl.client_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.client_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.client_id, ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['client_id', 'checklist_point_id'], $bindings);
    }

    public function getClientsCallsSummary(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?string $clientId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql($teamIds, $startDate, $endDate);
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($clientId !== null) {
            $where .= ' AND client_id = :clientId';
            $bindings['clientId'] = $clientId;
        }

        $sql = <<<SQL
            SELECT
                calls_summary.client_id client_id,
                1 clients_count,
                uniq(agent_id) agents_count,
                count() calls_count,
                sum(calls_summary.calls_duration) calls_duration
            FROM (
                SELECT
                    client_id,
                    last_value_respect_nulls(agent_id) agent_id,
                    last_value_respect_nulls(ccp.call_duration) calls_duration
                FROM (
                    SELECT
                        checklist_point_id,
                        call_id,
                        agent_id,
                        client_id,
                        call_duration
                    FROM
                         calls_checklists_points
                    WHERE {$where}
                ) ccp
                INNER JOIN (
                    SELECT
                        title,
                        checklist_id,
                        checklist_point_id
                    FROM dictionary(checklists_points)
                ) cp ON ccp.checklist_point_id = cp.checklist_point_id
                {$this->getChecklistFilterSql($checklistId)}
                GROUP BY
                    client_id,
                    ccp.call_id
            ) calls_summary
            GROUP BY calls_summary.client_id
        SQL;

        return $this->getClient()->selectAsTree($sql, ['client_id'], $bindings);
    }

    public function getClientCallsStatistics(
        int $companyId,
        array $teamIds,
        string $clientId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql($teamIds, $startDate, $endDate) . ' AND client_id = :clientId';
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);
        $bindings['clientId'] = $clientId;

        $sql = <<<SQL
            SELECT
                ccp.call_id call_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(ccp.call_duration) call_duration,
                last_value_respect_nulls(ccp.is_passed) is_passed,
                last_value_respect_nulls(ccp.status) status,
                last_value_respect_nulls(ccp.explanation) explanation,
                last_value_respect_nulls(ccp.call_type) call_type,
                last_value_respect_nulls(ccp.call_time) call_date,
                last_value_respect_nulls(cp.title) checklist_point_name
            FROM (
                SELECT
                    call_id,
                    call_duration,
                    call_time,
                    call_type,
                    explanation,
                    call_id,
                    checklist_point_id,
                    call_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.call_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['call_id', 'checklist_point_id'], $bindings);
    }

    public function getCampaignsTotalStatistics(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?string $campaignId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($campaignId !== null) {
            $clientsFilterAndClause = ' AND campaign_id = :campaignId';
            $bindings['campaignId'] = $campaignId;
        } else {
            $clientsFilterAndClause = ' AND campaign_id IS NOT NULL';
        }

        $clientsFilter = [$this->getClientsFilter($where, $clientsFilterAndClause)];

        $sql = <<<SQL
            SELECT
                last_value_respect_nulls(cl.campaign_id) campaign_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(cp.title) checklist_point_name,
                SUM(IF(ccp.is_passed, 1, 0)) passed_calls_count,
                {$this->getStatusCountsSql()},
                FLOOR((SUM(IF(ccp.is_passed, 1, 0)) * 100.0 / COUNT(ccp.call_id)), 0) AS passed_percentage
            FROM (
                SELECT
                    call_id,
                    client_id,
                    checklist_point_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                {$this->getFinalTableSqlUsingGroupBy(
                    'clients',
                    [
                        'company_id',
                        'client_id',
                    ],
                    'created',
                    [
                        'campaign_id',
                    ],
                    $clientsFilter,
                )}
            ) cl ON ccp.client_id = cl.client_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                cl.campaign_id,
                ccp.checklist_point_id
            ORDER BY
                cl.campaign_id, ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['campaign_id', 'checklist_point_id'], $bindings);
    }

    public function getCampaignsCallsSummary(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?string $campaignId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($campaignId !== null) {
            $clientsFilterAndClause = ' AND campaign_id = :campaignId';
            $bindings['campaignId'] = $campaignId;
        } else {
            $clientsFilterAndClause = ' AND campaign_id IS NOT NULL';
        }

        $clientsFilter = [$this->getClientsFilter($where, $clientsFilterAndClause)];

        $sql = <<<SQL
            SELECT
                calls_summary.campaign_id campaign_id,
                count() calls_count,
                uniq(calls_summary.agent_id) agents_count,
                uniq(calls_summary.client_id) clients_count,
                sum(calls_summary.calls_duration) calls_duration
            FROM (
                SELECT
                    cl.campaign_id,
                    last_value_respect_nulls(ccp.agent_id) agent_id,
                    last_value_respect_nulls(ccp.client_id) client_id,
                    last_value_respect_nulls(ccp.call_duration) calls_duration
                FROM (
                    SELECT
                        checklist_point_id,
                        call_id,
                        agent_id,
                        client_id,
                        call_duration
                    FROM
                         calls_checklists_points
                    WHERE {$where}
                ) ccp
                INNER JOIN (
                    SELECT
                        title,
                        checklist_id,
                        checklist_point_id
                    FROM dictionary(checklists_points)
                ) cp ON ccp.checklist_point_id = cp.checklist_point_id
                INNER JOIN (
                    {$this->getFinalTableSqlUsingGroupBy(
                        'clients',
                        [
                            'company_id',
                            'client_id',
                        ],
                        'created',
                        [
                            'campaign_id',
                        ],
                        $clientsFilter,
                    )}
                ) cl ON ccp.client_id = cl.client_id
                {$this->getChecklistFilterSql($checklistId)}
                GROUP BY
                    cl.campaign_id,
                    ccp.call_id
            ) calls_summary
            GROUP BY calls_summary.campaign_id
        SQL;

        return $this->getClient()->selectAsTree($sql, ['campaign_id'], $bindings);
    }

    public function getCampaignCallsStatistics(
        int $companyId,
        array $teamIds,
        string $campaignId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);
        $clientsFilterAndClause = ' AND campaign_id = :campaignId';
        $bindings['campaignId'] = $campaignId;

        $clientsFilter = [$this->getClientsFilter($where, $clientsFilterAndClause)];

        $sql = <<<SQL
            SELECT
                ccp.call_id call_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(ccp.call_duration) call_duration,
                last_value_respect_nulls(ccp.is_passed) is_passed,
                last_value_respect_nulls(ccp.status) status,
                last_value_respect_nulls(ccp.explanation) explanation,
                last_value_respect_nulls(ccp.call_type) call_type,
                last_value_respect_nulls(ccp.call_time) call_date,
                last_value_respect_nulls(cp.title) checklist_point_name
            FROM (
                SELECT
                    call_id,
                    call_duration,
                    call_time,
                    call_type,
                    client_id,
                    explanation,
                    call_id,
                    checklist_point_id,
                    call_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                {$this->getFinalTableSqlUsingGroupBy(
                    'clients',
                    [
                        'company_id',
                        'client_id',
                    ],
                    'created',
                    [
                        'campaign_id',
                    ],
                    $clientsFilter,
                )}
            ) cl ON ccp.client_id = cl.client_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.call_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['call_id', 'checklist_point_id'], $bindings);
    }

    public function getSourcesTotalStatistics(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?string $source = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($source !== null) {
            $clientsFilterAndClause = ' AND source = :source';
            $bindings['source'] = $source;
        } else {
            $clientsFilterAndClause = ' AND source IS NOT NULL';
        }

        $clientsFilter = [$this->getClientsFilter($where, $clientsFilterAndClause)];

        $sql = <<<SQL
            SELECT
                last_value_respect_nulls(cl.source) source,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(cp.title) checklist_point_name,
                SUM(IF(ccp.is_passed, 1, 0)) passed_calls_count,
                {$this->getStatusCountsSql()},
                FLOOR((SUM(IF(ccp.is_passed, 1, 0)) * 100.0 / COUNT(ccp.call_id)), 0) AS passed_percentage
            FROM (
                SELECT
                    call_id,
                    client_id,
                    checklist_point_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                {$this->getFinalTableSqlUsingGroupBy(
                    'clients',
                    [
                        'company_id',
                        'client_id',
                    ],
                    'created',
                    [
                        'source',
                    ],
                    $clientsFilter,
                )}
            ) cl ON ccp.client_id = cl.client_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                cl.source,
                ccp.checklist_point_id
            ORDER BY
                cl.source, ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['source', 'checklist_point_id'], $bindings);
    }

    public function getSourcesCallsSummary(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?string $source = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        if ($source !== null) {
            $clientsFilterAndClause = ' AND source = :source';
            $bindings['source'] = $source;
        } else {
            $clientsFilterAndClause = ' AND source IS NOT NULL';
        }

        $clientsFilter = [$this->getClientsFilter($where, $clientsFilterAndClause)];

        $sql = <<<SQL
            SELECT
                calls_summary.source source,
                count() calls_count,
                uniq(calls_summary.agent_id) agents_count,
                uniq(calls_summary.client_id) clients_count,
                sum(calls_summary.calls_duration) calls_duration
            FROM (
                SELECT
                    cl.source,
                    last_value_respect_nulls(ccp.agent_id) agent_id,
                    last_value_respect_nulls(ccp.client_id) client_id,
                    last_value_respect_nulls(ccp.call_duration) calls_duration
                FROM (
                    SELECT
                        checklist_point_id,
                        call_id,
                        agent_id,
                        client_id,
                        call_duration
                    FROM
                         calls_checklists_points
                    WHERE {$where}
                ) ccp
                INNER JOIN (
                    SELECT
                        title,
                        checklist_id,
                        checklist_point_id
                    FROM dictionary(checklists_points)
                ) cp ON ccp.checklist_point_id = cp.checklist_point_id
                INNER JOIN (
                    {$this->getFinalTableSqlUsingGroupBy(
                        'clients',
                        [
                            'company_id',
                            'client_id',
                        ],
                        'created',
                        [
                            'source',
                        ],
                        $clientsFilter,
                    )}
                ) cl ON ccp.client_id = cl.client_id
                {$this->getChecklistFilterSql($checklistId)}
                GROUP BY
                    cl.source,
                    ccp.call_id
            ) calls_summary
            GROUP BY calls_summary.source
        SQL;

        return $this->getClient()->selectAsTree($sql, ['source'], $bindings);
    }

    public function getSourceCallsStatistics(
        int $companyId,
        array $teamIds,
        string $source,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql($teamIds, $startDate, $endDate);
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);
        $bindings['source'] = $source;
        $clientsFilter = [$this->getClientsFilter($where, ' AND source = :source')];

        $sql = <<<SQL
            SELECT
                ccp.call_id call_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(ccp.call_duration) call_duration,
                last_value_respect_nulls(ccp.is_passed) is_passed,
                last_value_respect_nulls(ccp.status) status,
                last_value_respect_nulls(ccp.explanation) explanation,
                last_value_respect_nulls(ccp.call_type) call_type,
                last_value_respect_nulls(ccp.call_time) call_date,
                last_value_respect_nulls(cp.title) checklist_point_name
            FROM (
                SELECT
                    call_id,
                    client_id,
                    call_duration,
                    call_time,
                    call_type,
                    explanation,
                    call_id,
                    checklist_point_id,
                    call_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                {$this->getFinalTableSqlUsingGroupBy(
                    'clients',
                    [
                        'company_id',
                        'client_id',
                    ],
                    'created',
                    [
                        'source',
                    ],
                    $clientsFilter,
                )}
            ) cl ON ccp.client_id = cl.client_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.call_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['call_id', 'checklist_point_id'], $bindings);
    }

    public function getTeamsTotalStatistics(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?int $teamId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        $teamFilter = '';

        if ($teamId !== null) {
            $teamFilter = ' WHERE team_id = :teamId';
            $bindings['teamId'] = $teamId;
        }

        $sql = <<<SQL
            SELECT
                teams.team_id team_id,
                last_value_respect_nulls(teams.team_name) team_name,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(cp.title) checklist_point_name,
                SUM(IF(ccp.is_passed, 1, 0)) passed_calls_count,
                {$this->getStatusCountsSql()},
                FLOOR((SUM(IF(ccp.is_passed, 1, 0)) * 100.0 / COUNT(ccp.call_id)), 0) AS passed_percentage
            FROM (
                SELECT
                    call_id,
                    agent_id,
                    client_id,
                    checklist_point_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                SELECT
                    user_id,
                    team_id,
                    team_name
                FROM
                    dictionary(users_teams)
                {$teamFilter}
                GROUP BY
                    user_id,
                    team_id,
                    team_name
            ) teams ON teams.user_id = ccp.agent_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                teams.team_id,
                ccp.checklist_point_id
            ORDER BY
                teams.team_id, ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['team_id', 'checklist_point_id'], $bindings);
    }

    public function getTeamsCallsSummary(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        ?int $teamId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        $teamFilter = '';

        if ($teamId !== null) {
            $teamFilter = ' WHERE team_id = :teamId';
            $bindings['teamId'] = $teamId;
        }

        $sql = <<<SQL
            SELECT
                calls_summary.team_id team_id,
                count() calls_count,
                uniq(agent_id) agents_count,
                uniq(client_id) clients_count,
                sum(calls_summary.calls_duration) calls_duration
            FROM (
                SELECT
                    teams.team_id,
                    last_value_respect_nulls(agent_id) agent_id,
                    last_value_respect_nulls(client_id) client_id,
                    last_value_respect_nulls(ccp.call_duration) calls_duration
                FROM (
                    SELECT
                        checklist_point_id,
                        agent_id,
                        call_id,
                        client_id,
                        call_duration
                    FROM
                         calls_checklists_points
                    WHERE {$where}
                ) ccp
                INNER JOIN (
                    SELECT
                        title,
                        checklist_id,
                        checklist_point_id
                    FROM dictionary(checklists_points)
                ) cp ON ccp.checklist_point_id = cp.checklist_point_id
                INNER JOIN (
                    SELECT
                        user_id,
                        team_id,
                        team_name
                    FROM
                        dictionary(users_teams)
                    {$teamFilter}
                    GROUP BY
                        user_id,
                        team_id,
                        team_name
                ) teams ON teams.user_id = ccp.agent_id
                {$this->getChecklistFilterSql($checklistId)}
                GROUP BY
                    teams.team_id,
                    ccp.call_id
            ) calls_summary
            GROUP BY calls_summary.team_id
        SQL;

        return $this->getClient()->selectAsTree($sql, ['team_id'], $bindings);
    }

    public function getTeamCallsStatistics(
        int $companyId,
        array $teamIds,
        int $teamId,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);
        $teamFilter = ' WHERE team_id = :teamId';
        $bindings['teamId'] = $teamId;

        $sql = <<<SQL
            SELECT
                ccp.call_id call_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(ccp.call_duration) call_duration,
                last_value_respect_nulls(ccp.is_passed) is_passed,
                last_value_respect_nulls(ccp.status) status,
                last_value_respect_nulls(ccp.explanation) explanation,
                last_value_respect_nulls(ccp.call_type) call_type,
                last_value_respect_nulls(ccp.call_time) call_date,
                last_value_respect_nulls(cp.title) checklist_point_name
            FROM (
                SELECT
                    call_id,
                    agent_id,
                    call_duration,
                    call_time,
                    call_type,
                    explanation,
                    call_id,
                    checklist_point_id,
                    call_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            INNER JOIN (
                SELECT
                    user_id,
                    team_id,
                    team_name
                FROM
                    dictionary(users_teams)
                {$teamFilter}
                GROUP BY
                    user_id,
                    team_id,
                    team_name
            ) teams ON teams.user_id = ccp.agent_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.call_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['call_id', 'checklist_point_id'], $bindings);
    }

    public function getCallsStatistics(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        $sql = <<<SQL
            SELECT
                ccp.call_id call_id,
                ccp.checklist_point_id checklist_point_id,
                last_value_respect_nulls(ccp.call_duration) call_duration,
                last_value_respect_nulls(ccp.is_passed) is_passed,
                last_value_respect_nulls(ccp.status) status,
                last_value_respect_nulls(ccp.explanation) explanation,
                last_value_respect_nulls(ccp.call_type) call_type,
                last_value_respect_nulls(ccp.call_time) call_date,
                last_value_respect_nulls(cp.title) checklist_point_name
            FROM (
                SELECT
                    call_id,
                    agent_id,
                    call_duration,
                    call_time,
                    call_type,
                    explanation,
                    call_id,
                    checklist_point_id,
                    call_id,
                    is_passed,
                    status
                FROM
                     calls_checklists_points
                WHERE {$where}
            ) ccp
            INNER JOIN (
                SELECT
                    title,
                    checklist_id,
                    checklist_point_id
                FROM dictionary(checklists_points)
            ) cp ON ccp.checklist_point_id = cp.checklist_point_id
            {$this->getChecklistFilterSql($checklistId)}
            GROUP BY
                ccp.call_id,
                ccp.checklist_point_id
            ORDER BY
                ccp.checklist_point_id;
        SQL;

        return $this->getClient()->selectAsTree($sql, ['call_id', 'checklist_point_id'], $bindings);
    }

    public function getCallsSummary(
        int $companyId,
        array $teamIds,
        ?int $checklistId = null,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): array {
        $where = $this->getStatisticsWhereSql(
            $teamIds,
            $startDate,
            $endDate,
        );
        $bindings = $this->getStatisticsBindings($teamIds, $companyId, $startDate, $endDate, $checklistId);

        $sql = <<<SQL
            SELECT
                uniq(call_id) calls_count,
                uniq(client_id) clients_count,
                uniq(agent_id) agents_count,
                sum(calls_summary.calls_duration) calls_duration
            FROM (
                SELECT
                    ccp.call_id,
                    last_value_respect_nulls(client_id) client_id,
                    last_value_respect_nulls(agent_id) agent_id,
                    last_value_respect_nulls(ccp.call_duration) calls_duration
                FROM (
                    SELECT
                        checklist_point_id,
                        agent_id,
                        call_id,
                        client_id,
                        call_duration
                    FROM
                         calls_checklists_points
                    WHERE {$where}
                ) ccp
                INNER JOIN (
                    SELECT
                        title,
                        checklist_id,
                        checklist_point_id
                    FROM dictionary(checklists_points)
                ) cp ON ccp.checklist_point_id = cp.checklist_point_id
                INNER JOIN (
                    SELECT
                        user_name,
                        user_id
                    FROM dictionary(users)
                ) u ON u.user_id = ccp.agent_id
                {$this->getChecklistFilterSql($checklistId)}
                GROUP BY
                    ccp.call_id
            ) calls_summary
        SQL;

        return $this->getClient()->selectAll($sql, $bindings);
    }

    public function saveCallChecklistPoints(array $callChecklistPoints): void
    {
        $this->getClient()->insert(
            $this->getTableName(),
            $callChecklistPoints,
            array_keys(current($callChecklistPoints))
        );
    }

    public function deleteByCall(Call $call): void
    {
        $this->getClient()->softDelete($this->getTableDataName(), [['call_id', '=', $call->getId()]]);
    }

    private function getStatisticsWhereSql(
        array $teamIds,
        Carbon $startDate = null,
        Carbon $endDate = null,
    ): string {
        $where = 'company_id = :companyId';

        if (!empty($teamIds)) {
            $where .= ' AND agent_id IN
                    (
                        SELECT
                            user_id
                        FROM
                            dictionary(users_teams)
                        WHERE
                            company_id = :companyId
                            AND team_id IN (:teamsId)
                    )';
        }

        if ($startDate !== null) {
            $where .= ' AND toDate(call_time) >= :startDate';
        }

        if ($endDate !== null) {
            $where .= ' AND toDate(call_time) <= :endDate';
        }

        return $where;
    }

    private function getStatisticsBindings(
        array $teamIds,
        int $companyId,
        Carbon $startDate = null,
        Carbon $endDate = null,
        ?int $checklistId = null,
    ): array {
        $bindings = [
            'companyId' => $companyId,
            'teamsId' => $teamIds,
        ];

        if ($startDate !== null) {
            $bindings['startDate'] = $startDate->toDateString();
        }

        if ($endDate !== null) {
            $bindings['endDate'] = $endDate->toDateString();
        }

        if ($checklistId !== null) {
            $bindings['checklistId'] = $checklistId;
        }

        return $bindings;
    }

    private function getChecklistFilterSql(
        ?int $checklistId = null,
    ): string {
        $checklistFilter = '';

        if ($checklistId !== null) {
            $checklistFilter = ' WHERE cp.checklist_id = :checklistId';
        }

        return $checklistFilter;
    }

    private function getClientsFilter(string $where, string $ands = ''): array
    {
        return [
            'type' => 'expression',
            'value' => <<<SQL
                client_id IN
                (
                    SELECT DISTINCT
                        client_id
                    FROM
                        calls_checklists_points
                    WHERE {$where}
                )
                {$ands}
            SQL,
        ];
    }

    private function getStatusCountsSql(): string
    {
        $statusPassed = self::STATUS_PASSED;
        $statusNotPassed = self::STATUS_NOT_PASSED;
        $statusNotEncountered = self::STATUS_NOT_ENCOUNTERED;

        return <<<SQL
            SUM(IF(ccp.status = '{$statusPassed}', 1, 0)) passed_count,
            SUM(IF(ccp.status = '{$statusNotPassed}', 1, 0)) not_passed_count,
            SUM(IF(ccp.status = '{$statusNotEncountered}', 1, 0)) not_encountered_count
        SQL;
    }
}
