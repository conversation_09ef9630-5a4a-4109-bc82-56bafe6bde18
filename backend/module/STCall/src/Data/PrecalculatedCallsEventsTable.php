<?php

declare(strict_types=1);

namespace STCall\Data;

use Carbon\Carbon;

class PrecalculatedCallsEventsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STClickhouse\Data\CallsWhereCondition;

    /**
     *
     * @todo Remove FINAL
     *
     * @param string $callId
     * @param int $companyId
     * @return int
     */
    public function getPrecalculatedEventsCount(string $callId, int $companyId): int
    {
        $sql = '
            SELECT
                count(*)
            FROM
                (
                    ' . $this->getFinalTableSqlUsingFinal($this->getTableName()) . '
                ) ce
            WHERE
                call_id = \'' . $callId . '\'
                AND company_id =  \'' . $companyId . '\'
        ';

        return (int) $this->getClient()->selectValue($sql);
    }

    /**
     * @param \STCompany\Entity\Event\Event $event
     * @param Carbon|null $minimalReviewTime
     * @param int $limit
     * @param bool $movedToNeutral
     * @return array
     */
    public function getReviewedCallCandidatesIdsWithEvent(
        \STCompany\Entity\Event\Event $event,
        ?Carbon $minimalReviewTime,
        int $limit = 10,
        bool $movedToNeutral = false,
    ): array {
        $sql = '
            SELECT
                DISTINCT call_id
            FROM
                precalculated_calls_events
            WHERE
                event_id = \'' . $event->getId() . '\'
                AND event_is_deleted = ' . (int) $movedToNeutral . '
                AND call_id IN (
                    SELECT
                        DISTINCT call_id
                    FROM
                        calls_reviews
                    WHERE
                        is_reviewed = 1
                        AND role_id = ' . $event->getRole()->getId() . '
        ';
        if ($minimalReviewTime instanceof Carbon) {
            $sql .= '
                        AND reviewed_time >= toDateTime32(\'' . $minimalReviewTime . '\')
            ';
        }
        $sql .= '
                )
            LIMIT
                ' . $limit . '
        ';

        return $this->getClient()->selectColumn($sql, 'call_id');
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @param \STCompany\Entity\Event\Event $event
     * @param array $callIds
     * @param bool $movedToNeutral
     * @return array
     */
    public function getPrecalculatedCallsEvents(
        \STCompany\Entity\Company $company,
        \STCompany\Entity\Event\Event $event,
        array $callIds,
        bool $movedToNeutral = false
    ): array {
        if (count($callIds) === 0) {
            return [];
        }

        $isDeleted = (int) $movedToNeutral;

        $sql = <<<SQL
            SELECT 
                pce.call_id call_id, 
                paragraph paragraph_number, 
                paragraph_start_time,
                event_is_deleted,
                event_text,
                event_en_text, 
                event_highlight,
                event_en_highlight,
                calls.call_language language, 
                paragraphs,
                paragraphs_en,
                additional_events
            FROM (
                {$this->getFinalTableSqlUsingGroupBy(
                    'precalculated_calls_events',
                    [
                        'company_id',
                        'role_id',
                        'call_id',
                        'paragraph',
                        'event_id',
                    ],
                    'created',
                    [
                        'event_is_deleted',
                        'paragraph_start_time',
                        'event_text',
                        'event_en_text',
                        'event_highlight',
                        'event_en_highlight',
                    ],
                    [
                        'call_id' => $callIds,
                        'company_id' => $company->getId(),
                        'event_id' => $event->getId(),
                    ]
                )}) pce
            INNER JOIN ({$this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'company_id',
                    'call_id',
                ],
                'created',
                [
                    'call_language',
                ],
                [
                    'call_id' => $callIds,
                    'company_id' => $company->getId(),
                ]
                )}) calls
                ON calls.call_id = pce.call_id 
            INNER JOIN
                (
                    SELECT
                        call_id,
                        groupArray(decrypt('aes-256-ofb', text, '{$company->getEncryptionKey()}')) paragraphs,
                        groupArray(decrypt('aes-256-ofb', en_text, '{$company->getEncryptionKey()}')) paragraphs_en
                    FROM
                    (
                        {$this->getFinalTableSqlUsingGroupBy(
                            'calls_paragraphs',
                            [
                                'company_id',
                                'call_id',
                                'paragraph_number',
                            ],
                            'created',
                            [
                                'text',
                                'en_text',
                            ],
                            [
                                'call_id' => $callIds,
                                'company_id' => $company->getId(),
                            ]
                        )}
                        ORDER BY
                            paragraph_number
                    )
                    GROUP BY
                        call_id
                ) cp
                ON cp.call_id = pce.call_id            
            INNER JOIN
                (
                    SELECT
                        call_id,
                        groupArray(
                            map
                            (
                                 'paragraph', toString(paragraph),
                                 'event_id', toString(event_id),
                                 'event_highlight', event_highlight,
                                 'event_en_highlight', event_en_highlight
                            )
                        ) additional_events
                    FROM
                    (
                        {$this->getFinalTableSqlUsingGroupBy(
                            'precalculated_calls_events',
                            [
                                'company_id',
                                'role_id',
                                'call_id',
                                'paragraph',
                                'event_id',
                            ],
                            'created',
                            [
                                'event_highlight',
                                'event_en_highlight',
                            ],
                            [
                                'call_id' => $callIds,
                                'role_id' => $event->getRole()->getId(),
                                'company_id' => $company->getId(),
                            ]
                        )}
                    )
                    GROUP BY
                        call_id
                ) ae
                ON ae.call_id = pce.call_id
            WHERE pce.event_is_deleted = {$isDeleted}
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    /**
     * @param \STCompany\Entity\Company $company
     * @param array|int $roleIds
     * @param array|string $callIds
     * @return array|string
     */
    public function getPrecalculatedCalls(\STCompany\Entity\Company $company, array|int $roleIds, array|string $callIds)
    {
        $sql = '
            SELECT 
                company_id,
                role_id,
                call_id,
                call_time,
                agent_id,
                client_id,
                call_origin,
                paragraph,
                paragraph_start_time,
                paragraph_speaker_role,
                event_changed_from_event_id,
                event_id,
                event_name,
                event_score,
                event_highlight,
                event_en_highlight,
                event_text,
                event_en_text,
                event_icon,
                event_is_pinned,
                event_is_deleted,
                event_category_id,
                event_category_name,
                event_color_id,
                event_fill_color_hex,
                event_outline_color_hex,
                event_color_priority,
                event_changed_from_event_id
            FROM
                (
                    ' .
                    $this->getFinalTableSqlUsingGroupBy(
                        'precalculated_calls_events',
                        [
                            'call_id',
                            'company_id',
                            'event_id',
                            'paragraph',
                            'role_id',
                        ],
                        'created',
                        [
                            'call_time',
                            'agent_id',
                            'client_id',
                            'call_origin',
                            'paragraph_start_time',
                            'paragraph_speaker_role',
                            'event_changed_from_event_id',
                            'event_name',
                            'event_score',
                            'event_highlight',
                            'event_en_highlight',
                            'event_text',
                            'event_en_text',
                            'event_icon',
                            'event_is_pinned',
                            'event_is_deleted',
                            'event_category_id',
                            'event_category_name',
                            'event_color_id',
                            'event_fill_color_hex',
                            'event_outline_color_hex',
                            'event_color_priority',
                            'event_changed_from_event_id',
                        ],
                        [
                            'company_id' => $company->getId(),
                            'call_id' => $callIds,
                            'role_id' => $roleIds,
                        ]
                    ) . '
                )
                WHERE
                    event_is_deleted = 0
        ';

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param array $eventsPrecalculated
     * @return int
     */
    public function save(array $eventsPrecalculated): int
    {
        if (count($eventsPrecalculated) === 0) {
            return 0;
        }

        $values = array_map(function ($eventsPrecalculated) {
            ksort($eventsPrecalculated);
            return $eventsPrecalculated;
        }, $eventsPrecalculated);

        $columns = array_keys(current($values));
        $this->getClient()->insert($this->getTableName(), $values, $columns);
        return count($eventsPrecalculated);
    }
}
