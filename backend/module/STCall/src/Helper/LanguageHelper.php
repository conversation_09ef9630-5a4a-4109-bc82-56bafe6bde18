<?php

declare(strict_types=1);

namespace STCall\Helper;

class LanguageHelper
{
    private const LANGUAGE_TO_BCP_47_MAPPINGS = [
        'af' => ['af-ZA'],
        'am' => ['am-ET'],
        'ar' => [
            'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IL', 'ar-IQ',
            'ar-JO', 'ar-KW', 'ar-LB', 'ar-LY', 'ar-MA', 'ar-OM',
            'ar-PS', 'ar-QA', 'ar-SA', 'ar-SY', 'ar-TN', 'ar-YE'
        ],
        'as' => [],
        'az' => ['az-AZ'],
        'ba' => [],
        'be' => [],
        'bg' => ['bg-BG'],
        'bn' => ['bn-IN'],
        'bo' => [],
        'br' => [],
        'bs' => ['bs-BA'],
        'ca' => ['ca-ES'],
        'cs' => ['cs-CZ'],
        'cy' => ['cy-GB'],
        'da' => ['da-DK'],
        'de' => ['de-AT', 'de-CH', 'de-DE'],
        'el' => ['el-GR'],
        'en' => [
            'en-US', 'en-AB', 'en-CA', 'en-GB', 'en-GH', 'en-HK',
            'en-IE', 'en-IN', 'en-KE', 'en-NG', 'en-NZ',
            'en-PH', 'en-SG', 'en-TZ', 'en-AU', 'en-ZA'
        ],
        'es' => [
            'es-ES', 'es-BO', 'es-CL', 'es-CO', 'es-CR',
            'es-CU', 'es-DO', 'es-EC', 'es-AR', 'es-GQ',
            'es-GT', 'es-HN', 'es-MX', 'es-NI', 'es-PA',
            'es-PE', 'es-PR', 'es-PY', 'es-SV', 'es-US',
            'es-UY', 'es-VE', 'es-419',
        ],
        'et' => ['et-EE'],
        'eu' => ['eu-ES'],
        'fa' => ['fa-IR'],
        'fi' => ['fi-FI'],
        'fil' => ['fil-PH'],
        'fo' => [],
        'fr' => ['fr-FR', 'fr-CA', 'fr-CH', 'fr-BE'],
        'ga' => ['ga-IE'],
        'gl' => ['gl-ES'],
        'gu' => ['gu-IN'],
        'ha' => [],
        'haw' => [],
        'he' => ['he-IL'],
        'hi' => ['hi-IN', 'hi-Latn'],
        'hr' => ['hr-HR'],
        'ht' => [],
        'hu' => ['hu-HU'],
        'hy' => ['hy-AM'],
        'id' => ['id-ID'],
        'is' => ['is-IS'],
        'it' => ['it-CH', 'it-IT'],
        'ja' => ['ja-JP'],
        'jv' => ['jv-ID'],
        'jw' => [],
        'ka' => ['ka-GE'],
        'kk' => ['kk-KZ'],
        'km' => ['km-KH'],
        'kn' => ['kn-IN'],
        'ko' => ['ko-KR'],
        'la' => [],
        'lb' => [],
        'ln' => [],
        'lo' => ['lo-LA'],
        'lt' => ['lt-LT'],
        'lv' => ['lv-LV'],
        'mg' => [],
        'mi' => [],
        'mk' => ['mk-MK'],
        'ml' => ['ml-IN'],
        'mn' => ['mn-MN'],
        'mr' => ['mr-IN'],
        'ms' => ['ms-MY'],
        'mt' => ['mt-MT'],
        'my' => ['my-MM'],
        'nb' => ['nb-NO'],
        'ne' => ['ne-NP'],
        'nl' => ['nl-NL', 'nl-BE'],
        'nn' => [],
        'no' => [],
        'oc' => [],
        'pa' => [],
        'pl' => ['pl-PL'],
        'ps' => ['ps-AF'],
        'pt' => ['pt-PT', 'pt-BR'],
        'ro' => ['ro-RO'],
        'ru' => ['ru-RU'],
        'sa' => [],
        'sd' => [],
        'si' => ['si-LK'],
        'sk' => ['sk-SK'],
        'sl' => ['sl-SI'],
        'sn' => [],
        'so' => ['so-SO'],
        'sq' => ['sq-AL'],
        'sr' => ['sr-RS'],
        'su' => [],
        'sv' => ['sv-SE'],
        'sw' => ['sw-KE', 'sw-TZ'],
        'ta' => ['ta-IN'],
        'te' => ['te-IN'],
        'tg' => [],
        'th' => ['th-TH'],
        'tk' => [],
        'tl' => [],
        'tr' => ['tr-TR'],
        'tt' => [],
        'uk' => ['uk-UA'],
        'ur' => [],
        'uz' => ['uz-UZ'],
        'vi' => ['vi-VN'],
        'yi' => [],
        'yo' => [],
        'wuu' => ['wuu-CN'],
        'yue' => ['yue-CN'],
        'zh' => ['zh-CN', 'zh-CN-shandong', 'zh-CN-sichuan', 'zh-HK', 'zh-TW'],
        'zu' => ['zu-ZA'],
    ];

    /**
     * @param string $languageCode
     * @return string
     * @throws \OutOfBoundsException
     */
    public static function getBCP47ByLanguageCode(string $languageCode): string
    {
        if (!isset(self::LANGUAGE_TO_BCP_47_MAPPINGS[$languageCode])) {
            throw new \OutOfBoundsException('There is no BCP-47 language for the language code "' . $languageCode . '"');
        }

        return self::LANGUAGE_TO_BCP_47_MAPPINGS[$languageCode][0];
    }

    /**
     * @param string $language
     * @return string
     */
    public static function getLanguageCodeByLanguage(string $language): string
    {
        if (array_key_exists($language, self::LANGUAGE_TO_BCP_47_MAPPINGS)) {
            return $language;
        }

        foreach (self::LANGUAGE_TO_BCP_47_MAPPINGS as $languageCode => $bcp47Languages) {
            if (in_array($language, $bcp47Languages)) {
                return $languageCode;
            }
        }

        throw new \OutOfBoundsException('There is no language code for the given language: "' . $language . '"');
    }

    /**
     * @param string $language
     * @return bool
     */
    public static function hasLanguageCodeByLanguage(string $language): bool
    {
        if (array_key_exists($language, self::LANGUAGE_TO_BCP_47_MAPPINGS)) {
            return true;
        }

        foreach (self::LANGUAGE_TO_BCP_47_MAPPINGS as $languageCode => $bcp47Languages) {
            if (in_array($language, $bcp47Languages)) {
                return true;
            }
        }

        return false;
    }
}
