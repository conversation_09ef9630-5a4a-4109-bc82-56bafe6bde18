<?php

declare(strict_types=1);

namespace STCall\Daemon\Export;

use Carbon\Carbon;
use STCall\Service\Export\CallExportGenerator;
use STCall\Service\Interfaces\CompanySaverInterface;
use STCall\Service\Interfaces\CompanySelectorInterface;
use ST<PERSON>ompany\Entity\Company;
use STRabbit\Entity\AbstractDaemon;

class CallExportDaemon extends AbstractDaemon
{
    public const string CALLS_EXPORTS_QUEUE_NAME = 'calls-exports';
    public const string CALLS_EXPORTS_QUEUE_ERROR_NAME = 'calls-exports-error';

    private static array $exportTypesList = [
        CallExportGenerator::EXPORT_TYPE_CALLS,
        CallExportGenerator::EXPORT_TYPE_PARAGRAPHS,
        CallExportGenerator::EXPORT_TYPE_EVENTS
    ];

    public function __construct(
        private readonly CompanySelectorInterface $companySelector,
        private readonly CallExportGenerator $callExportGenerator,
        private readonly CompanySaverInterface $companySaver,
    ) {
    }

    public function handle(string $message): void
    {
        $data = json_decode($message);

        $company = $this->companySelector->getCompany((int) $data->company_id);

        $endDate = Carbon::now();
        $startDate = $this->getStartDate($company, $endDate);

        foreach (self::$exportTypesList as $exportType) {
            $this->callExportGenerator->generate($company, $startDate, $endDate, $exportType);
        }

        $company->setLastExportDate($endDate);

        $this->companySaver->saveCompany($company);
    }

    private function getStartDate(Company $company, Carbon $endDate): Carbon
    {
        if ($company->getLastExportDate()) {
            $startDate = clone $company->getLastExportDate();
            $startDate->addSecond();

            return $startDate;
        }

        return (clone $endDate)->subDay();
    }
}
