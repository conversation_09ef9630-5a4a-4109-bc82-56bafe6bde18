<?php

declare(strict_types=1);

namespace STCall\Daemon\S3;

use STCall\Service\AwsTrait;
use STCompany\Service\CompanyService;
use STRabbit\Service\ProvidesRabbit;

class S3IntegrationCompanyDaemon extends \STRabbit\Entity\AbstractDaemon
{
    use ProvidesRabbit;
    use AwsTrait;

    public const QUEUE = 's3-integration-company';
    public const QUEUE_ERROR = 's3-integration-company-error';

    /**
     *
     * @param string $message
     * @return void
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);
        $channel = $this->rabbit()->getChannel();

        $awsConfig = $this->params()->offsetGet('aws_config');
        /** @var CompanyService $companyService */
        $companyService = $this->params()->offsetGet(CompanyService::class);
        $this->setAwsConfig($awsConfig['api']);
        $this->setEnv($awsConfig['env']);

        $companyId = (int) $data->company_id;
        $this->setCompany($companyService->getCompany($companyId));

        foreach ($this->getFilesListFromCompanyBucket(S3IntegrationCallDaemon::UNPROCESSED_DIR) as $filePath) {
            $fileName = pathinfo($filePath)['basename'];
            $unprocessedFilePath = S3IntegrationCallDaemon::UNPROCESSED_DIR . $fileName;
            $processingFilePath = S3IntegrationCallDaemon::PROCESSING_DIR . $fileName;

            $message = new \PhpAmqpLib\Message\AMQPMessage(json_encode([
                'file_path' => $processingFilePath,
                'company_id' => $companyId,
            ]));
            $channel->basic_publish($message, '', routing_key: S3IntegrationCallDaemon::QUEUE);

            // move file to processing dir
            $this->moveObject(
                $unprocessedFilePath,
                $processingFilePath
            );
        }

        $channel->close();
    }
}
