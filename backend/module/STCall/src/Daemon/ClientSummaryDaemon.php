<?php

declare(strict_types=1);

namespace STCall\Daemon;

use STAlgo\Service\AiSolutionsCommutatorService;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Entity\CallCollection;
use STCall\Service\CallService;
use STCall\Service\ClientSummaryService;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCompany\Entity\Company;
use STRabbit\Entity\AbstractDaemon;

class ClientSummaryDaemon extends AbstractDaemon
{
    public const string QUEUE = 'client-summary';
    public const string QUEUE_ERROR = 'client-summary-error';

    public function __construct(
        private readonly CallService $callService,
        private readonly CompanySelectorInterface $companySelector,
        private readonly ClientSummaryService $clientSummaryService,
        private readonly AiSolutionsCommutatorService $aiSolutionsCommutator
    ) {
    }

    public function handle(string $message): void
    {
        $data = json_decode($message, true);

        if (!isset($data['company_id']) || !isset($data['client_id'])) {
            throw new \InvalidArgumentException('Missing required parameters: company_id and client_id');
        }

        $company = $this->companySelector->getCompany((int) $data['company_id']);
        $clientId = (string) $data['client_id'];

        $lastSummary = $this->clientSummaryService->getLastSummary(
            $company->getId(),
            $clientId,
        );

        $startDate = $lastSummary?->getCreated();
        $calls = $this->callService->getClientCallsByDateRange(
            $company,
            $clientId,
            startDate: $startDate,
        );

        if ($calls->isEmpty()) {
            return;
        }

        $this->generateSummaryFromCalls(
            $company,
            $clientId,
            $calls,
        );
    }

    private function generateSummaryFromCalls(
        Company $company,
        string $clientId,
        CallCollection $calls,
    ): void {
        // Get client summary with last_call_time less than call time of the earliest call
        $previousSummary = $this->clientSummaryService->getLastSummary(
            $company->getId(),
            $clientId,
            $calls->current()->getTime()
        );

        // Get calls since previous summary
        if ($previousSummary !== null) {
            $calls = $this->callService->getClientCallsByDateRange(
                $company,
                $clientId,
                dateColumn: 'call_time',
                startDate: $previousSummary->getLastCallTime()
            );
        }

        try {
            $summaryDTO = $this->aiSolutionsCommutator->getClientSummary(
                $company->getId(),
                $calls,
                $previousSummary
            );
            $this->clientSummaryService->createClientSummaryFromDTO(
                $summaryDTO,
                $company->getId(),
                $clientId,
                $calls->last()->getTime()
            );

            return;
        } catch (ThirdPartyApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw new ThirdPartyApiException('Failed to generate client summary: ' . $e->getMessage());
        }
    }
}
