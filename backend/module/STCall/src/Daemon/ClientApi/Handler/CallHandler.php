<?php

declare(strict_types=1);

namespace STCall\Daemon\ClientApi\Handler;

class CallHandler extends AbstractHandler
{
    /**
     *
     * @param array $requestBody
     * @return bool
     * @throws \Exception
     */
    public function run(array $requestBody): bool
    {
        $recordingFile = $requestBody['recording_file'] ?? null;
        if (!empty($recordingFile)) {
            $this->callAnalysisService->addToQueue(
                $this->company->getId(),
                null,
                \STCall\Service\CallAnalysis\FileDownloadStep::CALL_FILE_DOWNLOAD_QUEUE,
                \STCall\Daemon\Analysis\FileDownloadStepDaemon::RABBITMQ_EXCHANGE_NAME,
                [
                    'params' => $requestBody,
                    'request_id' => $this->requestId,
                ],
                rand(0, 10 * 1000)
            );
            return true;
        }
        /** @var \STCall\Service\Import\UploadParams\UploadParams $uploadParams */
        $uploadParams = $this->hydrate(
            [
                'driver_name' => 'api-upload',
                'company' => $this->company,
                'user' => null,
                'options' => $requestBody,
            ],
            \STCall\Service\Import\UploadParams\UploadParams::class
        );
        $this->uploadService->uploadCall($uploadParams);
        return true;
    }
}
