<?php

declare(strict_types=1);

namespace STCall;

use Lam<PERSON>\ModuleManager\ModuleEvent;
use Lam<PERSON>\ModuleManager\ModuleManager;
use STCall\Listeners\CallAlgoEventsStepFinishedListener;
use STCall\Listeners\CallStepFinishedListener;
use STCall\Service\CallAnalysisService;
use STCall\Service\Precalculation\CallPrecalculationManagerService;
use STCall\Service\Precalculation\CallPrecalculationManagerServiceWrapper;
use STLib\ModuleManager\MultiConfigModule;

class Module extends MultiConfigModule
{
    /**
     *
     * @param ModuleManager $moduleManager
     * @return void
     */
    public function init(ModuleManager $moduleManager): void
    {
        $events = $moduleManager->getEventManager();
        $events->attach(
            ModuleEvent::EVENT_LOAD_MODULES_POST,
            function (ModuleEvent $moduleEvent) use ($events): void {
                $serviceManager = $moduleEvent->getParam('ServiceManager');
                $callPrecalculationManagerService = $serviceManager->get(CallPrecalculationManagerService::class);
                CallPrecalculationManagerServiceWrapper::setStaticCallPrecalculationManagerService(
                    $callPrecalculationManagerService
                );

                /** @var CallStepFinishedListener $callStepFinishedListener */
                $callStepFinishedListener = $serviceManager->get(CallStepFinishedListener::class);
                $callAlgoEventsStepFinishedListener = $serviceManager->get(CallAlgoEventsStepFinishedListener::class);

                $events->getSharedManager()->attach(
                    '*',
                    CallAnalysisService::EVENT_CALL_TRANSCRIBING_STEP_FINISHED,
                    [$callStepFinishedListener, 'listen']
                );
                $events->getSharedManager()->attach(
                    '*',
                    CallAnalysisService::EVENT_CALL_TRANSCRIBING_JOB_COLLECTION_STEP_FINISHED,
                    [$callStepFinishedListener, 'listen']
                );
                $events->getSharedManager()->attach(
                    '*',
                    CallAnalysisService::EVENT_CALL_TRANSLATION_STEP_FINISHED,
                    [$callStepFinishedListener, 'listen']
                );
                $events->getSharedManager()->attach(
                    '*',
                    CallAnalysisService::EVENT_CALL_SUMMARIZATION_STEP_FINISHED,
                    [$callStepFinishedListener, 'listen']
                );
                $events->getSharedManager()->attach(
                    '*',
                    CallAnalysisService::EVENT_CALL_CHECKLIST_STEP_FINISHED,
                    [$callStepFinishedListener, 'listen']
                );

                $events->getSharedManager()->attach(
                    '*',
                    CallAnalysisService::EVENT_CALL_ALGO_EVENTS_STEP_FINISHED,
                    [$callAlgoEventsStepFinishedListener, 'listen']
                );
            }
        );
    }
}
