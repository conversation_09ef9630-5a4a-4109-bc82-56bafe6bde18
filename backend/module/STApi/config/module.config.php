<?php

declare(strict_types=1);

namespace STApi;

use ST<PERSON>pi\ServiceProviding\ApplicationServiceProvider;
use STCompany\Service\Interfaces\ApplicationCreatorInterface as CompanyApplicationCreatorInterface;
use STLib\Mvc\Data\TableFactory;
use STLib\Mvc\DependencyInjection\DefaultFactory;

return [
    'controller_plugins' => [
        'invokables' => [
            'api' => Controller\Plugin\Api::class,
            'apiPermissionChecker' => Controller\Plugin\ApiPermissionChecker::class,
        ]
    ],
    'service_manager' => [
        'factories' => [
            Service\ApplicationService::class => DefaultFactory::class,
            Data\ApiApplicationsTable::class => TableFactory::class,
        ],
        'aliases' => [
            CompanyApplicationCreatorInterface::class => ApplicationServiceProvider::class,
        ],
    ]
];
