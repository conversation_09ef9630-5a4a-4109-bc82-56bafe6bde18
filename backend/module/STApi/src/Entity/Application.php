<?php

declare(strict_types=1);

namespace STApi\Entity;

class Application
{
    /**
     *
     * @var int
     */
    protected ?int $id = null;

    /**
     *
     * @var string
     */
    protected string $name;

    /**
     *
     * @var string
     */
    protected string $token;

    /**
     *
     * @var int
     */
    protected int $type;

    /**
     *
     * @var int|null
     */
    protected ?int $companyId = null;

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     *
     * @return string
     */
    public function getToken(): string
    {
        return $this->token;
    }

    /**
     *
     * @return int
     */
    public function getType(): int
    {
        return $this->type;
    }

    /**
     *
     * @return int|null
     */
    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    /**
     *
     * @return bool
     */
    public function isRobonoteApplication(): bool
    {
        return $this->getType() === \STApi\Data\ApiApplicationsTable::ROBONOTE_APPLICATION;
    }

    /**
     *
     * @return bool
     */
    public function isThirdPartyCompanyApplication(): bool
    {
        return $this->getType() === \STApi\Data\ApiApplicationsTable::THIRD_PARTY_COMPANY_APPLICATION;
    }

    /**
     *
     * @return bool
     */
    public function isChildApplication(): bool
    {
        return $this->getType() === \STApi\Data\ApiApplicationsTable::CHILD_APPLICATION;
    }

    /**
     *
     * @param int $id
     * @return \STApi\Entity\Application
     */
    public function setId(int $id): Application
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string $name
     * @return \STApi\Entity\Application
     */
    public function setName(string $name): Application
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param string $token
     * @return \STApi\Entity\Application
     */
    public function setToken(string $token): Application
    {
        $this->token = $token;
        return $this;
    }

    /**
     *
     * @param int $type
     * @return Application
     */
    public function setType(int $type): Application
    {
        $this->type = $type;
        return $this;
    }

    /**
     *
     * @param int|null $companyId
     * @return Application
     */
    public function setCompanyId(?int $companyId): Application
    {
        $this->companyId = $companyId;
        return $this;
    }
}
