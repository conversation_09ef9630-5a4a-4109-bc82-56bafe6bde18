<?php

declare(strict_types=1);

namespace STApi\Data;

class ApiApplicationsTable extends \STLib\Db\AbstractTable
{
    public const ROBONOTE_APPLICATION = 0;
    public const THIRD_PARTY_COMPANY_APPLICATION = 1;
    public const CHILD_APPLICATION = 2;

    /**
     *
     * @param int $companyId
     * @return \ArrayObject|null
     */
    public function getApplicationByCompanyId(int $companyId): ?\ArrayObject
    {
        return $this->tableGateway->select([
            'company_id' => $companyId,
            'application_type' => static::THIRD_PARTY_COMPANY_APPLICATION,
        ])->current();
    }

    /**
     *
     * @param string $token
     * @return \ArrayObject|null
     */
    public function getApplicationByToken(string $token): ?\ArrayObject
    {
        return $this->tableGateway->select([
            'application_token' => $token,
        ])->current();
    }

    /**
     *
     * @param \STApi\Entity\Application $application
     * @return int
     */
    public function saveApplication(\STApi\Entity\Application $application): int
    {
        $data = [
            'application_name' => $application->getName(),
            'application_type' => $application->getType(),
            'company_id' => $application->getCompanyId(),
            'application_token' => $application->getToken(),
        ];

        if ($application->getId() > 0) {
            $this->tableGateway->update($data, [
                'application_id' => $application->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $application->setId((int)$this->tableGateway->lastInsertValue);
        }

        return $application->getId();
    }
}
