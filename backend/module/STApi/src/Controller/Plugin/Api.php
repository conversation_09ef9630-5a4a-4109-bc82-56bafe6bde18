<?php

declare(strict_types=1);

namespace STApi\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Api extends AbstractPlugin
{
    /**
     *
     * @return \STApi\Service\ApplicationService
     */
    public function application(): \STApi\Service\ApplicationService
    {
        return $this->getController()->getServiceManager()->get(\STApi\Service\ApplicationService::class);
    }
}
