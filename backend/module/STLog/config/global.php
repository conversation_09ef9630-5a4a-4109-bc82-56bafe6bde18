<?php

declare(strict_types=1);

use STRoboTruck\Service\Logger\Writer as RoboTruckWriter;

return [
    'logger' => [
        'default' => [
            'writers' => [
                'file' => [
                    'type' => 'stream',
                    'priority' => 10,
                    'options' => [
                        'stream' => getcwd() . '/data/log/default.log',
                    ],
                ],
            ],
        ],
        'clickhouse-migrations' => [
            'writers' => [
                'stream' => [
                    'type' => 'stream',
                    'priority' => 10,
                    'options' => [
                        'stream' => 'php://output',
                    ],
                ],
            ],
        ],
        'api-calls-logs' => [
            'writers' => [
                'stream' => [
                    'type' => \STClickhouse\Service\Logger\Writer::class,
                    'options' => [],
                    'settings' => [
                        'table' => 'api_calls_logs',
                        'additinal_columns' => [
                            'id',
                            'company_id',
                            'call_id',
                        ],
                        'with_message' => true,
                    ],
                ],
            ],
        ],
        'robo-truck' => [
            'writers' => [
                'stream' => [
                    'type' => RoboTruckWriter::class,
                    'options' => [],
                    'settings' => [
                        'env' => getenv('APP_ENV') ?? 'local',
                    ],
                ],
            ],
        ],
    ]
];
