<?php

declare(strict_types=1);

namespace Api;

use Laminas\Router\Http\Method;
use Laminas\Router\Http\Segment;

return [
    'router' => [
        'routes' => [
            'robo-metrics' => [
                'type' => \Laminas\Router\Http\Segment::class,
                'options' => [
                    'route' => '/robo-metrics[/]',
                    'defaults' => [
                        'controller' => Controller\RoboMetrics\IndexController::class,
                        'action' => 'not-found',
                    ],
                ],
                'child_routes' => [
                    'dictionary' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'dictionary[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\DictionaryController::class,
                            ],
                        ],
                        'child_routes' => [
                            'companies' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'companies[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-companies',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'algo-events' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'algo-events[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-algo-events',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'companies-algo-apis' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'companies-algo-apis[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\AlgoApiController::class,
                            ],
                        ],
                        'child_routes' => [
                            'get' => [
                                'type' => \Laminas\Router\Http\Method::class,
                                'options' => [
                                    'verb' => 'get',
                                    'defaults' => [
                                        'action' => 'get-companies-algo-apis',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'algo-statistics' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'algo-statistics[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\AlgoStatisticsController::class,
                            ],
                        ],
                        'child_routes' => [
                            'algo-events-count-per-paragraph' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'algo-events-count-per-paragraph[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-algo-events-count-per-paragraph',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'algo-events-count-per-call' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'algo-events-count-per-call[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-algo-events-count-per-call',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'average-score-per-event' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'average-score-per-event[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-average-score-per-event',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'paragraphs-count-per-call' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'paragraphs-count-per-call[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-paragraphs-count-per-call',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'paragraphs-length' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'paragraphs-length[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-paragraphs-length',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'algo-events-statistics' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'algo-events-statistics[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-algo-events-statistics',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'algo-apis' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'algo-apis[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\AlgoApiController::class,
                            ],
                        ],
                        'child_routes' => [
                            'get' => [
                                'type' => \Laminas\Router\Http\Method::class,
                                'options' => [
                                    'verb' => 'get',
                                    'defaults' => [
                                        'action' => 'get-algo-apis',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'translation-drivers' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'translation-drivers[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\LanguageDriverController::class,
                            ],
                        ],
                        'child_routes' => [
                            'get' => [
                                'type' => \Laminas\Router\Http\Method::class,
                                'options' => [
                                    'verb' => 'get',
                                    'defaults' => [
                                        'action' => 'get-translation-drivers',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'transcribe-drivers' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'transcribe-drivers[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\LanguageDriverController::class,
                            ],
                        ],
                        'child_routes' => [
                            'get' => [
                                'type' => \Laminas\Router\Http\Method::class,
                                'options' => [
                                    'verb' => 'get',
                                    'defaults' => [
                                        'action' => 'get-transcribe-drivers',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'companies-calls-volume' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'companies-calls-volume[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\CompanyController::class,
                            ],
                        ],
                        'child_routes' => [
                            'get' => [
                                'type' => \Laminas\Router\Http\Method::class,
                                'options' => [
                                    'verb' => 'get',
                                    'defaults' => [
                                        'action' => 'get-companies-calls-volume',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'companies' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'companies[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\CompanyController::class,
                            ],
                        ],
                        'child_routes' => [
                            'get' => [
                                'type' => \Laminas\Router\Http\Method::class,
                                'options' => [
                                    'verb' => 'get',
                                    'defaults' => [
                                        'action' => 'get-companies',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'calls' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'calls[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\CallController::class,
                            ],
                        ],
                        'child_routes' => [
                            'summary' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'summary[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-summary',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'chat-calls-summary' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'chat-calls-summary[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-chat-calls-summary',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'call-languages' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'call-languages[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-call-languages',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'call-logs' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'call-logs[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-call-logs',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'call-logs-by-message' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'call-logs-by-message[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-call-logs-by-message',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'billing-summary' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'billing-summary[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-billing-summary',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'usage-statistics' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'usage-statistics[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-usage-statistics',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'clickhouse' => [
                        'type' => \Laminas\Router\Http\Segment::class,
                        'options' => [
                            'route' => 'clickhouse[/]',
                            'defaults' => [
                                'controller' => Controller\RoboMetrics\ClickhouseController::class,
                            ],
                        ],
                        'child_routes' => [
                            'performance' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'performance[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'performance',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'slow-queries' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'slow-queries[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'slow-queries',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'error-queries' => [
                                'type' => \Laminas\Router\Http\Segment::class,
                                'options' => [
                                    'route' => 'error-queries[/]',
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => \Laminas\Router\Http\Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'error-queries',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
