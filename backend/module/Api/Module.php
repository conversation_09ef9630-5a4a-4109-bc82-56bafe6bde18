<?php

declare(strict_types=1);

namespace Api;

class Module extends \STLib\ModuleManager\MultiConfigModule
{
    public function __construct()
    {
        parent::__construct();
        $this->addConfigFile('routing.v0.config.php');
        $this->addConfigFile('routing.v0.admin.config.php');
        $this->addConfigFile('routing.v0.checklist.config.php');
        $this->addConfigFile('routing.v0.onboarding.config.php');
        $this->addConfigFile('routing.robo-metrics.config.php');
    }
}
