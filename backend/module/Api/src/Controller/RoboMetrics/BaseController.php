<?php

declare(strict_types=1);

namespace Api\Controller\RoboMetrics;

/**
 *
 * @method \STRoboMetrics\Controller\Plugin\RoboMetrics roboMetrics()
 */
class BaseController extends \STLib\Mvc\Controller\AbstractController
{
    /**
     *
     * @var \STLib\Expand\Collection|null
     */
    protected ?\STLib\Expand\Collection $apiParams = null;

    /**
     *
     * @param \Laminas\Mvc\MvcEvent $event
     * @return \Laminas\Http\Response
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     * @throws \STApi\Entity\Exception\InvalidResponseFormatApiException
     * @throws \Throwable
     */
    public function onDispatch(\Laminas\Mvc\MvcEvent $event): \Laminas\Http\Response
    {
        try {
            $this->apiPermissionChecker()->checkRobonoteChildApiOnlyAccess();
            $result = parent::onDispatch($event);
        } catch (\Throwable $e) {
            $appEnv = getenv('APP_ENV');
            if ($appEnv === 'console') {
                error_log($this->getErrorLogMessage($e));
            }
            $debug = $this->getServiceManager()->get('config')['api']['debug'] ?? true;
            if ($debug) {
                throw $e;
            }
            $statusCode = $e->getCode();
            if ($statusCode > 599 || $statusCode < 100) {
                $statusCode = \Laminas\Http\Response::STATUS_CODE_500;
            }
            $messages = is_array(json_decode($e->getMessage(), true)) ? json_decode($e->getMessage(), true) : $e->getMessage();
            $error = [
                'code' => $statusCode,
                'messages' => $messages,
            ];
        }
        if (isset($result) && $result instanceof \Laminas\Http\Response) {
            return $result;
        }
        return $this->output([
            'result' => $result ?? null,
            'error' => $error ?? null,
        ], $statusCode ?? \Laminas\Http\Response::STATUS_CODE_200);
    }
}
