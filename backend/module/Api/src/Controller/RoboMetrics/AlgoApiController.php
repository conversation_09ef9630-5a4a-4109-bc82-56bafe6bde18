<?php

declare(strict_types=1);

namespace Api\Controller\RoboMetrics;

class AlgoApiController extends BaseController
{
    /**
     *
     * @return array
     */
    public function getCompaniesAlgoApisAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoApi()->getCompaniesWithAlgoApis(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getAlgoApisAction(): array
    {
        return [
            'result' => $this->roboMetrics()->algoApi()->getAlgoApisWithCompanies(),
        ];
    }
}
