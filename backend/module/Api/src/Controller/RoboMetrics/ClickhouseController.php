<?php

declare(strict_types=1);

namespace Api\Controller\RoboMetrics;

class ClickhouseController extends BaseController
{
    /**
     *
     * @return array
     */
    public function performanceAction(): array
    {
        $afterTime = \Carbon\Carbon::parse($this->getApiParam('after_time'))->startOfDay();
        return [
            'performance' => $this->roboMetrics()->clickhouse()->getPerformance($afterTime),
        ];
    }

    /**
     *
     * @return array
     */
    public function slowQueriesAction(): array
    {
        $afterTime = \Carbon\Carbon::parse($this->getApiParam('after_time'))->startOfDay();
        $minDuration = (int) $this->getApiParam('min_duration');
        return [
            'slow_queries' => $this->roboMetrics()->clickhouse()->getSlowQueries($minDuration, $afterTime),
        ];
    }

    /**
     *
     * @return array
     */
    public function errorQueriesAction(): array
    {
        $afterTime = \Carbon\Carbon::parse($this->getApiParam('after_time'))->startOfDay();
        return [
            'error_queries' => $this->roboMetrics()->clickhouse()->getErrorQueries($afterTime),
        ];
    }
}
