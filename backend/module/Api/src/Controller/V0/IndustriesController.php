<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

final class IndustriesController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getIndustriesAction(): array
    {
        $withLlmEvents = (bool) $this->getApiParam('with_llm_events');

        return [
            'industries' => $this->industry()->industrySelector()->getIndustries($withLlmEvents)->toArray(),
        ];
    }
}
