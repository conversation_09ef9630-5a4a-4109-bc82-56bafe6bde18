<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use STApi\Entity\Exception\ValidationApiException;
use STIndustry\Validator\IndustryExistsValidator;

final class IndustryAlgoApiController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     * @throws ValidationApiException
     */
    public function getAlgoApisAction(): array
    {
        $industryId = (int) $this->getApiParam('id');

        /** @var IndustryExistsValidator $validator */
        $validator = $this->getServiceManager()->get(IndustryExistsValidator::class);
        $validator->setInstance($industryId);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        return [
            'algo_apis' => $this->industry()->algoApiSelector()->getAlgoApis($industryId)->toArray(),
        ];
    }
}
