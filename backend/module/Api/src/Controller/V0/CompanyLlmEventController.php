<?php

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ValidationApiException;
use STCompany\Validator\ConnectLlmEventValidator;
use STCompany\Validator\DisconnectLlmEventValidator;

class CompanyLlmEventController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getEventsAction(): array
    {
        return [
            'events' => $this->company()->llmEventSelector()->getLlmEvents($this->company->getId())->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     */
    public function getEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        return [
            'event' => $this->company()->llmEventSelector()->getLlmEvent($id, $this->company->getId())->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     */
    public function connectEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var ConnectLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(ConnectLlmEventValidator::class);
        $validator->setInstance(['llm_event_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $event = $this->company()->llmEventConnector()->connect($id, $this->company->getId());

        return [
            'event' => $event->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function disconnectEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var DisconnectLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(DisconnectLlmEventValidator::class);
        $validator->setInstance(['llm_event_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->llmEventConnector()->disconnect($id, $this->company->getId());

        return [
            'is_deleted' => true,
            'message' => 'Successfully disconnected event from company.',
        ];
    }
}
