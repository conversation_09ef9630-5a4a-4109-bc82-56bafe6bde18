<?php

declare(strict_types=1);

namespace Api\Controller\V0;

class CompanyVocabularyController extends BaseController
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @return array
     */
    public function getVocabularyAction(): array
    {
        $vocabularyCollection = $this->company()->vocabulary()->getCompanyVocabulary($this->company->getId());
        return [
            'vocabulary' => $vocabularyCollection->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws ValidationApiException
     */
    public function saveWordAction(): array
    {
        $vocabularyWordId = (int) $this->getApiParam('word_id');
        $vocabularyWord = $this->hydrate(array_merge($this->getApiParams()->toArray(), [
            'company_id' => $this->company->getId(),
        ]), \STCompany\Entity\CompanyVocabularyWord::class);
        if ($vocabularyWordId > 0) {
            // call to check access vocabulary word
            $this->company()->vocabulary()->getCompanyVocabularyWord($this->company->getId(), $vocabularyWordId);
            $vocabularyWord->setId($vocabularyWordId);
        }

        $validator = $this->getServiceManager()->get(\STCompany\Validator\CompanyVocabularyWordValidator::class);
        $validator->setInstance($vocabularyWord);
        $validator->validate();

        if ($validator->hasError()) {
            throw new \STApi\Entity\Exception\ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $vocabularyWordId = $this->company()->vocabulary()->saveCompanyVocabularyWord($vocabularyWord);
        return [
            'word' => $this->company()->vocabulary()->getCompanyVocabularyWord($this->company->getId(), $vocabularyWordId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function deleteWordAction(): array
    {
        $vocabularyWordId = (int) $this->getApiParam('word_id');
        // call to check access vocabulary word
        $vocabularyWord = $this->company()->vocabulary()->getCompanyVocabularyWord($this->company->getId(), $vocabularyWordId);
        $this->company()->vocabulary()->deleteCompanyVocabularyWord($this->company->getId(), $vocabularyWord->getId());

        return [
            'success' => true,
        ];
    }
}
