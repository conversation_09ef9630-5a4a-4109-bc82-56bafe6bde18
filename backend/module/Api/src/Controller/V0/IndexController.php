<?php

declare(strict_types=1);

namespace Api\Controller\V0;

class IndexController extends BaseController
{
    /**
     *
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function notFoundAction()
    {
        throw new \STApi\Entity\Exception\NotFoundApiException();
    }

    /**
     *
     * @return \Laminas\Http\Response
     * @throws \STApi\Entity\Exception\InvalidResponseFormatApiException
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function pdfAction(): \Laminas\Http\Response
    {
        // remove when pdf generation libs will start work without deprecated errors
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);

        $fileName = $this->getApiParam('filename');
        $html = $this->getApiParam('html');
        $library = $this->getApiParam('library');

        if ($library === 'dompdf') {
            $dompdf = new \Dompdf\Dompdf();
            $dompdf->loadHtml($html);
            $dompdf->render();
            $pdfContent = $dompdf->stream($fileName);
        } elseif ($library === 'wk') {
            $pdf = new \mikehaertl\wkhtmlto\Pdf();
            $pdf->addPage($html);
            $pdfContent = $pdf->toString();
        } else {
            throw new \STApi\Entity\Exception\ValidationApiException('Unknown library value, only "dompdf" or "wk" values are available');
        }
        return $this->output($pdfContent, \Laminas\Http\Response::STATUS_CODE_200, static::PDF_FORMAT);
    }

    /**
     *
     * @return array
     */
    public function getLanguagesAction(): array
    {
        $result = [];
        foreach (\STCall\Data\CallsTable::LANGUAGES as $language => $name) {
            $result[] = [
                'name' => $name,
                'language' => $language,
            ];
        }
        return $result;
    }

    /**
     *
     * @return \Laminas\Stdlib\ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function knowledgeBaseProxyAction(): \Laminas\Stdlib\ResponseInterface
    {
        $client = new \GuzzleHttp\Client();
        $path = (string) $this->params()->fromRoute('path');
        $domain = (string) $this->params()->fromQuery('domain');

        $result = $client->request(
            'GET',
            'https://' . $domain . '/wp-json/wp/v2/' . $path,
            [
                'auth' => [
                    'admin',
                    getenv('KNOWLEDGE_BASE_TOKEN'),
                ],
                'query' => $this->params()->fromQuery(),
            ]
        );

        $this->getResponse()
            ->setContent($result->getBody()->getContents())
            ->setStatusCode($result->getStatusCode())
            ->getHeaders()
            ->addHeaders($result->getHeaders());

        return $this->getResponse();
    }
}
