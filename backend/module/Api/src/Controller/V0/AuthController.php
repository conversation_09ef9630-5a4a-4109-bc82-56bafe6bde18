<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ST<PERSON>pi\Entity\Exception\BadRequestApiException;
use ST<PERSON>pi\Entity\Exception\ForbiddenApiException;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\TooManyUnsuccessfullyLoginAttempts;
use STApi\Entity\Exception\ValidationApiException;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STUser\Entity\User;
use STUser\Service\UserService;
use STUser\Validator\UserValidator;

class AuthController extends BaseController
{
    use BaseHydratorTrait;

    /**
     *
     * @var bool
     */
    protected bool $checkAuthentification = false;

    /**
     *
     * @var bool
     */
    protected bool $checkCompanyAccess = false;

    /**
     *
     * @var bool
     */
    protected bool $checkRoleAccess = false;

    /**
     * @return array
     * @throws ValidationApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     */
    public function signUpAction(): array
    {
        /** @var User $user */
        $user = $this->hydrate($this->getApiParams()->toArray(), User::class);

        if ($user->getEmail()) {
            $user->setEmail(trim($user->getEmail()));
        }

        $config = $this->getServiceManager()->get('config');

        $validator = $this->getServiceManager()->get(UserValidator::class);
        $validator->setInstance($user);
        $validator->validate('email', 'password', 'password-confirm', 'email-not-exists', 'avatar');
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $twoFactorSecret = $this->auth()->twoFactor()->createSecret();
        $user
                ->setTwoFactorSecret($twoFactorSecret)
                ->hashPassword($config['auth']['salt']);
        $user->isFirstLogin(true);
        $this->user()->saveUser($user);

        return [
            'two-auth-qr-code' => $this->auth()->twoFactor()->getQRCodeImageAsDataUri($user->getEmail(), $user->getTwoFactorSecret()),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws ForbiddenApiException
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     * @throws BadRequestApiException
     * @throws NotFoundApiException
     * @throws TooManyUnsuccessfullyLoginAttempts
     */
    public function signInAction(): array
    {
        $config = $this->getServiceManager()->get('config');
        $code = $this->getApiParam('code');

        $validator = $this->getServiceManager()->get(UserValidator::class);
        /** @var User $userInput */
        $userInput = $this->hydrate($this->getApiParams()->toArray(), User::class);
        $validator->setInstance($userInput);
        $validator->validate('email', 'email-exists');

        /** @var UserService $userService */
        $userService = $this->user();

        try {
            $user = $userService->getUserByEmail($userInput->getEmail());

            if ($user->isFirstLogin()) {
                throw new ForbiddenApiException('User need to change password first');
            }

            $user->setPassword($userInput->getPassword());
            $user->hashPassword($config['auth']['salt']);
            $createdUser = $this->auth()->signIn($user);
        } catch (NotFoundApiException $e) {
            throw new BadRequestApiException('Invalid email or password');
        } catch (\Exception $e) {
            throw $e;
        }

        $qrCode = null;
        if (!$user->hasTwoFactorSecret()) {
            $twoFactorSecret = $this->auth()->twoFactor()->createSecret();
            $user->setTwoFactorSecret($twoFactorSecret);
            $userService->saveUser($user);
            $qrCode = $this->auth()->twoFactor()->getQRCodeImageAsDataUri($user->getEmail(), $user->getTwoFactorSecret());
        }

        if (is_null($code)) {
            return [
                'is-qr-code-generated' => !is_null($qrCode),
                'two-auth-qr-code' => $qrCode,
            ];
        }

        if ($this->auth()->twoFactor()->verifyCode($user->getTwoFactorSecret(), $code)) {
            $this->auth()->backup($createdUser);
            return [
                'user' => $createdUser->toArray(),
            ];
        }

        throw new BadRequestApiException('Invalid code of two factor authentification');
    }

    /**
     *
     * @return array
     */
    public function signOutAction(): array
    {
        if ($this->auth()->isSigned()) {
            $this->auth()->signOut($this->getToken());
        }
        return [
            'signed-out' => true,
        ];
    }

    /**
     *
     * @return array
     * @throws ValidationApiException
     * @throws NotFoundApiException
     */
    public function passwordRecoveryRequestAction(): array
    {
        $email = $this->getApiParam('email');
        try {
            $user = $this->user()->getUserByEmail($email);
        } catch (NotFoundApiException $e) {
            throw new BadRequestApiException('Invalid e-mail');
        } catch (\Exception $e) {
            throw $e;
        }
        $code = $this->auth()->getChangePasswordCode($user);
        $redirectUrl = $this->front()->makeAbsolutUrl(
            str_replace('{code}', $code, $this->getApiParam('redirect_url'))
        );

        $result = $this->mail()->addToQueue($this->front()->getActiveFront()->getId(), $user, [
            'template_id' => 'reset-password',
            'substitutions' => [
                'dynamic_html' => [
                    'reset_link' => $redirectUrl,
                ],
            ],
        ]);
        return [
            'queued' => $result,
        ];
    }

    /**
     *
     * @return array
     * @throws ValidationApiException
     */
    public function passwordRecoveryUpdateAction(): array
    {
        $config = $this->getServiceManager()->get('config');
        $code = $this->getApiParam('code');

        $user = $this->auth()->getUserByChangePasswordCode($code);

        $validator = $this->getServiceManager()->get(UserValidator::class);
        $validator->setInstance($this->hydrate($this->getApiParams()->toArray(), \STUser\Entity\User::class));
        $validator->validate('password', 'password-confirm');
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $user->setPassword($this->getApiParam('password'));
        $user->hashPassword($config['auth']['salt']);
        $user->isFirstLogin(false);
        $this->user()->saveUser($user, updatePassword: true);
        $user->clearPassword();

        $this->auth()->removeRecoveryCode($code);
        return [
            'changed' => true,
        ];
    }
}
