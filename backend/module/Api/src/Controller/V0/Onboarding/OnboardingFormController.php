<?php

declare(strict_types=1);

namespace Api\Controller\V0\Onboarding;

use Api\Controller\V0\BaseController;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STOnboarding\Validator\AvailableForEditFormValidator;
use STOnboarding\Validator\OnboardingFormExistsValidator;
use STOnboarding\Validator\UpdateCompanySettingsValidator;

class OnboardingFormController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ValidationApiException
     */
    public function getFormAction(): array
    {
        $externalId = $this->getApiParam('id');

        /** @var OnboardingFormExistsValidator $validator */
        $validator = $this->getServiceManager()->get(OnboardingFormExistsValidator::class);
        $validator->setInstance([
            'external_id' => $externalId,
        ]);
        $this->validate($validator);

        $form = $this->onboarding()->onboardingFormSelector()->getFormByExternalId($externalId);

        return [
            'form' => $form->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundApiException
     */
    public function updateFormAction(): array
    {
        $externalId = $this->getApiParam('id');
        $companyName = $this->getApiParam('company_name');
        $companyLogo = $this->getApiParam('company_logo');

        /** @var UpdateCompanySettingsValidator $validator */
        $validator = $this->getServiceManager()->get(UpdateCompanySettingsValidator::class);
        $validator->setInstance([
            'external_id' => $externalId,
            'company_name' => $companyName,
            'company_logo' => $companyLogo,
        ]);
        $this->validate($validator);

        $this->onboarding()->onboardingFormSaver()->updateCompanySettings($externalId, $companyName, $companyLogo);

        return [
            'form' => $this->onboarding()->onboardingFormSelector()->getFormByExternalId($externalId)->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     */
    public function submitFormAction(): array
    {
        $externalId = $this->getApiParam('id');

        /** @var AvailableForEditFormValidator $validator */
        $validator = $this->getServiceManager()->get(AvailableForEditFormValidator::class);
        $validator->setInstance([
            'external_id' => $externalId
        ]);
        $this->validate($validator);

        $this->onboarding()->onboardingFormSaver()->submitForm($externalId);

        return [
            'form' => $this->onboarding()->onboardingFormSelector()->getFormByExternalId($externalId)->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundApiException
     */
    public function needHelpAction(): array
    {
        $externalId = $this->getApiParam('id');

        /** @var AvailableForEditFormValidator $validator */
        $validator = $this->getServiceManager()->get(OnboardingFormExistsValidator::class);
        $validator->setInstance([
            'external_id' => $externalId
        ]);
        $this->validate($validator);

        $onboardingForm = $this->onboarding()->onboardingFormSelector()->getFormByExternalId($externalId);
        $this->onboarding()->onboardingNotificator()->notifyAboutNeedHelp($onboardingForm);

        return [
            'form' => $onboardingForm->toArray()
        ];
    }
}
