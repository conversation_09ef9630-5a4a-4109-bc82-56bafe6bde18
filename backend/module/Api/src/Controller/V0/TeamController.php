<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use STCompany\Entity\Role;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class TeamController extends BaseController
{
    use BaseHydratorTrait;

    /**
     *
     * @return array
     */
    public function getTeamsAction(): array
    {
        return [
            'teams' => $this->company()->team()->getTeams($this->company->getId())->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getTeamAction(): array
    {
        $teamId = (int) $this->getApiParam('team_id');
        return [
            'team' => $this->company()->team()->getTeam($this->company->getId(), $teamId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function saveTeamAction(): array
    {
        $teamId = (int) $this->getApiParam('team_id');
        $team = $this->hydrate([
            'name' => $this->getApiParam('name'),
            'company_id' => $this->company->getId(),
        ], \STCompany\Entity\Team::class);

        if ($teamId > 0) {
            // call to check access to team
            $this->company()->team()->getTeam($this->company->getId(), $teamId);
            $team->setId($teamId);
        }

        $validator = $this->getServiceManager()->get(\STCompany\Validator\TeamValidator::class);
        $validator->setInstance($team);
        $validator->validate();
        if ($validator->hasError()) {
            throw new \STApi\Entity\Exception\ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $teamId = $this->company()->team()->saveTeam($team);

        return [
            'team' => $this->company()->team()->getTeam($this->company->getId(), $teamId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function bulkSaveUsersAction(): array
    {
        $userIds = (array) $this->getApiParam('user_ids');
        $teamId = (int) $this->getApiParam('team_id');

        //check user id's
        $companyUserIds = $this->company()->user()->filterCompanyUsers($this->company->getId(), $userIds);
        //check team
        $this->company()->team()->getTeam($this->company->getId(), $teamId);

        $this->company()->team()->bulkSaveUsers(
            $this->company->getId(),
            $companyUserIds,
            $teamId,
        );

        return [
            'team' => $this->company()->team()->getTeam($this->company->getId(), $teamId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function deleteTeamAction(): array
    {
        $teamId = (int) $this->getApiParam('team_id');
        // call to check access to team
        $team = $this->company()->team()->getTeam($this->company->getId(), $teamId);
        $this->company()->team()->deleteTeam($this->company->getId(), $team->getId());
        return [
            'deleted' => true,
        ];
    }
}
