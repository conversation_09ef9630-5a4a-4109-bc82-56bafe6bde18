<?php

declare(strict_types=1);

namespace STEms\Validator;

use Lam<PERSON>\Validator\LessThan;
use STLib\Validator\Validator;

class DataSetExamplesSearchValidator extends Validator
{
    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run(): void
    {
        /** @var array $dataSetExamplesSearch */
        $dataSetExamplesSearch = $this->getInstance();

        if (empty($dataSetExamplesSearch['examples_limit'])) {
            $this->addError('examples_limit', 'Data set examples limit is required');
        }

        if (!(new LessThan(['max' => 300]))->isValid($dataSetExamplesSearch['examples_limit'])) {
            $this->addError('examples_limit', 'Data set examples limit must be less than 300');
        }
    }
}
