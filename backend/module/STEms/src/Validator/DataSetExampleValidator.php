<?php

declare(strict_types=1);

namespace STEms\Validator;

use STEms\Entity\DataSetExample;
use STLib\Validator\Validator;

class DataSetExampleValidator extends Validator
{
    /**
     *
     * @see \STLib\Validator\ValidatorInterface::run()
     */
    public function run(): void
    {
        /** @var \STEms\Entity\DataSetExample $dataSetExample */
        $dataSetExample = $this->getInstance();

        if ($this->hasCheck('text')) {
            $textValidator = new \Laminas\Validator\StringLength([
                'min' => \STEms\Service\EmsDataSetExampleService::EXAMPLE_MIN_LENGTH,
                'max' => 4096,
            ]);
            if (!$textValidator->isValid($dataSetExample->getText())) {
                $this->addError('name', 'Data set example text length must be between ' . \STEms\Service\EmsDataSetExampleService::EXAMPLE_MIN_LENGTH . ' and 4096 characters');
            }
        }

        if ($this->hasCheck('status')) {
            $statusValidator = new \Laminas\Validator\InArray([
                'haystack' => [
                    \STEms\Data\EmsDataSetExamplesTable::STATUS_UNSORTED,
                    \STEms\Data\EmsDataSetExamplesTable::STATUS_CONFIRMED,
                    \STEms\Data\EmsDataSetExamplesTable::STATUS_NEUTRAL,
                ],
                'strict' => true,
            ]);
            if (!$statusValidator->isValid($dataSetExample->getStatus())) {
                $this->addError('status', 'Invalid status value');
            }
        }
    }
}
