<?php

declare(strict_types=1);

namespace STEms\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;
use STEms\Service\EmsDataSetExampleService;
use STEms\Service\EmsDataSetService;

class Ems extends AbstractPlugin
{
    /**
     *
     * @return EmsDataSetExampleService
     */
    public function dataSetExample(): EmsDataSetExampleService
    {
        return $this->getController()->getServiceManager()->get(EmsDataSetExampleService::class);
    }

    /**
     *
     * @return EmsDataSetService
     */
    public function dataSet(): EmsDataSetService
    {
        return $this->getController()->getServiceManager()->get(EmsDataSetService::class);
    }
}
