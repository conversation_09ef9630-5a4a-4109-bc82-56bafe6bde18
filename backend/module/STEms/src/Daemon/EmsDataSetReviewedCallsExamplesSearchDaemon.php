<?php

declare(strict_types=1);

namespace STEms\Daemon;

use STCall\Service\Precalculation\CallPrecalculationService;
use STCompany\Entity\Company;
use STCompany\Entity\Event\Event;
use STCompany\Service\CompanyService;
use STCompany\Service\EventService;
use STEms\Entity\DataSet;
use STEms\Service\EmsDataSetExampleService;
use STEms\Service\EmsDataSetService;

class EmsDataSetReviewedCallsExamplesSearchDaemon extends AbstractEmsDataSetExamplesSearchDaemon
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    public const QUEUE = 'ems-data-set-reviewed-calls-examples-search';
    public const QUEUE_ERROR = 'ems-data-set-reviewed-calls-examples-search-error';
    public const EXAMPLES_LIMIT = 300;

    /**
     * @param string $message
     * @return void
     * @throws \JsonException
     * @throws \STApi\Entity\Exception\NotFoundApiException
     */
    public function handle(string $message): void
    {
        $data = json_decode($message, false, 512, JSON_THROW_ON_ERROR);
        $dataSetId = $data->data_set_id;
        $examplesLimit = (int) $data->examples_limit;

        /** @var EmsDataSetService $emsDataSetService */
        $emsDataSetService = $this->params()->offsetGet(EmsDataSetService::class);
        /** @var EmsDataSetExampleService $emsDataSetExamplesService */
        $emsDataSetExamplesService = $this->params()->offsetGet(EmsDataSetExampleService::class);
        /** @var CompanyService $companyService */
        $companyService = $this->params()->offsetGet(CompanyService::class);
        /** @var EventService $eventService */
        $eventService = $this->params()->offsetGet(EventService::class);

        $dataSet = $emsDataSetService->getDataSet($dataSetId);

        $company = $companyService->getCompany($dataSet->getCompanyId());
        $event = $eventService->getEvent($company->getId(), $dataSet->getEventId());
        $precalculatedCallEvents = $this->getPrecalculatedCallEvents($company, $event, $dataSet, $examplesLimit);

        $dataSetExamples = $emsDataSetExamplesService->makeDataSetExamplesFromEvents($precalculatedCallEvents, $dataSet->getDataSetId());
        $emsDataSetExamplesService->saveDataSetExamples($dataSetExamples);

        $this->sendEmailOnSearchFinished($dataSet);

        $dataSet->setReviewedCallsExamplesLastUpdate(new \Carbon\Carbon('now'));
        $emsDataSetService->saveDataSet($dataSet);
    }

    /**
     * @param Company $company
     * @param Event $event
     * @param DataSet $dataSet
     * @param int $limit
     * @return array
     */
    private function getPrecalculatedCallEvents(Company $company, Event $event, DataSet $dataSet, int $limit): array
    {
        /** @var CallPrecalculationService $callPrecalculationService */
        $callPrecalculationService = $this->params()->offsetGet(CallPrecalculationService::class);

        $suitableCallsIds = $callPrecalculationService->getReviewedCallCandidatesIdsWithEvent(
            $event,
            $dataSet->getReviewedCallsExamplesLastUpdate(),
            $limit
        );
        $suitableCallsIdsWithEventsMovedToNeutral = $callPrecalculationService->getReviewedCallCandidatesIdsWithEvent(
            $event,
            $dataSet->getReviewedCallsExamplesLastUpdate(),
            $limit,
            true
        );

        return array_merge(
            $callPrecalculationService->getPrecalculatedCallsEvents(
                $company,
                $event,
                $suitableCallsIds
            ),
            $callPrecalculationService->getPrecalculatedCallsEvents(
                $company,
                $event,
                $suitableCallsIdsWithEventsMovedToNeutral,
                true
            ),
        );
    }
}
