<?php

declare(strict_types=1);

namespace STEms\Service;

use PhpAmqpLib\Message\AMQPMessage;
use STApi\Entity\Exception\BadRequestApiException;
use STApi\Entity\Exception\NotFoundApiException;
use STEms\Daemon\EmsDataSetExportDaemon;
use STEms\Data\EmsDataSetsTable;
use STEms\Entity\DataSet;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STRabbit\Service\ProvidesRabbit;
use STRedis\Service\ProvidesRedis;

class EmsDataSetService
{
    use ProvidesRabbit;
    use BaseHydratorTrait;
    use ProvidesRedis;

    protected const MINIMAL_TIME_BETWEEN_EXAMPLES_SEARCH_RUN_IN_MINUTES = 10;

    /**
     *
     * @param EmsDataSetsTable $emsDataSetsTable
     */
    public function __construct(
        protected EmsDataSetsTable $emsDataSetsTable,
    ) {
    }

    /**
     * @param string $dataSetId
     * @param ?int $companyId
     * @return DataSet
     * @throws NotFoundApiException
     */
    public function getDataSet(string $dataSetId, ?int $companyId = null): DataSet
    {
        $dataSetData = $this->emsDataSetsTable->getDataSet($dataSetId, $companyId);

        /** @var DataSet $dataSet */
        $dataSet = $this->hydrate(
            $dataSetData,
            DataSet::class,
        );

        return $dataSet;
    }

    /**
     * @param int $eventId
     * @param int $companyId
     * @return DataSet|null
     */
    public function getDataSetByEventId(int $eventId, int $companyId): ?DataSet
    {
        $dataSetData = $this->emsDataSetsTable->getDataSetByEventId($eventId, $companyId);

        return $dataSetData !== null ? $this->hydrate($dataSetData, DataSet::class) : null;
    }

    /**
     * @param array $eventIds
     * @return array
     */
    public function getDataSetsIdsByEventsIds(array $eventIds): array
    {
        return $this->emsDataSetsTable->getDataSetsIdsByEventsIds($eventIds);
    }

    /**
     * @param DataSet $dataSet
     * @return void
     */
    public function saveDataSet(DataSet $dataSet): void
    {
        $this->emsDataSetsTable->saveDataSet($dataSet);
    }

    /**
     * @param string|array $dataSetIds
     * @param string $queue
     * @param int $examplesLimit
     * @return void
     * @throws BadRequestApiException
     * @throws \JsonException
     */
    public function addDataSetToSearchQueue(string|array $dataSetIds, string $queue, int $examplesLimit): void
    {
        $dataSetIds = is_array($dataSetIds) ? $dataSetIds : [$dataSetIds];

        $channel = $this->rabbit()->getChannel();

        foreach ($dataSetIds as $dataSetId) {
            $this->setSearchLastRunTime($queue, $dataSetId, static::MINIMAL_TIME_BETWEEN_EXAMPLES_SEARCH_RUN_IN_MINUTES);
            $message = new AMQPMessage(json_encode([
                'data_set_id' => $dataSetId,
                'examples_limit' => $examplesLimit,
            ], JSON_THROW_ON_ERROR));
            $channel->basic_publish($message, '', routing_key: $queue);
        }

        $channel->close();
    }

    /**
     * @param string $dataSetId
     * @return void
     * @throws \JsonException
     */
    public function sendMessageOnDataSetConfirmed(string $dataSetId): void
    {
        $channel = $this->rabbit()->getChannel();
        $message = new AMQPMessage(json_encode([
            'data_set_ids' => [$dataSetId],
            'statuses' => [
                \STEms\Data\EmsDataSetExamplesTable::STATUS_CONFIRMED,
                \STEms\Data\EmsDataSetExamplesTable::STATUS_NEUTRAL,
            ],
        ], JSON_THROW_ON_ERROR));
        $channel->basic_publish($message, exchange: '', routing_key: EmsDataSetExportDaemon::QUEUE);
        $channel->close();
    }

    /**
     * @param string $queue
     * @param string $dataSetId
     * @param int $expireInMinutes
     * @return void
     * @throws BadRequestApiException
     */
    private function setSearchLastRunTime(string $queue, string $dataSetId, int $expireInMinutes = 10): void
    {
        $key = $queue . ':' . $dataSetId;
        if ($this->redis()->get($key) !== null) {
            throw new BadRequestApiException('Minimal time between search running is ' . $expireInMinutes . ' minutes');
        }

        $this->redis()->set($key, (string) time(), 'EX', $expireInMinutes * 60);
    }
}
