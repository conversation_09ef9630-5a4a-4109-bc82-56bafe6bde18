<?php

declare(strict_types=1);

namespace STCompany\Validator;

use STLib\Validator\Validator;

final class UpdateSettingsValidator extends Validator
{
    private const string ERROR_WRONG_SETTING_NAME = 'Wrong setting name.';
    private const string ERROR_WRONG_SETTING_VALUE = 'Wrong setting value.';

    private static array $availableSettings = [
        'is_checklists_enabled',
        'is_summarization_enabled',
        'is_s3_integration_enabled',
        'is_manual_import_enabled'
    ];

    public function run(): void
    {
        ['setting_name' => $settingName, 'value' => $value] = $this->getInstance();

        if (!in_array($settingName, self::$availableSettings)) {
            $this->addError('setting_name', self::ERROR_WRONG_SETTING_NAME);

            return;
        }
        if (!is_bool($value)) {
            $this->addError('value', self::ERROR_WRONG_SETTING_VALUE);
        }
    }
}
