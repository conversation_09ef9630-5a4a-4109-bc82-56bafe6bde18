<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding;

use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use STOnboarding\Entity\OnboardingForm;

class CompanyBuilder
{
    public function __construct(private readonly CompanyService $companyService)
    {
    }

    public function createFromOnboardingForm(OnboardingForm $onboardingForm): Company
    {
        $company = new Company();
        $company->setName($onboardingForm->getCompanyName());
        $company->setAvatar($onboardingForm->getCompanyLogo());
        if (!empty($onboardingForm->getCallsSettings())) {
            $company->setLanguages($onboardingForm->getCallsSettings()['languages']);

            $company->setMinCallDurationForAutoAnalyze(
                $onboardingForm->getCallsSettings()['min_call_duration_for_auto_analyze']
            );
        } else {
            $company->setMinCallDurationForAutoAnalyze(0);
        }

        return $company;
    }

    public function setAwsData(Company $company): void
    {
        $company->setAwsS3BucketRegion(CompanyService::DEFAULT_AWS_REGION);
        $company->setAwsS3BucketName($this->companyService->getCommonAwsBucket($company));
        $company->setAwsS3BucketDir($this->companyService->createCompanyAwsBucketDir($company));
    }
}
