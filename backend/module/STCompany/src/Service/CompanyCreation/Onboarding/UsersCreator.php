<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding;

use Exception;
use STCompany\Entity\Company;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreation\UserBuilder;
use STCompany\Service\CompanyCreation\Onboarding\UsersCreation\UserInviter;
use STCompany\Service\TeamService;
use STCompany\Service\UserService;

class UsersCreator
{
    public function __construct(
        private readonly UserBuilder $userBuilder,
        private readonly TeamService $teamService,
        private readonly UserService $userService,
        private readonly UserInviter $userInviter,
    ) {
    }

    /**
     * @throws Exception
     */
    public function createUsers(Company $company, array $usersData, string $inviteLink): void
    {
        $team = $this->userBuilder->buildTeam($company);
        $this->teamService->saveTeam($team);

        $senderName = null;
        foreach ($usersData as $userData) {
            if (!$senderName) {
                $senderName = $userData['name'];
            }

            $user = $this->userBuilder->buildUser($userData['name'], $userData['email'], $company, $team);
            $this->userService->saveUser($user, $company->getId());

            if ($user->isFirstLogin()) {
                $this->userInviter->inviteNewUser($user, $company, $inviteLink, $senderName);
            } else {
                $this->userInviter->inviteExistedUser($user, $company, $senderName);
            }
        }
    }
}
