<?php

declare(strict_types=1);

namespace STCompany\Service;

use STCompany\Entity\UserCollection;

class UserService
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;
    use \STLib\Db\ProvidesTransaction;
    use \STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerServiceTrait;

    public const FAKE_NOT_EXISTED_TEAM_ID = -1;

    /**
     *
     * @var int|null
     */
    protected ?int $forcedRoleId = null;

    /**
     *
     * @var bool
     */
    protected bool $forcedReadOnlyPermissions = false;

    /**
     *
     * @var \STUser\Data\UsersTable
     */
    protected \STUser\Data\UsersTable $usersTable;

    /**
     *
     * @var \STCompany\Data\RolesTable
     */
    protected \STCompany\Data\RolesTable $rolesTable;

    /**
     *
     * @var \STCompany\Data\TeamsTable
     */
    protected \STCompany\Data\TeamsTable $teamsTable;

    /**
     *
     * @var \STCompany\Data\UsersCompaniesRolesTable
     */
    protected \STCompany\Data\UsersCompaniesRolesTable $usersCompaniesRolesTable;


    /**
     *
     * @var \STCompany\Data\UsersTeamsTable
     */
    protected \STCompany\Data\UsersTeamsTable $usersTeamsTable;

    /**
     *
     * @var \STCompany\Data\RolesPermissionsTable
     */
    protected \STCompany\Data\RolesPermissionsTable $rolesPermissionsTable;

    /**
     *
     * @var \STCompany\Data\PermissionsTable
     */
    protected \STCompany\Data\PermissionsTable $permissionsTable;

    /**
     *
     * @var \STCompany\Data\UsersCompaniesLanguagesTable
     */
    protected \STCompany\Data\UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable;

    /**
     *
     * @var \STCall\Data\CallsTable
     */
    protected \STCall\Data\CallsTable $callsTable;

    /**
     *
     * @param \STUser\Data\UsersTable $usersTable
     * @param \STCompany\Data\RolesTable $rolesTable
     * @param \STCompany\Data\TeamsTable $teamsTable
     * @param \STCompany\Data\UsersCompaniesRolesTable $usersCompaniesRolesTable
     * @param \STCompany\Data\UsersTeamsTable $usersTeamsTable
     * @param \STCompany\Data\RolesPermissionsTable $rolesPermissionsTable
     * @param \STCompany\Data\UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable
     * @param \STCall\Data\CallsTable $callsTable
     */
    public function __construct(
        \STUser\Data\UsersTable $usersTable,
        \STCompany\Data\RolesTable $rolesTable,
        \STCompany\Data\TeamsTable $teamsTable,
        \STCompany\Data\UsersCompaniesRolesTable $usersCompaniesRolesTable,
        \STCompany\Data\UsersTeamsTable $usersTeamsTable,
        \STCompany\Data\RolesPermissionsTable $rolesPermissionsTable,
        \STCompany\Data\PermissionsTable $permissionsTable,
        \STCompany\Data\UsersCompaniesLanguagesTable $usersCompaniesLanguagesTable,
        \STCall\Data\CallsTable $callsTable,
    ) {
        $this->usersTable = $usersTable;
        $this->rolesTable = $rolesTable;
        $this->teamsTable = $teamsTable;
        $this->usersCompaniesRolesTable = $usersCompaniesRolesTable;
        $this->usersTeamsTable = $usersTeamsTable;
        $this->rolesPermissionsTable = $rolesPermissionsTable;
        $this->permissionsTable = $permissionsTable;
        $this->usersCompaniesLanguagesTable = $usersCompaniesLanguagesTable;
        $this->callsTable = $callsTable;
    }

    /**
     *
     * @param int $companyId
     * @param array $teamIds
     * @param bool $withAdmins
     * @return \STCompany\Entity\UserCollection
     */
    public function getUsers(int $companyId, array $teamIds = [], bool $withAdmins = false): \STCompany\Entity\UserCollection
    {
        $rolesData = $this->rolesTable->getRoles($companyId);
        $usersData = $this->usersTable->getUsersByCompanyId($companyId, $teamIds);
        $teamsData = $this->teamsTable->getUsersTeams($companyId, userIds: null, teamIds: $teamIds);
        $languagesData = $this->usersCompaniesLanguagesTable->getUsersLanguages($companyId, $teamIds);

        $roleCollection = new \STCompany\Entity\RoleCollection();
        foreach ($rolesData as $roleData) {
            $roleCollection->add($this->hydrate((array) $roleData, \STCompany\Entity\Role::class));
        }

        $userCollection = new \STCompany\Entity\UserCollection();
        foreach ($usersData as $userData) {
            $user = $this->hydrate((array) $userData, \STCompany\Entity\User::class, withConstructor: true);
            if (isset($userData['role_id'])) {
                $user->setRole($roleCollection->offsetGet($userData['role_id']));
            }
            $userCollection->add($user);
        }

        foreach ($teamsData as $teamData) {
            $userCollection
                    ->offsetGet($teamData['user_id'])
                    ->getTeams()
                    ->add($this->hydrate((array) $teamData, \STCompany\Entity\Team::class));
        }

        foreach ($languagesData as $languageData) {
            $userCollection
                    ->offsetGet($languageData['user_id'])
                    ->addLanguage($languageData['language']);
        }

        if (!$withAdmins) {
            $userCollection->filter(function ($user) {
                return $user->getRole() instanceof \STCompany\Entity\Role && !$user->getRole()->isAdmin();
            });
        }

        return $userCollection;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param array $teamIds
     * @return \STCompany\Entity\User
     */
    public function getUser(int $companyId, int $userId, array $teamIds = []): \STCompany\Entity\User
    {
        $userData = $this->usersTable->getUserById($userId, $companyId, $teamIds)->current()->getArrayCopy();
        $teamsData = $this->teamsTable->getUserTeams($companyId, $userId);
        $baseRoleData = $this->rolesTable->getRoleByUserId($companyId, $userId);
        $roleData = is_null($this->forcedRoleId)
                ? $baseRoleData
                : $this->rolesTable->getRole($companyId, $this->forcedRoleId);
        $languagesData = $this->usersCompaniesLanguagesTable->getUserLanguages($companyId, $userId);

        /** @var \STCompany\Entity\User $user */
        $user = $this->hydrate($userData, \STCompany\Entity\User::class, withConstructor: true);

        /** @var \STCompany\Entity\Role $role */
        $role = $this->hydrate((array) $roleData->current(), \STCompany\Entity\Role::class);
        $baseRole = $this->hydrate((array) $baseRoleData->current(), \STCompany\Entity\Role::class);
        $permissionCollection = new \STCompany\Entity\Permission\PermissionCollection();
        if ($role->isAdmin() || $role->isCompanyAdmin()) {
            $permissionsData = $this->permissionsTable->getPermissions();
        } elseif (empty($role->getId())) {
            $permissionsData = [];
        } else {
            $permissionsData = $this->rolesPermissionsTable->getPermissionsByRoleIds($role->getId());
        }
        foreach ($permissionsData as $permissionData) {
            if ($role->isAdmin() || $role->isCompanyAdmin()) {
                $accessLevel = \STCompany\Data\PermissionsTable::WRITE_PERMISSION;
            } else {
                $accessLevel = $this->forcedReadOnlyPermissions ? \STCompany\Data\PermissionsTable::READ_PERMISSION : $permissionData['access_level'];
            }
            $userPermission = $this->hydrate([
                'id' => $permissionData['permission_id'],
                'name' => $permissionData['permission_name'],
                'system_name' => $permissionData['system_name'],
                'access_level' => $accessLevel,
            ], \STCompany\Entity\Permission\UserPermission::class);
            $permissionCollection->add($userPermission);
        }
        $role->setPermissions($permissionCollection);
        $user
                ->setRole($role)
                ->setBaseRole($baseRole);

        foreach ($teamsData as $teamData) {
            $user
                    ->getTeams()
                    ->add($this->hydrate((array) $teamData, \STCompany\Entity\Team::class));
        }

        $user->setLanguages(array_column($languagesData->toArray(), 'language'));

        return $user;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return array
     *
     * @description Empty array if team ids filtering is not needed
     */
    public function getUserTeamIds(int $companyId, int $userId): array
    {
        $user = $this->getUser($companyId, $userId);
        $teamIds = [];
        if (!$user->getBaseRole()->isAdmin() && !$user->getBaseRole()->isCompanySupervisor() && !$user->getBaseRole()->isCompanyAdmin()) {
            $teamIds = array_column($user->getTeams()->toArray(), 'id');
            // response for admin is empty array
            // response for not admin user should be not empty array
            // if user is not admin, but doesn't have teams, then add not existed team id to result
            if (count($teamIds) === 0) {
                $teamIds[] = static::FAKE_NOT_EXISTED_TEAM_ID;
            }
        }
        return $teamIds;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return bool
     */
    public function getCompanyUserActivity(int $companyId, int $userId): bool
    {
        return (bool) $this->usersCompaniesRolesTable->getActivity($companyId, $userId)->current()->is_active;
    }

    /**
     *
     * @param int $companyId
     * @param array $userTypes
     * @param array $teamIds
     * @return \STCompany\Entity\UserCollection
     */
    public function getUsersByRoleType(int $companyId, array $userTypes, array $teamIds = []): \STCompany\Entity\UserCollection
    {
        $usersData = $this->usersTable->getUsersByRoleType($companyId, $userTypes, $teamIds);
        $userCollection = new \STCompany\Entity\UserCollection();

        foreach ($usersData as $userData) {
            $user = $this->hydrate((array) $userData, \STCompany\Entity\User::class);
            $userCollection->add($user);
        }

        return $userCollection;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @param \STLib\Paginator\Params $paginationParams
     * @return \STLib\Paginator\Paginator
     */
    public function getAgentsWithPagination(
        \STCompany\Entity\Company $company,
        \STLib\Paginator\Params $paginationParams,
    ): \STLib\Paginator\Paginator {
        $paginator = $this->usersTable->getAgentsWithPagination($company->getId(), $paginationParams);

        $userCollection = new UserCollection();
        foreach ($paginator as $userData) {
            $user = $this->hydrate((array) $userData, \STCompany\Entity\Search\User::class, withConstructor: true);
            $userCollection->add($user);
        }
        $userIds = array_column($userCollection->toArray(), 'id');

        $todayCalls = $this->callsTable->getAgentsTodayCalls($company);
        foreach ($todayCalls as $todayCall) {
            $userId = (int) $todayCall['user_id'];
            $count = (int) $todayCall['calls_count'];
            if ($userCollection->offsetExists($userId)) {
                $userCollection
                        ->offsetGet($userId)
                        ->setCallsCount($count);
            }
        }

        $teamsData = $this->teamsTable->getUsersTeams($company->getId(), $userIds);
        foreach ($teamsData as $teamData) {
            $userCollection
                    ->offsetGet($teamData['user_id'])
                    ->getTeams()
                    ->add($this->hydrate((array) $teamData, \STCompany\Entity\Team::class));
        }

        $paginator->setItems($userCollection);
        return $paginator;
    }

    /**
     *
     * @param int $companyId
     * @param array $userIds
     * @return array
     */
    public function filterCompanyUsers(int $companyId, array $userIds): array
    {
        $users = $this->usersTable->getUsersByUserIds($companyId, $userIds);
        return array_column($users->toArray(), 'user_id');
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param bool $isActive
     * @return int
     */
    public function changeCompanyUserActivity(int $companyId, int $userId, bool $isActive): int
    {
        return $this->usersCompaniesRolesTable->changeActivity($companyId, $userId, $isActive);
    }

    /**
     *
     * @param int $roleId
     * @return UserService
     */
    public function setForcedRoleId(int $roleId): UserService
    {
        $this->forcedRoleId = $roleId;
        return $this;
    }

    /**
     *
     * @param bool $forcedReadOnlyPermissions
     * @return UserService
     */
    public function setForcedReadOnlyPermissions(bool $forcedReadOnlyPermissions): UserService
    {
        $this->forcedReadOnlyPermissions = $forcedReadOnlyPermissions;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @param array $userIds
     * @return void
     */
    public function setAutoAnalyzeForUsers(\STCompany\Entity\Company $company, array $userIds): void
    {
        $this->beginTransaction();

        $this->usersCompaniesRolesTable->setAutoAnalyzeForCompanyUsers($company->getId(), [], false);

        if (!empty($userIds)) {
            $this->usersCompaniesRolesTable->setAutoAnalyzeForCompanyUsers($company->getId(), $userIds, true);
        }

        $this->commit();
    }

    /**
     *
     * @param \STCompany\Entity\User $user
     * @param int $companyId
     * @return int
     * @throws \Exception
     */
    public function saveUser(\STCompany\Entity\User $user, int $companyId): int
    {
        $this->beginTransaction();
        try {
            $userId = $this->usersTable->saveUser($user, updatePassword: (bool) $user->getPassword());

            $this->usersCompaniesRolesTable->saveUserData($userId, $companyId, $user->getRole()->getId(), $user->getCompanyUserId(), $user->isAutoAnalyzeCalls());

            $teams = $this->teamsTable->getTeams($companyId, $userId);
            $teamIds = array_column($teams->toArray(), 'team_id');
            $this->usersTeamsTable->delete($userId, $teamIds);
            $this->usersTeamsTable->bulkInsert($userId, $user->getTeams());

            $this->usersCompaniesLanguagesTable->deleteUserLanguages($companyId, $userId);
            $this->usersCompaniesLanguagesTable->saveUserLanguages($companyId, $user);

            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }

        return $userId;
    }

    /**
     *
     * @param int $userId
     * @param int $companyId
     * @return bool
     * @throws \Exception
     */
    public function deleteUser(int $userId, int $companyId): bool
    {
        $this->beginTransaction();
        try {
            $teams = $this->teamsTable->getTeams($companyId, $userId);
            $teamIds = array_column($teams->toArray(), 'team_id');
            $this->usersTeamsTable->delete($userId, $teamIds);
            $this->usersCompaniesRolesTable->deleteUserRole($userId, $companyId);
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }

        return true;
    }
}
