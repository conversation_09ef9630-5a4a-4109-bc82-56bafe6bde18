<?php

declare(strict_types=1);

namespace STCompany\Entity;

class Team
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var int
     */
    protected ?int $id = null;

    /**
     *
     * @var string
     */
    protected ?string $name = null;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var string|null
     */
    protected ?string $companyTeamId;

    /**
     *
     * @var ?UserCollection
     */
    protected ?UserCollection $users = null;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->users = new UserCollection();
    }

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string|null
     */
    public function getCompanyTeamId(): ?string
    {
        return $this->companyTeamId;
    }

    /**
     *
     * @return UserCollection|null
     */
    public function getUsers(): ?UserCollection
    {
        return $this->users;
    }

    /**
     *
     * @param int|null $id
     * @return Team
     */
    public function setId(?int $id): Team
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string|null $name
     * @return Team
     */
    public function setName(?string $name): Team
    {
        $this->name = $name;
        return $this;
    }

    /**
     *
     * @param int $companyId
     * @return Team
     */
    public function setCompanyId(int $companyId): Team
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param string|null $companyTeamId
     * @return Team
     */
    public function setCompanyTeamId(?string $companyTeamId): Team
    {
        $this->companyTeamId = $companyTeamId;
        return $this;
    }

    /**
     *
     * @param UserCollection|null $users
     * @return Team
     */
    public function setUsers(?UserCollection $users): Team
    {
        $this->users = $users;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['users'] = $this->getUsers() instanceof \STCompany\Entity\UserCollection ? $this->getUsers()->toArray() : null;
        unset($result['company_id']);
        unset($result['company_team_id']);
        return $result;
    }
}
