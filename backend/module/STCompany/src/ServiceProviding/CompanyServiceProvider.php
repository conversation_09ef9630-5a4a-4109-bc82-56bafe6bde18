<?php

declare(strict_types=1);

namespace STCompany\ServiceProviding;

use Exception;
use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Service\Export\Interfaces\CompaniesToExportSelectorInterface as CallCompaniesToExportSelectorInterface;
use STCall\Service\Interfaces\CompanySaverInterface as CallCompanySaverInterface;
use STCall\Service\Interfaces\CompanySelectorInterface as CallCompanySelectorInterface;
use STCall\Service\Interfaces\RolesSelectorInterface as CallRolesSelectorInterface;
use STCall\Service\Interfaces\TeamSelectorInterface;
use STCall\Service\Interfaces\UserTeamIdsSelectorInterface as CallUserTeamIdsSelectorInterface;
use STCompany\Entity\Company;
use STCompany\Entity\TeamCollection;
use STCompany\Service\AlgoEvents\EventsAlgoEventsSelectorService;
use STCompany\Service\CompanyCreation\Onboarding\CompanyCreator;
use STCompany\Service\CompanyService;
use STCompany\Service\RoleService;
use STCompany\Service\TeamService;
use STCompany\Service\UserService;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Interfaces\CompanyCreatorInterface as OnboardingCompanyCreatorInterface;
use STOnboarding\Service\Interfaces\EventsAlgoEventsSelectorInterface;

readonly class CompanyServiceProvider implements
    EventsAlgoEventsSelectorInterface,
    TeamSelectorInterface,
    OnboardingCompanyCreatorInterface,
    CallCompanySelectorInterface,
    CallCompaniesToExportSelectorInterface,
    CallCompanySaverInterface,
    CallUserTeamIdsSelectorInterface,
    CallRolesSelectorInterface
{
    public function __construct(
        private EventsAlgoEventsSelectorService $eventsAlgoEventsSelector,
        private CompanyCreator $companyCreator,
        private CompanyService $companyService,
        private UserService $userService,
        private TeamService $teamService,
        private RoleService $roleService,
    ) {
    }

    public function filterMostPopularAlgoEvents(array $eventNames, int $limit): array
    {
        return $this->eventsAlgoEventsSelector->filterMostPopularAlgoEventNames($eventNames, $limit);
    }

    /**
     * @throws Exception
     */
    public function createFromOnboardingForm(OnboardingForm $onboardingForm): Company
    {
        return $this->companyCreator->createFromOnboardingForm($onboardingForm);
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function getCompany(int $companyId, bool $withoutRelationships = false): Company
    {
        return $this->companyService->getCompany($companyId, withoutRelationships: $withoutRelationships);
    }

    public function getCompaniesIds(): array
    {
        return $this->companyService->getCompaniesIdsToExport();
    }

    /**
     * @throws Exception
     */
    public function saveCompany(Company $company): int
    {
        return $this->companyService->saveCompany($company);
    }

    public function getUserTeamIds(int $companyId, int $userId): array
    {
        return $this->userService->getUserTeamIds($companyId, $userId);
    }

    public function getTeams(int $companyId, ?int $userId = null): TeamCollection
    {
        return $this->teamService->getTeams($companyId, $userId);
    }

    public function getRoleIds(int $companyId): array
    {
        return $this->roleService->getRoleIds($companyId);
    }
}
