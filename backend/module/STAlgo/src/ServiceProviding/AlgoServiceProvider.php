<?php

declare(strict_types=1);

namespace STAlgo\ServiceProviding;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ReflectionException;
use STAlgo\Entity\AlgoApi;
use STAlgo\Service\AlgoApiService;
use STAlgo\Service\Client;
use STAlgo\Service\CallSummarization\CallSummarizationGetter;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Entity\Call;
use STCall\Entity\CallSummarization;
use STCall\Service\CallAnalysis\Summarization\Interfaces\CallSummarizationGetterInterface;
use STCompany\Entity\Company;
use STCompany\Service\Interfaces\AlgoApiSelectorInterface;
use STTranslation\Service\Drivers\Interfaces\AlgoClientInterface as TranslationAlgoClientInterface;

final readonly class AlgoServiceProvider implements AlgoApiSelectorInterface, TranslationAlgoClientInterface, CallSummarizationGetterInterface
{
    public function __construct(
        private AlgoApiService $algoApiService,
        private Client $client,
        private CallSummarizationGetter $callSummarizationGetter,
    ) {
    }

    /**
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getAlgoApi(int $algoApiId): AlgoApi
    {
        return $this->algoApiService->getAlgoApi($algoApiId);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function translate(array $params): array
    {
        return $this->client->translate($params);
    }

    public function detectLanguageByText(string $text): array
    {
        return $this->client->detectLanguageByText($text);
    }

    public function detectLanguage(string $content): array
    {
        return $this->client->detectLanguage($content);
    }

    /**
     * @param Company $company
     * @param Call $call
     * @return CallSummarization
     * @throws GuzzleException
     * @throws JsonException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     */
    public function getSummarization(Company $company, Call $call): CallSummarization
    {
        return $this->callSummarizationGetter->getSummarization($company, $call);
    }
}
