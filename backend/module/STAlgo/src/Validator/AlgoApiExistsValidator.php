<?php

namespace STAlgo\Validator;

use STAlgo\Data\AlgoApisTable;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Validator\Validator;

class AlgoApiExistsValidator extends Validator
{
    public function __construct(private readonly AlgoApisTable $algoApisTable)
    {
    }

    protected const string ERROR_ALGO_API_DOES_NOT_EXIST = 'The algo api does not exists.';

    public function run(): void
    {
        /** @var array $input */
        $algoApiId = $this->getInstance();

        try {
            $this->algoApisTable->getApis($algoApiId);
        } catch (NotFoundApiException) {
            $this->addError('algo_api_id', self::ERROR_ALGO_API_DOES_NOT_EXIST);

            return;
        }
    }
}
