<?php

declare(strict_types=1);

namespace STAlgo\Data;

use Exception;
use <PERSON><PERSON>\Db\ResultSet\ResultSetInterface;
use <PERSON>inas\Db\Sql\Select;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use <PERSON><PERSON>pi\Entity\Exception\NotFoundApiException;
use STLib\Db\AbstractTable;

class AlgoApisTable extends AbstractTable
{
    public static string $table = 'algo_apis';

    /**
     *
     * @param null|int|int[] $apiId
     * @return ResultSetInterface
     * @throws NotFoundApiException
     */
    public function getApis(int|array|null $apiId = null): ResultSetInterface
    {
        $select = $this->getSelectNotDeleted();

        if (!is_null($apiId)) {
            $select->where(['algo_api_id' => $apiId]);
        }

        $result = $this->tableGateway->selectWith($select);
        if (is_int($apiId) && $result->count() === 0) {
            throw new NotFoundApiException('Algo API not found');
        }
        return $result;
    }

    /**
     *
     * @return ResultSetInterface
     */
    public function getDefaultApiIds(): ResultSetInterface
    {
        $select = $this->getSelectNotDeleted();
        $select
            ->columns(['algo_api_id'])
            ->where([
                'is_default_api' => 1
            ]);
        return $this->tableGateway->selectWith($select);
    }

    /**
     *
     * @param AlgoApi $algoApi
     * @return int
     */
    public function saveAlgoApi(AlgoApi $algoApi): int
    {
        $data = [
            'path' => $algoApi->getPath(),
            'industry_id' => $algoApi->getIndustryId(),
            'is_default_api' => $algoApi->isDefaultApi(),
            'analyze_method' => $algoApi->getAnalyzeMethod(),
        ];

        if ($algoApi->getId() > 0) {
            $this->tableGateway->update($data, [
                'algo_api_id' => $algoApi->getId(),
            ]);
        } else {
            $this->tableGateway->insert($data);
            $algoApi->setId((int) $this->tableGateway->lastInsertValue);
        }

        return $algoApi->getId();
    }

    /**
     * @throws Exception
     */
    public function saveAlgoApis(AlgoApiCollection $algoApiCollection): void
    {
        $dataToSave = [];
        foreach ($algoApiCollection as $algoApi) {
            $dataToSave[] = [
                'algo_api_id' => $algoApi->getId(),
                'path' => $algoApi->getPath(),
                'industry_id' => $algoApi->getIndustryId(),
                'is_default_api' => $algoApi->isDefaultApi(),
                'analyze_method' => $algoApi->getAnalyzeMethod(),
                'name' => $algoApi->getName(),
                'description' => $algoApi->getDescription(),
                'is_deleted' => $algoApi->getIsDeleted(),
            ];
        }

        $updatedColumns = [
            'industry_id',
            'is_default_api',
            'name',
            'description',
            'is_deleted',
        ];

        $this->multiInsertOrUpdate($dataToSave, $updatedColumns);
    }

    public function searchPromptByPath(string $path): ResultSetInterface
    {
        $select = $this->getSelectNotDeleted();
        $select
            ->columns(['algo_api_id'])
            ->where(['analyze_method' => '/promptV1'])
            ->where->like('path', '%' . $path . '%');

        return $this->tableGateway->selectWith($select);
    }

    public function getCompanyNerAlgoApis(int $companyId): ResultSetInterface
    {
        $select = $this->getSelectNotDeleted();
        $select->quantifier('DISTINCT')
            ->join(
                [
                    'caa' => 'companies_algo_apis',
                ],
                'caa.algo_api_id = ' . self::$table . '.algo_api_id',
                [],
                Select::JOIN_LEFT
            )
            ->join(
                [
                    'aai' => 'algo_apis_industries'
                ],
                'aai.algo_api_id = ' . self::$table . '.algo_api_id',
                [],
                Select::JOIN_LEFT
            )
            ->join(
                [
                    'ci' => 'companies_industries'
                ],
                'ci.industry_id = aai.industry_id',
                [],
                Select::JOIN_LEFT
            )
            ->order(self::$table . '.algo_api_id');

        $select->where->nest()
            ->equalTo('caa.company_id', $companyId)
            ->or
            ->equalTo('ci.company_id', $companyId)
            ->unnest()
            ->equalTo('analyze_method', CompaniesAlgoApisTable::NER_ANALYZE_METHOD);

        return $this->tableGateway->selectWith($select);
    }

    private function getSelectNotDeleted(): Select
    {
        return $this->tableGateway->getSql()
            ->select()
            ->where([self::$table . '.is_deleted' => 0]);
    }
}
