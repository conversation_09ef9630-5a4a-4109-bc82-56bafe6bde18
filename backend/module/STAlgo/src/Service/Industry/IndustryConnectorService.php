<?php

namespace STAlgo\Service\Industry;

use STAlgo\Data\AlgoApiIndustriesTable;
use STAlgo\Entity\Industry\Industry;
use STApi\Entity\Exception\NotFoundApiException;

class IndustryConnectorService
{
    public function __construct(private readonly AlgoApiIndustriesTable $algoApiIndustryTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function connect(int $industryId, int $algoApiId): Industry
    {
        $industry = new Industry();
        $industry->setId($industryId);
        $industry->setAlgoAPiId($algoApiId);

        $this->algoApiIndustryTable->saveIndustry($industry);

        return $this->algoApiIndustryTable->getIndustry($industry->getId(), $industry->getAlgoApiId());
    }

    public function disconnect(int $industryId, int $algoApiId): void
    {
        $this->algoApiIndustryTable->deleteIndustry($industryId, $algoApiId);
    }
}
