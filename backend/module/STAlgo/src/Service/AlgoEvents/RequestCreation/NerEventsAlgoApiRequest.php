<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoEvents\RequestCreation;

use STAlgo\Entity\AlgoApi;
use STAlgo\Service\ParamsBuilding\RequestParams;

class NerEventsAlgoApiRequest implements EventsAlgoApiRequestInterface
{
    public function __construct(
        private readonly AlgoApi $algoApi,
        private readonly string $apiUrl,
        private readonly RequestParams $params,
        private readonly ?string $analyzeMethod = null,
        private readonly ?string $env = null,
    ) {
    }

    public function getEnv(): ?string
    {
        return $this->env;
    }

    public function getUrl(): string
    {
        $analyzeMethod = $this->analyzeMethod ?? $this->algoApi->getAnalyzeMethod();

        return $this->apiUrl . $this->algoApi->getPath() . $analyzeMethod;
    }

    public function getParams(): array
    {
        return $this->params->toArray();
    }

    public function getAlgoApiId(): int
    {
        return $this->algoApi->getId();
    }

    public function getIndustryId(): int
    {
        return $this->algoApi->getIndustryId();
    }

    public function getAlgoApiPath(): string
    {
        return $this->algoApi->getPath();
    }
}
