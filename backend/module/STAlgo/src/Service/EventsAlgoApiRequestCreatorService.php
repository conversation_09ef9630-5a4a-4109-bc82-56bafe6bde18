<?php

declare(strict_types=1);

namespace STAlgo\Service;

use ReflectionException;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequestCreator;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequestCreator;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Entity\Call;
use STCompany\Entity\Company;

class EventsAlgoApiRequestCreatorService
{
    public function __construct(
        private readonly RequestParamsBuilder $paramsBuilder,
        private readonly NerEventsAlgoApiRequestCreator $nerRequestCreator,
        private readonly LlmEventsAlgoApiRequestCreator $llmRequestCreator,
    ) {
    }

    /**
     * @param Company $company
     * @param Call $call
     * @param array|null $algoApiParams
     * @param bool $withoutLlm
     * @return array
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function create(Company $company, Call $call, ?array $algoApiParams = null, bool $withoutLlm = false): array
    {
        $requestParams = $this->paramsBuilder->build($company, $call);

        $requests = $this->nerRequestCreator->create($company, $requestParams, $algoApiParams);

        if ($withoutLlm) {
            return $requests;
        }

        $llmRequest = $this->llmRequestCreator->create($company, clone $requestParams);
        if ($llmRequest) {
            $requests[] = $llmRequest;
        }

        return $requests;
    }
}
