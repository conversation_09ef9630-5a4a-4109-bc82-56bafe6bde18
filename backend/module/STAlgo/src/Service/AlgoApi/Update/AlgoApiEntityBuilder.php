<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoApi\Update;

use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STLib\Mvc\Hydrator\BaseHydrator;

class AlgoApiEntityBuilder
{
    public function __construct(private BaseHydrator $hydrator)
    {
    }

    public function buildCollection(array $data): AlgoApiCollection
    {
        $expandedData = $this->expandData($data['models']);

        $algoApiCollection = new AlgoApiCollection();
        foreach ($expandedData as $row) {
            $algoApi = $this->createEntity();
            $this->hydrator->hydrate($row, $algoApi);
            $algoApiCollection->add($algoApi, $algoApi->getPath());
        }

        return $algoApiCollection;
    }

    private function expandData(array $data): array
    {
        $result = [];
        foreach ($data as $row) {
            $model = $row['model'];
            foreach ($row['versions'] as $version) {
                $result[] = [
                    'path' => '/api/' . $model . '/' . $version,
                    'industry_id' => 0,
                    'analyze_method' => '',
                    'name' => $row['name'],
                    'description' => $row['description'],
                    'is_deleted' => false,
                ];
            }
        }

        return $result;
    }

    protected function createEntity(): AlgoApi
    {
        return new AlgoApi();
    }
}
