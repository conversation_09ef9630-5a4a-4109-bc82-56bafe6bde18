<?php

declare(strict_types=1);

namespace STAlgo\Entity;

use STLib\Mvc\Hydrator\BaseHydratorTrait;

class AlgoApi
{
    use BaseHydratorTrait;

    /**
     *
     * @var int|null
     */
    protected ?int $id = null;

    /**
     *
     * @var string
     */
    protected string $path;

    /**
     *
     * @var int
     */
    protected int $industryId;

    /**
     *
     * @var bool
     */
    protected bool $isDefaultApi = false;

    /**
     *
     * @var string
     */
    protected string $analyzeMethod;

    /**
     * @var string|null
     */
    protected ?string $name = null;

    /**
     * @var string|null
     */
    protected ?string $description = null;

    /**
     *
     * @var bool
     */
    protected bool $isDeleted;

    /**
     *
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     *
     * @return string
     */
    public function getPath(): string
    {
        return $this->path;
    }

    /**
     *
     * @return int
     */
    public function getIndustryId(): int
    {
        return $this->industryId;
    }

    /**
     *
     * @return string
     */
    public function getAnalyzeMethod(): string
    {
        return $this->analyzeMethod;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getIsDeleted(): bool
    {
        return $this->isDeleted;
    }

    /**
     *
     * @param int|null $id
     * @return AlgoApi
     */
    public function setId(?int $id): AlgoApi
    {
        $this->id = $id;
        return $this;
    }

    /**
     *
     * @param string $path
     * @return AlgoApi
     */
    public function setPath(string $path): AlgoApi
    {
        $this->path = $path;
        return $this;
    }

    /**
     *
     * @param int $industryId
     * @return AlgoApi
     */
    public function setIndustryId(int $industryId): AlgoApi
    {
        $this->industryId = $industryId;
        return $this;
    }

    /**
     *
     * @param string $analyzeMethod
     * @return AlgoApi
     */
    public function setAnalyzeMethod(string $analyzeMethod): AlgoApi
    {
        $this->analyzeMethod = $analyzeMethod;
        return $this;
    }

    public function setName(?string $name): AlgoApi
    {
        $this->name = $name;
        return $this;
    }

    public function setDescription(?string $description): AlgoApi
    {
        $this->description = $description;
        return $this;
    }

    public function setIsDeleted(bool $isDeleted): AlgoApi
    {
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     *
     * @param bool|null $isDefaultApi
     * @return AlgoApi|bool
     */
    public function isDefaultApi(?bool $isDefaultApi = null): AlgoApi|bool
    {
        if (is_null($isDefaultApi)) {
            return $this->isDefaultApi;
        }
        $this->isDefaultApi = $isDefaultApi;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        return $this->extract($this);
    }
}
