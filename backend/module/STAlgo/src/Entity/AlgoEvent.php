<?php

declare(strict_types=1);

namespace STAlgo\Entity;

class AlgoEvent
{
    /**
     *
     * @var string
     */
    protected string $algoEvent;

    /**
     *
     * @var int
     */
    protected int $algoApiId;

    /**
     *
     * @var string|null
     */
    protected ?string $hint;

    /**
     *
     * @var int
     */
    protected string $industryId;

    /**
     *
     * @var string
     */
    protected string $industryName;

    /**
     *
     * @return string
     */
    public function getAlgoEvent(): string
    {
        return $this->algoEvent;
    }

    /**
     *
     * @return int
     */
    public function getAlgoApiId(): int
    {
        return $this->algoApiId;
    }

    /**
     *
     * @return string|null
     */
    public function getHint(): ?string
    {
        return $this->hint;
    }

    /**
     *
     * @return string
     */
    public function getIndustryId(): string
    {
        return $this->industryId;
    }

    /**
     *
     * @return string
     */
    public function getIndustryName(): string
    {
        return $this->industryName;
    }

    /**
     *
     * @param string $algoEvent
     * @return AlgoEvent
     */
    public function setAlgoEvent(string $algoEvent): AlgoEvent
    {
        $this->algoEvent = $algoEvent;
        return $this;
    }

    /**
     *
     * @param int $algoApiId
     * @return AlgoEvent
     */
    public function setAlgoApiId(int $algoApiId): AlgoEvent
    {
        $this->algoApiId = $algoApiId;
        return $this;
    }

    /**
     *
     * @param string|null $hint
     * @return AlgoEvent
     */
    public function setHint(?string $hint): AlgoEvent
    {
        $this->hint = $hint;
        return $this;
    }

    /**
     *
     * @param string $industryId
     * @return AlgoEvent
     */
    public function setIndustryId(string $industryId): AlgoEvent
    {
        $this->industryId = $industryId;
        return $this;
    }

    /**
     *
     * @param string $industryName
     * @return AlgoEvent
     */
    public function setIndustryName(string $industryName): AlgoEvent
    {
        $this->industryName = $industryName;
        return $this;
    }
}
