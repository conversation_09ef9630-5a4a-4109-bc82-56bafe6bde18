<?php

declare(strict_types=1);

namespace STAlgo\Entity;

use Traversable;

/**
 * @extends Traversable<array-key,AlgoApi>
 * @method AlgoApi offsetGet(mixed $index): mixed
 */
class AlgoApiCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $algoApi
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $algoApi, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($algoApi instanceof AlgoApi)) {
            throw new \RuntimeException('Algo<PERSON><PERSON> must be an instace of "\STAlgo\Entity\AlgoApi"');
        }
        parent::add($algoApi, $key ?? $algoApi->getId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $algoApi) {
            $result[] = $algoApi->toArray();
        }
        return $result;
    }
}
