{"name": "robonote/robonote", "description": "Robonote.io", "type": "project", "keywords": ["robonote"], "require": {"php": "~8.3.14", "laminas/laminas-component-installer": "^3.0", "laminas/laminas-development-mode": "^3.2", "laminas/laminas-mvc": "^3.3.4", "laminas/laminas-db": "^2.17", "laminas/laminas-log": "^2.13.1", "laminas/laminas-cli": "^1.1.1", "laminas/laminas-mvc-i18n": "^1.2.0", "laminas/laminas-mvc-plugins": "^1.1.0", "laminas/laminas-mvc-middleware": "^2.0.0", "laminas/laminas-session": "^2.10.0", "laminas/laminas-di": "^3.2.2", "laminas/laminas-paginator": "^2.17", "predis/predis": "^2.0", "doctrine/migrations": "^3.5", "nesbot/carbon": "^3.9.0", "php-amqplib/php-amqplib": "^3.2", "airbrake/phpbrake": "^1.0.0", "smi2/phpclickhouse": "^1.4", "guzzlehttp/guzzle": "^7.5", "php-http/guzzle7-adapter": "^1.0", "robthree/twofactorauth": "^1.8.2", "php-ffmpeg/php-ffmpeg": "^1.0", "aws/aws-sdk-php": "^3.237", "stichoza/google-translate-php": "^5.2", "dompdf/dompdf": "^3.1", "mikehaertl/phpwkhtmltopdf": "^2.5", "phpoffice/phpspreadsheet": "^4.2.0", "sparkpost/sparkpost": "^2.3", "php-http/message": "^1.16", "php-http/message-factory": "^1.1", "guzzlehttp/psr7": "^2.6", "psr/http-message": "^1.1", "myclabs/deep-copy": "^1.11", "symfony/lock": "^7.2", "google/cloud-translate": "^1.19", "stolt/json-lines": "^4.0"}, "autoload": {"psr-4": {"tests\\": "tests"}}, "scripts": {"cs-check": "vendor/bin/phpcs", "cs-fix": "vendor/bin/phpcbf", "development-disable": "laminas-development-mode disable", "development-enable": "laminas-development-mode enable", "development-status": "laminas-development-mode status", "post-create-project-cmd": ["@development-enable", "php bin/update-gitignore.php", "php -r 'if (file_exists(\"bin/remove-package-artifacts.php\")) include \"bin/remove-package-artifacts.php\";'", "php -r 'if (file_exists(\"CHANGELOG.md\")) unlink(\"CHANGELOG.md\");'"], "serve": "php -S 0.0.0.0:8080 -t public", "test": "vendor/bin/phpunit", "static-analysis": "vendor/bin/psalm --shepherd --stats"}, "scripts-descriptions": {"cs-check": "Run coding standards checks.", "cs-fix": "Automatically fix coding standard issues.", "development-disable": "Disable development mode.", "development-enable": "Enable development mode.", "development-status": "Detail whether or not the application is in development mode.", "serve": "Start the built-in PHP web server and serve the application.", "test": "Run unit tests."}, "config": {"allow-plugins": {"laminas/laminas-component-installer": true, "php-http/discovery": true}}, "require-dev": {"squizlabs/php_codesniffer": "*", "larapack/dd": "1.*", "laminas/laminas-test": "4.11", "fakerphp/faker": "^1.23"}}